!function(e,t,n,i){var r=30,a=90,l=40,s=10,o=e.rad2deg=function(e){return e/(Math.PI/180)},c=(e.deg2rad=function(e){return e*(Math.PI/180)},navigator.platform.toLowerCase()),g=navigator.userAgent.toLowerCase(),d=(g.indexOf("iphone")>-1||g.indexOf("ipad")>-1||g.indexOf("ipod")>-1)&&(c.indexOf("iphone")>-1||c.indexOf("ipad")>-1||c.indexOf("ipod")>-1),u=e.Picker=function(e,t){var n=this;n.holder=e,n.options=t||{},n.init(),n.initInertiaParams(),n.calcElementItemPostion(!0),n.bindEvent()};u.prototype.findElementItems=function(){var e=this;return e.elementItems=[].slice.call(e.holder.querySelectorAll("li")),e.elementItems},u.prototype.init=function(){var e=this;e.list=e.holder.querySelector("ul"),e.findElementItems(),e.height=e.holder.offsetHeight,e.r=e.height/2-s,e.d=2*e.r,e.itemHeight=e.elementItems.length>0?e.elementItems[0].offsetHeight:l,e.itemAngle=parseInt(e.calcAngle(.8*e.itemHeight)),e.hightlightRange=e.itemAngle/2,e.visibleRange=a,e.beginAngle=0,e.beginExceed=e.beginAngle-r,e.list.angle=e.beginAngle,d&&(e.list.style.webkitTransformOrigin="center center "+e.r+"px")},u.prototype.calcElementItemPostion=function(e){var t=this;e&&(t.items=[]),t.elementItems.forEach(function(n){var i=t.elementItems.indexOf(n);if(t.endAngle=t.itemAngle*i,n.angle=t.endAngle,n.style.webkitTransformOrigin="center center -"+t.r+"px",n.style.webkitTransform="translateZ("+t.r+"px) rotateX("+-t.endAngle+"deg)",e){var r={};r.text=n.innerHTML||"",r.value=n.getAttribute("data-value")||r.text,t.items.push(r)}}),t.endExceed=t.endAngle+r,t.calcElementItemVisibility(t.beginAngle)},u.prototype.calcAngle=function(e){var t=this,n=b=parseFloat(t.r);e=Math.abs(e);var i=180*parseInt(e/t.d);e%=t.d;var r=(n*n+b*b-e*e)/(2*n*b),a=i+o(Math.acos(r));return a},u.prototype.calcElementItemVisibility=function(e){var t=this;t.elementItems.forEach(function(n){var i=Math.abs(n.angle-e);i<t.hightlightRange?n.classList.add("highlight"):i<t.visibleRange?(n.classList.add("visible"),n.classList.remove("highlight")):(n.classList.remove("highlight"),n.classList.remove("visible"))})},u.prototype.setAngle=function(e){var t=this;t.list.angle=e,t.list.style.webkitTransform="perspective(1000px) rotateY(0deg) rotateX("+e+"deg)",t.calcElementItemVisibility(e)},u.prototype.bindEvent=function(){var t=this,n=0,i=null,r=!1;t.holder.addEventListener(e.EVENT_START,function(e){r=!0,e.preventDefault(),t.list.style.webkitTransition="",i=(e.changedTouches?e.changedTouches[0]:e).pageY,n=t.list.angle,t.updateInertiaParams(e,!0)},!1),t.holder.addEventListener(e.EVENT_END,function(e){r=!1,e.preventDefault(),t.startInertiaScroll(e)},!1),t.holder.addEventListener(e.EVENT_CANCEL,function(e){r=!1,e.preventDefault(),t.startInertiaScroll(e)},!1),t.holder.addEventListener(e.EVENT_MOVE,function(e){if(r){e.preventDefault();var a=(e.changedTouches?e.changedTouches[0]:e).pageY,l=a-i,s=t.calcAngle(l),o=l>0?n-s:n+s;o>t.endExceed&&(o=t.endExceed),o<t.beginExceed&&(o=t.beginExceed),t.setAngle(o),t.updateInertiaParams(e)}},!1),t.list.addEventListener("tap",function(e){elementItem=e.target,"LI"==elementItem.tagName&&t.setSelectedIndex(t.elementItems.indexOf(elementItem),200)},!1)},u.prototype.initInertiaParams=function(){var e=this;e.lastMoveTime=0,e.lastMoveStart=0,e.stopInertiaMove=!1},u.prototype.updateInertiaParams=function(e,t){var n=this,i=e.changedTouches?e.changedTouches[0]:e;if(t)n.lastMoveStart=i.pageY,n.lastMoveTime=e.timeStamp||Date.now(),n.startAngle=n.list.angle;else{var r=e.timeStamp||Date.now();r-n.lastMoveTime>300&&(n.lastMoveTime=r,n.lastMoveStart=i.pageY)}n.stopInertiaMove=!0},u.prototype.startInertiaScroll=function(e){var t=this,n=e.changedTouches?e.changedTouches[0]:e,i=e.timeStamp||Date.now(),r=(n.pageY-t.lastMoveStart)/(i-t.lastMoveTime),a=r>0?-1:1,l=6e-4*a*-1,s=Math.abs(r/l),o=r*s/2,c=t.list.angle,g=t.calcAngle(o)*a,d=g;return c+g<t.beginExceed&&(g=t.beginExceed-c,s=s*(g/d)*.6),c+g>t.endExceed&&(g=t.endExceed-c,s=s*(g/d)*.6),0==g?void t.endScroll():void t.scrollDistAngle(i,c,g,s)},u.prototype.scrollDistAngle=function(e,t,n,i){var r=this;r.stopInertiaMove=!1,function(e,t,n,i){var a=13,l=i/a,s=0;!function o(){if(!r.stopInertiaMove){var e=r.quartEaseOut(s,t,n,l);return r.setAngle(e),s++,s>l-1||e<r.beginExceed||e>r.endExceed?void r.endScroll():void setTimeout(o,a)}}()}(e,t,n,i)},u.prototype.quartEaseOut=function(e,t,n,i){return-n*((e=e/i-1)*e*e*e-1)+t},u.prototype.endScroll=function(){var e=this;if(e.list.angle<e.beginAngle)e.list.style.webkitTransition="150ms ease-out",e.setAngle(e.beginAngle);else if(e.list.angle>e.endAngle)e.list.style.webkitTransition="150ms ease-out",e.setAngle(e.endAngle);else{var t=parseInt((e.list.angle/e.itemAngle).toFixed(0));e.list.style.webkitTransition="100ms ease-out",e.setAngle(e.itemAngle*t)}e.triggerChange()},u.prototype.triggerChange=function(t){var n=this;setTimeout(function(){var i=n.getSelectedIndex(),r=n.items[i];!e.trigger||i==n.lastIndex&&t!==!0||e.trigger(n.holder,"change",{index:i,item:r}),n.lastIndex=i,"function"==typeof t&&t()},0)},u.prototype.correctAngle=function(e){var t=this;return e<t.beginAngle?t.beginAngle:e>t.endAngle?t.endAngle:e},u.prototype.setItems=function(e){var t=this;t.items=e||[];var n=[];t.items.forEach(function(e){null!==e&&e!==i&&n.push("<li>"+(e.text||e)+"</li>")}),t.list.innerHTML=n.join(""),t.findElementItems(),t.calcElementItemPostion(),t.setAngle(t.correctAngle(t.list.angle)),t.triggerChange(!0)},u.prototype.getItems=function(){var e=this;return e.items},u.prototype.getSelectedIndex=function(){var e=this;return parseInt((e.list.angle/e.itemAngle).toFixed(0))},u.prototype.setSelectedIndex=function(e,t,n){var i=this;i.list.style.webkitTransition="";var r=i.correctAngle(i.itemAngle*e);if(t&&t>0){var a=r-i.list.angle;i.scrollDistAngle(Date.now(),i.list.angle,a,t)}else i.setAngle(r);i.triggerChange(n)},u.prototype.getSelectedItem=function(){var e=this;return e.items[e.getSelectedIndex()]},u.prototype.getSelectedValue=function(){var e=this;return(e.items[e.getSelectedIndex()]||{}).value},u.prototype.getSelectedText=function(){var e=this;return(e.items[e.getSelectedIndex()]||{}).text},u.prototype.setSelectedValue=function(e,t,n){var i=this;for(var r in i.items){var a=i.items[r];if(a.value==e)return void i.setSelectedIndex(r,t,n)}},e.fn&&(e.fn.picker=function(e){return this.each(function(t,n){if(!n.picker)if(e)n.picker=new u(n,e);else{var i=n.getAttribute("data-picker-options"),r=i?JSON.parse(i):{};n.picker=new u(n,r)}}),this[0]?this[0].picker:null},e.ready(function(){e(".mui-picker").picker()}))}(window.mui||window,window,document,void 0);