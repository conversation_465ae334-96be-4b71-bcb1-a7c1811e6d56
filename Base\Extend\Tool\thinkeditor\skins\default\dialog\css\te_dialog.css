/* 选项卡/标题 */
.te_dialog .seltab { position:relative; }
.te_dialog .seltab .bdb { height:30px; border-bottom:2px solid #CCC; }
.te_dialog .seltab .links { font-size:12px; line-height:24px; position:absolute; top:5px; padding-left:10px; }
.te_dialog .seltab .links a { display:inline-block; padding:0 8px; color:#999; border:1px solid #FFF; border-bottom:none; float:left; }
.te_dialog .seltab .links a:hover { color:#333; }
.te_dialog .seltab .links a.cstyle { color:#333; border-color:#CCC; border-bottom-color:#FFF; border-bottom:2px solid #FFF; }
/* 内容盒子 */
.te_dialog .te_centbox { }
/* 按钮区域 */
.te_dialog .btnarea { margin-top:10px; text-align:right; padding-right:10px; padding-bottom:10px; }
/* 颜色区块 */
.te_dialog .colorsel { width:276px; padding-left:5px; overflow:hidden; padding-top:5px; zoom:1; }
.te_dialog .colorsel a { zoom:1; width:16px; height:16px; float:left; margin-right:5px; margin-bottom:5px; overflow:hidden; display:inline-block; border:1px solid #333; }
.te_dialog .colorsel a:hover { width:20px; height:20px; margin:-2px; margin-left:-2px; margin-right:3px; margin-bottom:3px; }
/* 插入图片区域 */
.te_dialog_image { width:360px; border:1px solid #999; background-color:#FFF; font-size:12px; }
.te_dialog_image .item { margin-top:10px; }
.te_dialog_image .ltext { width:80px; text-align:right; display:inline-block; line-height:22px; }
.te_dialog_image .input1 { border:1px solid #AAA; padding:0 5px; width:220px; line-height:20px; height:20px; }
.te_dialog_image .input2 { width:40px; padding:0 5px; margin:0; border:1px solid #AAA; line-height:20px; height:20px; }
/* 粘贴为文本格式 */
.te_dialog_pasteText { width:360px; border:1px solid #999; background-color:#FFF; font-size:12px; }
.te_dialog_pasteText .tips { color:#666; line-height:22px; margin-left:10px; margin-right:10px; display:inline-block; }
.te_dialog_pasteText .tarea1 { width:336px; height:120px; margin-left:10px; border:1px solid #AAA; }
/* 插入flash */
.te_dialog_flash { width:360px; border:1px solid #999; background-color:#FFF; font-size:12px; }
.te_dialog_flash .item { margin-top:10px; }
.te_dialog_flash .ltext { width:80px; text-align:right; display:inline-block; line-height:22px; }
.te_dialog_flash .input1 { border:1px solid #AAA; padding:0 5px; width:220px; line-height:20px; height:20px; }
.te_dialog_flash .input2 { width:40px; padding:0 5px; margin:0; border:1px solid #AAA; line-height:20px; height:20px; }
.te_dialog_flash .input3 { vertical-align:-3px; margin:0; line-height:20px; }
.te_dialog_flash .labeltext { line-height:20px; }
/* 插入表格 */
.te_dialog_table { width:360px; border:1px solid #999; background-color:#FFF; font-size:12px; }
.te_dialog_table .insertTable { margin:10px; }
.te_dialog_table .item { margin-top:10px; }
.te_dialog_table .ltext { width:80px; text-align:right; display:inline-block; line-height:22px; }
.te_dialog_table .input1 { width:40px; padding:0 5px; height:20px; line-height:20px; border:1px solid #AAA; }
.te_dialog_table .input2 { width:170px; padding:0 5px; height:20px; line-height:20px; border:1px solid #AAA; }
/* 插入表情 */
.te_dialog_face { width:434px; border:1px solid #999; background-color:#FFF; font-size:12px; }
.te_dialog_face .insertFace { width:436px; height:203px; background-color:#FFF; background-position:-1px -1px; cursor:pointer; }
.te_dialog_face .insertFace span { float:left; width:29px; height:29px; display:block; }
/* 插入代码 */
.te_dialog_code { width:360px; border:1px solid #999; background-color:#FFF; font-size:12px; }
/* .te_dialog_code .tips { color:#666; line-height:22px; margin-left:10px; margin-right:10px; display:inline-block; } */
.te_dialog_code .tarea1 { width:336px; height:120px; margin-left:10px; border:1px solid #AAA; }

/* 选择标题样式 */
.te_dialog_style { border:1px solid #999; background-color:#FFF; padding:10px; height:180px; overflow-y:scroll; width:154px; }
.te_dialog_style a { border:1px solid #FFF; color:#333; margin:5px 0; padding:2px; display:inline-block; }
.te_dialog_style a:hover { border:1px dotted #59CB55; background-color:#ECFFEC; }
/* 选择字体样式 */
.te_dialog_font { border:1px solid #999; background-color:#FFF; padding:10px; height:180px; overflow-y:scroll; width:159px; }
.te_dialog_font .centbox a { color:#333; font-size:12px; line-height:16px; border:1px solid #FFF; padding:2px; display:inline-block; }
.te_dialog_font .centbox a:hover { border:1px dotted #59CB55; background-color:#ECFFEC; }
.te_dialog_font .centbox span { color:#999; }
/* 选择字号 */
.te_dialog_fontsize { border:1px solid #999; background-color:#FFF; padding:10px; height:180px; overflow-y:scroll; width:94px; }
.te_dialog_fontsize .centbox a { color:#333; font-size:12px; border:1px solid #FFF; padding:2px; display:inline-block; }
.te_dialog_fontsize .centbox a:hover { border:1px dotted #59CB55; background-color:#ECFFEC; }

.te_dialog_fontcolor { border:1px solid #999; background-color:#FFF; width:281px; }

.te_dialog_about { width:360px; border:1px solid #999; background-color:#FFF; }
.te_dialog_about .aboutcontent { color:#333; margin:10px; height:140px; font-size:12px; overflow-y:scroll; line-height:1.6em; }
/* 插入表格 */
.te_dialog_link { width:360px; border:1px solid #999; background-color:#FFF; font-size:12px; }
.te_dialog_link .item { margin-top:10px; }
.te_dialog_link .ltext { width:80px; text-align:right; display:inline-block; line-height:22px; }
.te_dialog_link .input1 { width:190px; padding:0 5px; height:20px; line-height:20px; border:1px solid #AAA; }
.te_dialog_link .centbox { margin:10px; }
.filebox {cursor: pointer;height: 22px;overflow: hidden;position: absolute;width: 70px;}
.filebox .upinput {border-width: 5px;cursor: pointer;left: -12px; margin: 0;opacity: 0.01;padding: 0;position: absolute;z-index: 3;}
.filebox .upbtn {border: 1px solid #CCCCCC;color: #666666;display: block;font-size: 12px;height: 20px;line-height: 20px;text-align: center;width: 68px;}