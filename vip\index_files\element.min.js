void function(a,b){var c=a.alogObjectName||"alog",d=a[c]=a[c]||function(){a[alogName].l=a[alogName].l||+new Date,(a[c].q=a[c].q||[]).push(arguments)};d("define","element",function(){function c(){l||(l={},"AdivBliCaDulEdlFddGspanHtableIbodyJtrKsectionLtdMolNpOarticlePdtQformRimgSh3TinputUasideViWbXthYemZfont".replace(/([A-Z])([a-z]+)/g,function(a,b,c){l[l[b]=c]=b}))}function d(c,e,f,g){if(!c||1!=c.nodeType)return"";var h;if(/^\/.*\/$/.test(e)){var i=new RegExp(e.replace(/^\/|\/$/g,"")).exec(c.className);h=i&&i[1]}else if(h="undefined"!=typeof c.getAttribute&&c.getAttribute&&c.getAttribute(e)||"","#"==h?h="[id]":"."==h&&(h="[class]"),h.replace(/\[([\w-_]+)\]/,function(a,b){h=c.getAttribute(b)}),!h&&m.css){var j=c.currentStyle||a.getComputedStyle&&b.defaultView.getComputedStyle(c,null);String(j&&j[m.css]).replace(/([\w-]+)--([\w-_#.]+)/g,function(a,b,c){b==e&&(h=c)})}return g&&(g.target=c),h||f&&d(c.parentNode,e,1,g)||""}function e(a,f,g){if(g&&c(),f=f||b.body,!a||a==f||/^body$/i.test(a.tagName))return"";if(1!=a.nodeType||/^html$/i.test(a.tagName))return a.tagName||"";for(var h=d(a,m.alias),i=1,j=a.previousSibling,k=a.nodeName.toLowerCase();j;)i+=j.nodeName==a.nodeName,j=j.previousSibling;return h=(g&&l[k]||k)+(2>i?"":i)+(h&&"("+h+")"),a.parentNode==f?h:e(a.parentNode,f,g)+(/^[A-Z]/.test(h)?"":"-")+h}function f(a,b){return e(a,b,1)}function g(a){if(!a||!a.getBoundingClientRect)return[0,0];var b=a.getBoundingClientRect();return[parseInt(b.right-b.left),parseInt(b.bottom-b.top)]}function h(a,b,c){function d(a,b){return String((~~(a/c)*c/b).toFixed(3)).replace(/^0\./g,".")}if(!a||!a.getBoundingClientRect)return["0","0"];c=c||7;var e=a.getBoundingClientRect(),f=g(a);return[d(b[0]-e.left,f[0]),d(b[1]-e.top,f[1])]}function i(){var c=g(b.documentElement),d=g(b.body);return[Math.max(c[0],d[0],a.innerWidth||0,b.documentElement.scrollWidth||0),Math.max(c[1],d[1],a.innerHeight||0,b.documentElement.scrollHeight||0)]}function j(){return[Math.max(b.documentElement.scrollLeft||0,b.body.scrollLeft||0,b.defaultView&&b.defaultView.pageXOffset||0),Math.max(b.documentElement.scrollTop||0,b.body.scrollTop||0,b.defaultView&&b.defaultView.pageYOffset||0),a.innerWidth||b.documentElement.clientWidth||b.body.clientWidth||0,a.innerHeight||b.documentElement.clientHeight||b.body.clientHeight||0]}function k(a,b){m[a]=b}var l,m={},n={};return n.ex=d,n.getPath=e,n.getXPath=f,n.ep=h,n.ps=i,n.vr=j,n.an=k,"Group1Action1Extra1AliasParamText".replace(/([A-Z][a-z]+)(1|0)?/g,function(a,b,c){var e=b.toLowerCase();m[e]="alog-"+e,n["get"+b]=function(a,b){return d(a,m[e],c,b)}}),m.css=null,n})}(window,document);