<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">

<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title><?php echo ($title); ?> - <?php
 $name = "cfg_sitetitle"; if(empty($name)){ echo ""; }else{ echo htmlspecialchars_decode(C($name)); } ?>  - 站长源码库（zzmaku.com） </title>
    <link href="__PUBLIC__/main/css/layui.css" rel="stylesheet" type="text/css">
    <link href="__PUBLIC__/main/css/public.css" rel="stylesheet" type="text/css">
    <script type="text/javascript" src="__PUBLIC__/main/js/jquery.min.js"></script>
    <script type="text/javascript" src="__PUBLIC__/main/js/global.js"></script>
    <script type="text/javascript" src="__PUBLIC__/My97DatePicker/WdatePicker.js"></script>
    <script type="text/javascript" src="__PUBLIC__/main/js/jquery.tab.js"></script>
    <script type="text/javascript" src="__PUBLIC__/main/js/echarts.min.js"></script>
    <script src="__PUBLIC__/layer/layer.js"></script>
</head>
<body class="layui-layout-body">
    <div class="layui-layout layui-layout-admin">

      <script src="__PUBLIC__/layer/layui.js"></script>
<div class="layui-header">
    <a href="<?php echo U(GROUP_NAME.'/Main/index');?>" class="layui-logo"><?php echo C('cfg_sitename');?></a>
    <!-- 头部区域（可配合layui已有的水平导航） -->
    <ul class="layui-nav layui-layout-left">
       <!--
       <li class="layui-nav-item">
            <a href="javascript:;">新建</a>
            <dl class="layui-nav-child layui-anim layui-anim-upbit">
                <dd> <a href="<?php echo U(GROUP_NAME.'/Article/add');?>">文章</a></dd>
                <dd><a href="<?php echo U(GROUP_NAME.'/Article/addcat');?>">文章分类</a></dd>
                <dd> <a href="<?php echo U(GROUP_NAME.'/Admin/add');?>">管理员</a></dd>
            </dl>
        </li>
        -->
        <li class="layui-nav-item"><a href="<?php
 $name = "cfg_siteurl"; if(empty($name)){ echo ""; }else{ echo htmlspecialchars_decode(C($name)); } ?>" target="_blank">查看站点</a></li>
        <li class="layui-nav-item"><a href="<?php echo U(GROUP_NAME.'/Main/clearcache');?>">清除缓存</a></li>

    </ul>
    <ul class="layui-nav layui-layout-right">
        <li class="layui-nav-item">
            <a href="javascript:;">
                您好，<?php echo session('admin_user');?>
            </a>
          
            <dl class="layui-nav-child layui-anim layui-anim-upbit">
                <dd> <a href="<?php echo U(GROUP_NAME.'/Admin/chagemypass');?>">修改密码</a></dd>
            </dl>
        </li>
        <li class="layui-nav-item"> <a href="<?php echo U(GROUP_NAME.'/Index/logout');?>">退出</a></li>
    </ul>
</div>


      <!-- dcHead 结束 -->
        <div class="layui-side layui-bg-black">
<div class="layui-side-scroll">
	<!-- 左侧导航区域（可配合layui已有的垂直导航） -->
	<ul class="layui-nav layui-nav-tree"  lay-filter="test">
		<li class="layui-nav-item"><a href="<?php echo U(GROUP_NAME.'/Main/index');?>">管理首页</a></li>
		<li class="layui-nav-item"><a href="<?php echo U(GROUP_NAME.'/System/index');?>">系统设置</a></li>
		<li class="layui-nav-item"><a href="<?php echo U(GROUP_NAME.'/Admin/index');?>">后台管理员</a></li>
		<li class="layui-nav-item"><a href="<?php echo U('User/index');?>">用户管理</a></li>
		<li class="layui-nav-item"><a href="<?php echo U(GROUP_NAME.'/Daikuan/index');?>">借款列表</a></li>
		<li class="layui-nav-item"><a href="<?php echo U('User/qianbao');?>">钱包管理</a></li>
		<li class="layui-nav-item"><a href="<?php echo U(GROUP_NAME.'/Daikuan/contract');?>">借款合同</a></li>
		<li class="layui-nav-item"><a href="<?php echo U('Tixian/index');?>">提现管理</a></li>
		<li class="layui-nav-item"><a href="<?php echo U(GROUP_NAME.'/Bills/index');?>">还款管理</a></li>	
		<li class="layui-nav-item"><a href="<?php echo U(GROUP_NAME.'/Duanxin/addsms');?>">短信状态</a></li>
		<li class="layui-nav-item"><a href="<?php echo U(GROUP_NAME.'/Block/index');?>">自 由 块</a></li>
	<!--	<li class="layui-nav-item"><a href="<?php echo U(GROUP_NAME.'/Article/catlist');?>">文章分类</a></li> !-->
		<li class="layui-nav-item"><a href="<?php echo U(GROUP_NAME.'/Article/index');?>">常见问题</a></li>
	</ul>
  </div>
</div>  
      <div class="layui-body">
        <!-- 内容主体区域 -->
        <blockquote class="layui-elem-quote layui-text">
           <?php echo ($title); ?>
          </blockquote>
        <div style="padding: 15px;">
<div class="layui-table-tool">
<!--<h3 class="layui-table-tool-self"><a href="<?php echo U(GROUP_NAME.'/Admin/add');?>" class="actionBtn layui-btn">添加管理员</a></h3>
-->
    
    <div class="filter">
    
        <form action="<?php echo U(GROUP_NAME.'/Admin/index');?>" method="post">
    
            <font class="seach_span">管理员名称:</font>
    
            <input name="username" type="text" class="inpMain" value="<?php echo ($seach_name); ?>" size="20" />
    
            <input name="submit" class="btnGray layui-btn " type="submit" value="筛选" />
    
        </form>
    
    </div>

</div>



<style>
    
    .seach_span{

        float: left;

        line-height: 38px;

        font-size: 16px;

    }

</style>



<table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableBasic">

    <tr>

        <th width="30" align="center">编号</th>

        <th align="left">登录名</th>

        <th align="center">添加时间</th>

        <th align="center">最后登录时间</th>

        <th align="center">状态</th>

        <th align="center">操作</th>

    </tr>

    <?php if(is_array($data)): foreach($data as $key=>$vo): ?><tr>

            <td align="center"><?php echo ($vo["id"]); ?></td>
            <td><?php echo ($vo["username"]); ?></td>
            <td align="center"><?php echo (date("Y-m-d H:i:s",$vo["addtime"])); ?></td>

            <td align="center"><?php echo (date("Y-m-d H:i:s",$vo["lastlogin"])); ?></td>

            <td align="center">

                <?php if($vo['status'] == 1): ?>正常

                    <?php else: ?>

                    禁止<?php endif; ?>

            </td>

            <td align="center">
                <?php if($vo["ischannel"] == 0): ?>无需操作
                    <?php else: ?>
                    <button class="layui-btn layui-btn-sm layui-btn-normal"><a href="<?php echo U(GROUP_NAME.'/Admin/edit',array('editid' => $vo['id']));?>"><i class="layui-icon"></i>编辑</a> </button>
                    <button class="layui-btn layui-btn-sm layui-btn-normal"> <a href="javascript:;" onclick="delAdmin('<?php echo ($vo["username"]); ?>','<?php echo U(GROUP_NAME.'/Admin/del',array('id' => $vo['id'] ));?>');"><i class="layui-icon"></i>删除</a></button><?php endif; ?>


            </td>

        </tr><?php endforeach; endif; ?>

</table>

<div class="pager"><?php echo ($page); ?></div>

<script>

    function delAdmin(username,jumpurl){

        layer.confirm(

            '确定要删除该渠道:'+username+'吗?',

            function (){

                window.location.href = jumpurl;

            }

        );

    }

</script></div>
      </div>
      
    
        <div class="layui-footer">
		 <div id="footer">
			  <ul>
            <h1><font color="red" size="3">唯一联系方式QQ【18108197】_TG【Kong_Money】</font></b></h1> 
			  </ul>
		 </div>
	</div>

      </div>
      <script>
      //JavaScript代码区域
      layui.use('element', function(){
        var element = layui.element;
      });
      </script>
      </body>
      
      <script>
      //JavaScript代码区域
      layui.use('element', function(){
        var element = layui.element;
        
      });
      </script>
</body>

</html>