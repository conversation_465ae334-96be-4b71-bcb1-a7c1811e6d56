define("hiloan:app/page/apply/userGuide/userGuide",function(t){"use strict";function e(t){return t&&t.__esModule?t:{"default":t}}var i=t("hiloan:node_modules/vue/dist/vue.common"),o=e(i),n=t("hiloan:node_modules/underscore/underscore"),a=e(n),s=t("hiloan:app/static/config/api"),l=e(s),c=t("hiloan:node_modules/query-string/index"),r=e(c),d=t("hiloan:app/static/config/env-conf"),p=e(d),u=t("hiloan:app/static/config/front-system-path"),h=e(u),f=t("hiloan:components/fin-fg/util/index"),m=t("hiloan:app/component/no-invite/index"),g=e(m),y=t("hiloan:app/component/app-util/buildEntireUrl"),v=(e(y),t("hiloan:app/component/app-util/index")),w=e(v),S=t("hiloan:components/fin-fg/track/log"),_=e(S),P=t("page/userGuide"),A=e(P),L=t("hiloan:components/fin-ui/ui-loading/index"),b=e(L),M=t("hiloan:components/fin-ui/ui-mask/index"),W=e(M),I=t("hiloan:app/component/app-util/openlogAutoSend"),x=e(I),C=r.default.parse(location.search).fr,T="/hiloan/apply/tplLoanRecord",N="/hiloan/index";
new o.default({el:"body",data:function(){return a.default.extend({},A.default,{showMask:!1,showPage:!1,scrollTop:0,hasLogin:window.G.constants.isLogin,inWallet:window.Agent.OS.wallet,urlParam:r.default.parse(location.search),joinWhiteFail:!1,switchMC:p.default.switchMC,offsetHeight:document.body.clientHeight,showBottomBtn:!1})
},computed:{isNoInvite:function(){return!window.G.constants.errno&&!+this.allowInWhite&&!+this.isInWhiteList},activity:function(){return this.activityName||this.urlParam.activityName}},methods:{gotoHelp:function(){f.redirect(p.default.FRONT_SYSTEM+h.default.helplist)
},getScript:function(t,e){var i=document.getElementsByTagName("head")[0],o=document.createElement("script");o.setAttribute("type","text/javascript"),o.setAttribute("src",t),i.appendChild(o);var n=function(){"function"==typeof e&&e()
};document.all?o.onreadystatechange=function(){("loaded"===o.readyState||"complete"===o.readyState)&&n()}:o.onload=function(){n()}},renderSmsLogin:function(t,e){var i=this;t=new e.passport.smsLogin({product:"fbuym",u:(window.location?window.location.protocol.toLowerCase():document.location.protocol.toLowerCase())+"//www.baidu.com",smsRegTip:0,overseas:1,is_voice_sms:1,adapter:3,noguide:1,smsLoginText:"我要申请",subpro:"jxjddmain",staticPage:window.location.protocol+"//"+window.location.host+"/static/hiloan/app/static/login/v3Jump.html"}),t.on("render",function(t){t.returnValue=!1;
encodeURIComponent(location.search.split("?u=")[1]);$("#PASSP__1__mobilenumLabel").hide(),$("#PASSP__1__mobilenum").attr("placeholder","请输入手机号"),$("#PASSP__1__password").attr("placeholder","请输入验证码"),$("#PASSP__1__smsCodeSend").before("<div class='send'>|</div>"),$("#PASSP__1__smsCodeSend").val("获取验证码"),$("#PASSP__1__msgWrapper").remove(),$("#PASSP__1__getVoiceCode").remove(),$(".pass-tip-smsCodeSend").remove(),$("#PASSP__1__submitWrapper").before('<p id="PASSP__1__msgWrapper" class="pass-msg-generalMsgWrapper" ></p>')
}),t.on("loginSuccess",function(t){t.returnValue=!1,i.showMask=!0,f.redirect(N,{idx:2})}),t.on("loginError",function(){i.showMask=!1}),t.render("smsLogin")},applyGuideInfo:function(){var t=this;return l.default.applyGuideInfo().then(function(e){t.isInWhiteList=e.data.isInWhiteList,t.creditStatus=e.data.creditStatus,t.isCanApply=e.data.isCanApply,t.activityName=e.data.activityName,t.allowInWhite=e.data.allowInWhite
})},handleAccess:function(){var t=this;this.hasLogin?(x.default.sendOpenLogAction("checkAmount"),this.isInWhiteList?this.applyMoney():(x.default.sendOpenLogAction("joinwhite"),setTimeout(function(){t.applyInWhite()
},100))):location.href="#entrySeam"},applyMoney:function(){this.inWallet?this.toApplyMoney():3===+this.creditStatus&&0===+this.isCanApply?f.redirect("/hiloan/apply/tplApplyResult",{fr:C}):this.toApplyMoney()
},toApplyMoney:function(){this.switchMC?this.toMc():setTimeout(function(){f.redirect(T)},100)},applyInWhite:function(){var t=this;l.default.joinwhite({activityName:this.activity,fr:C}).then(function(e){var i=e.data;
1===i.activityStatus&&1===i.status||3===i.status?t.toApplyMoney():[0,2,3].indexOf(+i.activityStatus)>-1?(t.showPage=!0,t.joinWhiteFail=!0):f.redirect("/hiloan/apply/tplJoinWhiteResult",{fr:C,failStatus:i.failStatus})
})},toMc:function(){var t=location.href,e=location.protocol+"//"+location.host+T+location.search;f.redirect(p.default.FRONT_SYSTEM+h.default.finAccount,{switchUrl:t,nextUrl:e})},initEvents:function(){document.addEventListener("scroll",this.handleScroll,!1)
},handleScroll:function(t){if(t.preventDefault(),t.stopPropagation(),this.scrollTop=document.body.scrollTop,this.scrollTop){var e=this.scrollTop/this.offsetHeight;this.showBottomBtn=this.hasLogin?e>=.6:e>=.8
}else this.showBottomBtn=!1}},components:{noInvite:g.default,uiLoading:b.default,uiMask:W.default},created:function(){var t=this;return w.default.isFromBack()?void location.reload():(_.default.sendBfbPage(this.isNoInvite?["0"]:["1"]),G.constants.autoSendBfbLog=!0,void(this.hasLogin?2===+this.urlParam.idx?this.isNoInvite?this.showPage=!0:this.handleAccess():this.showPage=!0:(this.showPage=!0,this.getScript("https://wappass.baidu.com/static/touch/js/api/wrapper.js?cdnversion="+(new Date).getTime(),function(){var e=void 0;
passport.use("smsLogin",{library:!0,defaultCss:!1},function(i){t.renderSmsLogin(e,i)})}))))},ready:function(){this.initEvents()}})});