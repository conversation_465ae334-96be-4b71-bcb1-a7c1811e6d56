#tpm_backdrop{
  opacity: 0;
  filter: alpha(opacity=0);
  transition:opacity 0.3s;
  position:fixed;
  top:0;
  left:0;
  right:0;
  bottom:0;
  background:#000;
  display:none;
  z-index:3000;
}

#tpm_backdrop.in{
    display:block;
    opacity:0.8;
    filter: alpha(opacity=80);
}

#tpm_modal{
    top:-50%;
    position:fixed;
    left:50%;
    width:560px;
    margin-left:-280px;
    transition:top 0.3s;
    z-index:3001;
    background:#fff;
    border-radius:6px;
    display:none;
}

#tpm_modal.in{
    top:10%;
    display:block;
}
#tpm_modal .tpm_modal_head{
    padding:9px 15px;
    border-bottom: 1px solid #EEEEEE;
    position:relative;
}
#tpm_modal .tpm_modal_head .tpm_modal_close{
    position:absolute; 
    right:10px;
    top:10px;
    text-decoration:none;
    font-size: 16px;
    font-weight: bold;
    line-height: 20px;
    color: #000000;
    text-shadow: 0 1px 0 #ffffff;
    opacity: 0.2;
    filter: alpha(opacity=20);
}

#tpm_modal .tpm_modal_head h3{ 
    margin:0px;
    padding:0px;
    font-size:18px;
    color:#333;
}
#tpm_modal .tpm_modal_body{ 
    padding:15px;
}
#tpm_modal .tpm_modal_foot{ 
    background:#F5F5F5; 
    border-radius:0 0 6px 6px;
    border-top:1px solid #DDDDDD;
    box-shadow:0 1px 0 #FFFFFF inset;
    padding:14px 15px 15px;
    text-align:right;
}
.tpm_modal_foot button{
    border-radius:2px;
    background:#0078E7;
    color:#fff;
    border:none;
    padding:0.5em 1.5em;
}
.tpm_modal_foot button:hover{
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#00000000', endColorstr='#00000000', GradientType=0);
    background-image: -webkit-gradient(linear, 0 0, 0 100%, from(transparent), color-stop(40%, rgba(0,0,0, 0.05)), to(rgba(0,0,0, 0.05)));
    background-image: -webkit-linear-gradient(transparent, rgba(0,0,0, 0.05) 40%, rgba(0,0,0, 0.15));
    background-image: -moz-linear-gradient(top, rgba(0,0,0, 0.05) 0%, rgba(0,0,0, 0.05));
    background-image: -ms-linear-gradient(transparent, rgba(0,0,0, 0.05) 40%, rgba(0,0,0, 0.15));
    background-image: -o-linear-gradient(transparent, rgba(0,0,0, 0.05) 40%, rgba(0,0,0, 0.05));
    background-image: linear-gradient(transparent, rgba(0,0,0, 0.05) 40%, rgba(0,0,0, 0.05));
}
.tpm_modal_foot button.tpm_modal_cancel{
    color:#444;
    background-color:#E6E6E6;
    margin-right:15px;
}

#tpm_modal_phone{
    bottom:-50%;
    position:fixed;
    width:100%;
    transition:bottom 0.3s;
    z-index:3001;
    background:#eee;
    display:none;
}

#tpm_modal_phone.in{
    bottom:0;
    display:block;
}

#tpm_modal_phone .tpm_modal_head{
    padding:9px 10px;
}
#tpm_modal_phone .tpm_modal_head h3{ 
    margin:0px;
    padding:0px;
    font-size:16px;
    color:#333;
}

#tpm_modal_phone .tpm_modal_body{ 
    padding:10px 20px 20px 20px;
}

#tpm_modal_phone .tpm_modal_foot{ 
    border-top:1px solid #DDDDDD;
    box-shadow:0 1px 0 #FFFFFF inset;
    padding:14px 10px 10px;
    text-align:right;
}

#tpm_modal_iframe{
 margin:0px;
 padding:0px;
 border:none;
 overflow:auto;
 width:100%;
 height:350px;
}

#tpm_info{
    position:fixed;
    top:-30%;
    left:50%;
    width:200px;
    margin-left:-100px;
    text-align:center;
    opacity:0;
    filter: alpha(opacity=0);
    border-radius:6px;
    background:#5FC6DA;
    color:#fff;
    padding:10px;
    display:none;
    z-index:3001;
    transition:0.3s;
}
#tpm_info.in{
    top:10%;
    opacity:1;
    filter: alpha(opacity=100);
}

#tpm_info_phone{
    position:fixed;
    bottom:-30%;
    opacity:0;
    filter:alpha(opacity=0);
    width:100%;
    background:#5FC6DA;
    color:#fff;
    padding:5px;
    text-align:center;
    transition:0.3s;
    z-index:3001;
    display:none;
}
#tpm_info_phone.in{
    bottom:0;
    opacity:0.8;
    filter:alpha(opacity=80);
}


@media (max-width:767px) {
#tpm_modal{
    left:2%;
    right:2%;
    width:auto;
    margin-left:0px;
}

#tpm_modal.in{
    top:5%;
}

#tpm_modal_iframe{
    height:250px;
}

}
