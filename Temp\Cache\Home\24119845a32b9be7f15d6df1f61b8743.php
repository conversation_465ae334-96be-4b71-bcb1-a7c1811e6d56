<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html>
<html lang="en" class="no-js">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="description" content="">
	<meta name="keywords" content="">
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/amazeui.min.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/app.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/all.css">
	<title>身份信息 - 站长源码库（zzmaku.com） </title>
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/common.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/myinfo.css">
<style>
#sfz_zm_div,#sfz_fm_div,#sfz_sc_div{
	height: 100%;
}
#sfz_zm_div img,#sfz_fm_div img,#sfz_sc_div img{
	height: 100%;
	width: 100%;
}
</style>
</head>

<body>

	<div class="comm_top_nav" data-am-sticky="">
		<div class="am-g">
			<b>
				<div class="am-u-sm-2" onclick="javascript:window.location.replace(document.referrer);"><i
						class="am-icon-angle-left am-icon-fw"></i></div>
				<div class="am-u-sm-8">身份信息</div>
				<div class="am-u-sm-2"></div>
			</b>
		</div>

	</div>

	<div class="identity">
		<form action="" enctype="multipart/form-data">
			<div class="input_text_group">
				<div class="input_text_list">
					<div class="am-g">
						<div class="am-u-sm-4">真实姓名</div>
						<div class="am-u-sm-8">
							<input type="text" id="fullname" value="<?php echo ($userinfo["name"]); ?>" placeholder="请输入真实姓名">
						</div>
					</div>
				</div>
				<div class="input_text_list">
					<div class="am-g">
						<div class="am-u-sm-4">身份证号</div>
						<div class="am-u-sm-8 f_number">
							<input type="text" id="sfzh" value="<?php echo ($userinfo["usercard"]); ?>" placeholder=" 请输入身份证号">
						</div>
					</div>
				</div>
			</div>

			<div class="upload_group">
				<div class="am-g">
					<div class="am-u-sm-12">
						<b>上传身份证照片</b>
					</div>
				</div>
				<div class="am-g">
					<div class="am-u-sm-6">
						<div class="upload_box">
							<div id="sfz_zm_div"></div>
							<input type="hidden" id="sfz_zm" />
							<div style="display:none;">
								<input type="file" id="sfz_zm_input"
									onchange="uploadImg('sfz_zm','sfz_zm_div',this);" />
							</div>
							<div class="upload_box_icon" onclick="Selfile('sfz_zm_input');">
								<i class="am-icon-camera am-icon-fw"></i>
							</div>
							<span class="up_txt">上传身份证正面</span>
						</div>
					</div>
					<div class="am-u-sm-6">
						<div class="upload_box">
							<div id="sfz_fm_div"></div>
							<input type="hidden" id="sfz_fm" />
							<div style="display:none;">
								<input type="file" id="sfz_fm_input"
									onchange="uploadImg('sfz_fm','sfz_fm_div',this);" />
							</div>
							<div class="upload_box_icon" onclick="Selfile('sfz_fm_input');">
								<i class="am-icon-camera am-icon-fw"></i>
							</div>
							<span class="up_txt">上传身份证反面</span>
						</div>
					</div>
				</div>
				<div class="am-g">
					<div class="am-u-sm-6">
						<div class="upload_box">
							<div id="sfz_sc_div"></div>
							<input type="hidden" id="sfz_sc" />
							<div style="display:none;">
								<input type="file" id="sfz_sc_input"
									onchange="uploadImg('sfz_sc','sfz_sc_div',this);" />
							</div>
							<div class="upload_box_icon" onclick="Selfile('sfz_sc_input');">
								<i class="am-icon-camera am-icon-fw"></i>
							</div>
							<span class="up_txt">上传手持身份证</span>
						</div>
					</div>
				</div>
			</div>

			<div class="standard">
				<div class="am-g">
					<div class="am-u-sm-12">
						<b>上传标准：须本人身份证且清晰可辨,确认拍照权限已开启,用手机相机拍照以后上传,如若上传无反应请稍等片刻即可或重新退出APP重试</b>
					</div>
				</div>
				<div class="am-g">
					<div class="am-u-sm-12" style="margin: 15px 0 0;">
						<img src="__PUBLIC__/home/<USER>/image/standard.png" class="am-img-responsive" alt="">
					</div>
				</div>
			</div>

			<div class="fix_bottom">
				<div class="am-g">
					<button type="button" class="am-btn am-btn-block" id="identity-button" onclick="saveInfo();">
						确认提交
					</button>
				</div>
			</div>
		</form>
	</div>

	<div class="message">
		<p></p>
	</div>


	<script type="text/javascript">
		document.documentElement.addEventListener('touchmove', function (event) {
			if (event.touches.length > 1) {
				event.preventDefault();
			}
		}, false);
	</script>



	<!--[if (gte IE 9)|!(IE)]><!-->
	<script src="__PUBLIC__/home/<USER>/js/jquery3.2.min.js"></script>
	<!--<![endif]-->
	<script src="__PUBLIC__/home/<USER>/js/amazeui.min.js"></script>

	<script type="text/javascript">
		var isupload = false;
		//判断如果已经上传了图片就显示
		var sfz_zm = "<?php echo ($userinfo["cardphoto_1"]); ?>";
		var sfz_fm = "<?php echo ($userinfo["cardphoto_2"]); ?>";
		var sfz_sc = "<?php echo ($userinfo["cardphoto_3"]); ?>";
		if (sfz_zm != '') {
			$("#sfz_zm").val(sfz_zm);
			$("#sfz_zm_div").html('<img src="' + sfz_zm + '">');
		}
		if (sfz_fm != '') {
			$("#sfz_fm").val(sfz_fm);
			$("#sfz_fm_div").html('<img src="' + sfz_fm + '">');
		}
		if (sfz_sc != '') {
			$("#sfz_sc").val(sfz_sc);
			$("#sfz_sc_div").html('<img src="' + sfz_sc + '">');
		}

		$('#sel').change(function () {
			change('sel', 'sela')
		});
		$('.inputblur').click(function () {
			$(this).blur();
			$('.nofocus').blur();
		});

		// 弹窗

		// 倒计时
		function myTimer() {
			var sec = 3;
			var timer;
			clearInterval(timer);
			timer = setInterval(function () {
				console.log(sec--);
				if (sec == 1) {
					$(".message").addClass("m-hide");
					$(".message").removeClass("m-show");
				}
				if (sec == 0) {
					$(".message").hide();
					$(".message").removeClass("m-hide");
					clearInterval(timer);
				}
			}, 1000);
		}

		// 弹窗内容
		function message(data) {
			msg = $(".message p").html(data);
			$(".message").addClass("m-show");
			$(".message").show();

			myTimer();

		}

		// 初始化弹窗
		function mesg_default() {
			msg = '';
			$(".message").hide();
			$(".message").removeClass("m-show");
			$(".message").removeClass("m-hide");
		}

		function Selfile(inputid) {
			if (isupload != false) {
				message("其他文件正在上传...请稍后");
			} else {
				$("#" + inputid).click();
			}
		}
		function uploadImg(hiddenid, divid, obj) {
			var filename = $(obj).val();
			if (filename != '' && filename != null) {
				isupload = true;
				var pic = $(obj)[0].files[0];
				var fd = new FormData();
				fd.append('imgFile', pic);
				$.ajax({
					url: "__PUBLIC__/main/js/kindeditor/php/upload_json.php",
					type: "post",
					dataType: 'json',
					data: fd,
					cache: false,
					contentType: false,
					processData: false,
					success: function (data) {
						if (data && data.error == '0') {
							message("上传成功");
							var imgurl = data.url;
							$("#" + divid).html('<img src="' + imgurl + '">');
							$("#" + hiddenid).val(imgurl);
						} else {
							message("上传出错了...");
						}
					},
					error: function () {
						message("上传出错了...");
					}
				});
				isupload = false;
			}
			isupload = false;
		}

		function checkval(val_) {
			if (val_ == '' || val_ == null) {
				return false;
			} else {
				return true;
			}
		}
		
		// 身份证
		function checksfz(val_) {
			var sfz_zz = /^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$|^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/;
			if (sfz_zz.test(val_)) {
				return true;
			} else {
				return false;
			}
		}
		
		// 姓名正则
		function checkname(val_) {
			var name_zz = /^[\u4E00-\u9FA5]{2,6}$/;
			if (name_zz.test(val_)) {
				return true;
			} else {
				return false;
			}
		}

		//保存资料
		function saveInfo() {
			var name = $("#fullname").val();
			var card = $("#sfzh").val();
			var cardphoto_1 = $("#sfz_zm").val();
			var cardphoto_2 = $("#sfz_fm").val();
			var cardphoto_3 = $("#sfz_sc").val();
			if (checkval(name) && checkval(card) && checkval(cardphoto_1) && checkval(cardphoto_2) && checkval(cardphoto_3) && checksfz(card) && checkname(name)) {
				$.post(
					"<?php echo U('Info/baseinfo');?>",
					{
						name: name,
						usercard: card,
						cardphoto_1: cardphoto_1,
						cardphoto_2: cardphoto_2,
						cardphoto_3: cardphoto_3
					},
					function (data, state) {
						if (state != "success") {
							message("请求数据失败,请重试!");
						} else if (data.status == 1) {
							message("保存成功!");
							window.location.href = "<?php echo U('Info/unitinfo');?>";
						} else {
							message(data.msg);
						}
					}
				);
			} else {
				message("资料填写不完整,请检查!");
			}
		}

	</script>


</body>

</html>