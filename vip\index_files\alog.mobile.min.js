void function(a,b){function c(a){var c=B.get("alias")||{},d=c[a]||a+".js";if(!w[d]){w[d]=!0;var e="script",f=b.createElement(e),g=b.getElementsByTagName(e)[0];f.async=!0,f.src=d,g.parentNode.insertBefore(f,g)}}function d(a){if(!a.defined){for(var b=!0,d=[],f=a.requires,g=0;f&&g<f.length;g++){var h=f[g],i=A[h]=A[h]||{};i.defined||i==a?d.push(i.instance):(b=!1,i.defining||c(h),i.waiting=i.waiting||{},i.waiting[a.name]=a)}b&&(a.defined=!0,a.creator&&(a.instance=a.creator.apply(a,d)),e(a))}}function e(a){for(var b in a.waiting)d(a.waiting[b])}function f(a){return(a||new Date)-t}function g(a,b,c){if(a){"string"==typeof a&&(c=b,b=a,a=x);try{if(a==x)return y[b]=y[b]||[],void y[b].unshift(c);a.addEventListener?a.addEventListener(b,c,!1):a.attachEvent&&a.attachEvent("on"+b,c)}catch(d){}}}function h(a,b,c){if(a){"string"==typeof a&&(c=b,b=a,a=x);try{if(a==x){var d=y[b];if(!d)return;for(var e=d.length;e--;)d[e]===c&&d.splice(e,1);return}a.removeEventListener?a.removeEventListener(b,c,!1):a.detachEvent&&a.detachEvent("on"+b,c)}catch(f){}}}function i(a){var b=y[a],c=0;if(b){for(var d=[],e=arguments,f=1;f<e.length;f++)d.push(e[f]);for(var f=b.length;f--;)b[f].apply(this,d)&&c++;return c}}function j(a,b){if(a&&b){var c=new Image(1,1),d=[],e="img_"+ +new Date;for(var f in b)b[f]&&d.push(f+"="+encodeURIComponent(b[f]));x[e]=c,c.onload=c.onerror=function(){x[e]=c=c.onload=c.onerror=null,delete x[e]},c.src=a+"?"+d.join("&")}}function k(a,b){if(!a)return b;var c={};for(var d in b)null!==a[d]&&(c[a[d]||d]=b[d]);return c}function l(){var a=arguments,b=a[0];if(this.created||/^(on|un|set|get|create)$/.test(b)){for(var c=n.prototype[b],d=[],e=1,f=a.length;f>e;e++)d.push(a[e]);"function"==typeof c&&c.apply(this,d)}else this.argsList.push(a)}function m(a,b){var c={};for(var d in a)a.hasOwnProperty(d)&&(c[d]=a[d]);for(var d in b)b.hasOwnProperty(d)&&(c[d]=b[d]);return c}function n(a){this.name=a,this.fields={protocolParameter:{postUrl:null,protocolParameter:null}},this.argsList=[],this.alog=x}function o(a){var b;if(a=a||"default","*"==a){b=[];for(var c in z)b.push(z[c]);return b}var d=z[a];return d||(d=z[a]=new n(a)),d}var p=a.alogObjectName||"alog",q=a[p];if(!q||!q.defined){var r,s=b.all&&a.attachEvent,t=q&&q.l||+new Date,u=a.logId||(+new Date).toString(36)+Math.random().toString(36).substr(2,3),v=0,w={},x=function(a){var b,c,e,f,g=arguments;if("define"==a||"require"==a){for(var h=1;h<g.length;h++)switch(typeof g[h]){case"string":b=g[h];break;case"object":e=g[h];break;case"function":f=g[h]}return"require"==a&&(b&&!e&&(e=[b]),b=null),b=b?b:"#"+v++,c=A[b]=A[b]||{},void(c.defined||(c.name=b,c.requires=e,c.creator=f,"define"==a&&(c.defining=!0),d(c)))}return"function"==typeof a?void a(x):void String(a).replace(/^(?:([\w$_]+)\.)?(\w+)$/,function(a,b,c){g[0]=c,l.apply(x.tracker(b),g)})},y={},z={},A={alog:{name:"alog",defined:!0,instance:x}};n.prototype.start=n.prototype.create=function(a){if(!this.created){"object"==typeof a&&this.set(a),this.created=new Date,this.fire("create",this);for(var b;b=this.argsList.shift();)l.apply(this,b)}},n.prototype.send=function(a,b){var c=m({ts:f().toString(36),t:a,sid:u},this.fields);if("object"==typeof b)c=m(c,b);else{var d=arguments;switch(a){case"pageview":d[1]&&(c.page=d[1]),d[2]&&(c.title=d[2]);break;case"event":d[1]&&(c.eventCategory=d[1]),d[2]&&(c.eventAction=d[2]),d[3]&&(c.eventLabel=d[3]),d[4]&&(c.eventValue=d[4]);break;case"timing":d[1]&&(c.timingCategory=d[1]),d[2]&&(c.timingVar=d[2]),d[3]&&(c.timingValue=d[3]),d[4]&&(c.timingLabel=d[4]);break;case"exception":d[1]&&(c.exDescription=d[1]),d[2]&&(c.exFatal=d[2]);break;default:return}}this.fire("send",c),j(this.fields.postUrl,k(this.fields.protocolParameter,c))},n.prototype.set=function(a,b){if("string"==typeof a)"protocolParameter"==a&&(b=m({postUrl:null,protocolParameter:null},b)),this.fields[a]=b;else if("object"==typeof a)for(var c in a)this.set(c,a[c])},n.prototype.get=function(a,b){var c=this.fields[a];return"function"==typeof b&&b(c),c},n.prototype.fire=function(a,b){return x.fire(this.name+"."+a,b)},n.prototype.on=function(a,b){x.on(this.name+"."+a,b)},n.prototype.un=function(a,b){x.un(this.name+"."+a,b)},x.name="alog",x.sid=u,x.defined=!0,x.timestamp=f,x.un=h,x.on=g,x.fire=i,x.tracker=o,x("init");var B=o();if(B.set("protocolParameter",{alias:null}),q){var C=[].concat(q.p||[],q.q||[]);q.p=q.q=null;for(var D in x)x.hasOwnProperty(D)&&(q[D]=x[D]);x.p=x.q={push:function(a){x.apply(x,a)}};for(var E=0;E<C.length;E++)x.apply(x,C[E])}a[p]=x,s&&g(b,"mouseup",function(a){var b=a.target||a.srcElement;1==b.nodeType&&/^ajavascript:/i.test(b.tagName+b.href)&&(r=new Date)})}}(window,document);