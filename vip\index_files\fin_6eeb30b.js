;define("hiloan:components/fin-cs/credit-report/credit-report.vue",function(n,s,t){"use strict";function a(n){return n&&n.__esModule?n:{"default":n}}Object.defineProperty(s,"__esModule",{value:!0});{var e=n("hiloan:app/static/config/api");
a(e)}s.default={props:{showInfo:{type:Boolean,"default":!0},title:{type:String,"default":"个人征信授权书"},name:{type:String,"default":""},type:{type:String,"default":""},idcard:{type:String,"default":"身份证"},time:{type:String,"default":""},place:{type:String,"default":"北京市海淀区"},spouseName:{type:String,"default":""},spousePrcid:{type:String,"default":""},protocol:{type:<PERSON><PERSON><PERSON>,"default":!1},ptype:{type:String,"default":""},tplinfo:{type:String,"default":""}},data:function(){return{is:"credit-report",ready:!1}
},methods:{},components:{}};var l='<div class="fin-{{is}}">\n    <article class="mainContainer">\n    \n        <template v-if="!protocol">\n\n            <section class="topTitle">{{title}}</section>\n\n            <section class="topT">\n                <p class="bold">重要提示:</p>\n                <p class="bold">\n                    为了保障您的合法权益，您应当阅读并遵守本授权书，请您务必审慎阅读、充分理解本授权书条款内容，特别是免除重庆百度小额贷款有限公司及其合作的贷款机构（以下统称为“被授权人”或“贷款人”）责任，或限制您权利的条款，其中免除或者减轻责任条款可能以加粗形式提示您注意。除非您已阅读并接受本授权书所有条款，否则您无权使用重庆百度小额贷款有限公司及其合作的贷款机构提供的服务。您的使用、登录等行为即视为您已阅读并同意本授权书条款的约束。\n\n                </p>\n            </section>\n\n            <section class="topT">\n                <p class="gray">重庆百度小额贷款有限公司及其合作的贷款机构：</p>\n                <p class="gray">一、本人同意并不可撤销地授权：<span class="bold black">贷款人按照国家相关规定采集符合法律法规规定的本人个人信息及包括信贷信息在内的信用信息（含本人在贷款人处办理业务时产生的不良信息），并可以向中国人民银行金融信用信息基础数据库、互联网金融协会及其他依法设立的征信机构提供以上信息。</span>\n                </p>\n            </section>\n\n            <section class="topT">\n                <p class="gray">二、本人同意并不可撤销地授权：<span class="bold black">贷款人可以根据国家有关规定，通过中国人民银行金融信用信息基础数据库、其他依法设立的征信机构、公安部公民身份信息数据库、互联网金融协会查询、打印、保存符合法律法规规定的本人个人信息和包括信贷信息在内的信用信息。</span>\n                </p>\n            </section>\n\n\n            <section class="topT">\n                <ul class="gray">\n                    <li>用途如下:</li>\n                    <li>（一）审核本人贷款申请；</li>\n                    <li>（二）处理贷后管理事务；</li>\n                    <li>（三）处理本人征信异议；</li>\n                    <li>（四）依法或经有权部门要求；</li>\n                    <li>（五）其他本人向我司申请或办理的业务。</li>\n                </ul>\n            </section>\n\n            <section class="topT">\n                <p class="bold">授权人声明：\n                    本授权书是本人向重庆百度小额贷款有限公司及其合作的贷款机构做出的单方承诺，效力具有独立性，不因任何协议、条款无效而失效。\n                </p>\n            </section>\n\n            <section class="topT">\n                <p class="gray">以上授权期限为本人作出本授权承诺之日起至本人在贷款人处所有业务终结之日止。<span class="bold black">本人在贷款人处有借款额度但无借款余额的情况下，本人与贷款人的业务关系仍然存续，贷款人仍有权向中国人民银行金融信用信息基础数据库查询、上报本人的信用信息。</span>\n                </p>\n            </section>\n\n            <section class="topT">\n                <p class="bold">\n                    若本人与被授权人发生任何纠纷或争议，首先应友好协商解决；协商不成的，本人同意将纠纷或争议提交本授权书签订地（即中国北京市海淀区）有管辖权的人民法院管辖。本授权书的成立、生效、履行、解释及纠纷解决，适用中华人民共和国大陆地区法律（不包括冲突法）。\n                </p>\n            </section>\n\n            <section class="topT">\n                <p class="bold">\n                    本人已知悉本授权书所有内容<span class="red bold">（特别是加粗字体内容）</span>的意义以及由此产生的法律效力，自愿作出上述授权，本授权书是本人真实的意思表示，本人同意承担由此带来的一切法律后果。\n                </p>\n                <p>\n                    特此授权！\n                </p>\n            </section>\n\n            <section class="topT right" v-if="showInfo">\n                <p class="bold">\n                    授权人姓名:<span class="gray"> {{name || \'-\'}} </span>\n                </p>\n                <p class="bold">\n                    证件类型:<span class="gray"> {{type || \'-\'}} </span>\n                </p>\n                <p class="bold">\n                    证件号码:<span class="gray"> {{idcard || \'-\'}} </span>\n                </p>\n                <p v-if="spouseName" class="bold">\n                    配偶姓名:<span class="gray"> {{spouseName || \'-\'}} </span>\n                </p>\n                <p v-if="spousePrcid" class="bold">\n                    配偶身份证号:<span class="gray"> {{spousePrcid || \'-\'}} </span>\n                </p>\n                <p class="bold">\n                    合同签署日期:<span class="gray"> {{time || \'-\'}} </span>\n                </p>\n                <p class="bold">\n                    合同签订地:<span class="gray"> {{place || \'-\'}} </span>\n                </p>\n            </section>\n\n        </template>\n\n        <template v-if="protocol">\n            <section class="topT" v-html="tplinfo"></section>\n        </template>\n\n    </article>\n</div>';
t&&t.exports&&(t.exports.template=l),s&&s.default&&(s.default.template=l),t.exports=s["default"]});
;define("hiloan:components/fin-cs/credit-report/index",function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var o=e("hiloan:components/fin-cs/credit-report/credit-report.vue"),i=r(o);
t.default=i.default,n.exports=t["default"]});
;define("hiloan:components/fin-fg/util/request/util/serialize",function(e,n,r){"use strict";function o(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0}),n.default=function(e){if(!u.default.isObject(e))throw new Error("[error] 数据不是`Object`类型");
var n=[];for(var r in e)e.hasOwnProperty(r)&&i(n,r,e[r]);return n.join("&")};var t=e("hiloan:node_modules/underscore/underscore"),u=o(t),i=function f(e,n,r){if(null!=r)if(Array.isArray(r))r.forEach(function(r){f(e,n,r)
});else if(u.default.isObject(r))for(var o in r)r.hasOwnProperty(o)&&f(e,n+"["+o+"]",r[o]);else e.push(""+encodeURIComponent(n)+"="+encodeURIComponent(r));else null===r&&e.push(encodeURIComponent(n))};
r.exports=n["default"]});
;define("hiloan:components/fin-ui/ui-mask/ui-mask.vue",function(e,t,o){"use strict";function u(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var n=e("hiloan:node_modules/vue/dist/vue.common"),a=u(n),i=e("hiloan:node_modules/vue-touch/vue-touch"),s=u(i);
a.default.use(s.default),t.default={props:{show:{type:Boolean,"default":!1},opacity:{type:Number,"default":.7}},computed:{getBackground:function(){return"background-color: rgba(0, 0, 0, "+this.opacity+")"
}},methods:{preventTouchMove:function(e){e.preventDefault()},onTap:function(){this.$emit("tap",this)}}};var d='<div class="fin-ui-mask" :style="getBackground" v-show="show" transition="fade" v-touch:tap.stop="onTap" @touchmove="preventTouchMove($event)">\n</div>';
o&&o.exports&&(o.exports.template=d),t&&t.default&&(t.default.template=d),o.exports=t["default"]});
;define("hiloan:components/fin-ui/ui-mask/index",function(e,u,n){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(u,"__esModule",{value:!0});var t=e("hiloan:components/fin-ui/ui-mask/ui-mask.vue"),o=i(t);
u.default=o.default,n.exports=u["default"]});
;define("hiloan:components/fin-ui/ui-dialog/ui-dialog.vue",function(t,e,i){"use strict";function n(t){return t&&t.__esModule?t:{"default":t}}Object.defineProperty(e,"__esModule",{value:!0});var o=t("hiloan:components/fin-ui/ui-mask/index"),a=n(o),s="cancel",l=/^(?:confirm|dialog|alert|prompt)$/g,c=/^(?:lightbox|dialog)$/g,d=37.5;
e.default={props:{type:{type:String,"default":"confirm"},title:{type:String,"default":"标题"},content:{type:String,"default":"<div>hello world!</div>"},ok:{type:String,"default":"确定"},cancel:{type:String,"default":"取消"},animate:{type:String,"default":"fade"},close:{type:Boolean,"default":!1},show:{type:Boolean,"default":!1,twoWay:!0},mask:{type:Boolean,"default":!0},head:{type:Boolean,"default":!0},foot:{type:Boolean,"default":!0},top:{type:Number,"default":0},width:{type:Number,"default":300},prefix:{type:String,"default":""},preventDefaultTouchMove:{type:Boolean,"default":!0}},data:function(){return{is:"ui-dialog"}
},methods:{preventTouchMove:function(t){this.preventDefaultTouchMove&&t.preventDefault()},eventEmitter:function(t){this.$dispatch(t,this),t===s&&this.hideUi()},closeUi:function(){this.hideUi(),this.eventEmitter("close")
},showUi:function(){this.show=!0},hideUi:function(){this.show=!1},px2rem:function(t){return"number"!=typeof t||isNaN(t)?t:(t/d+1e-4).toFixed(4)+"rem"}},computed:{headerCss:function(){var t="";return this.head||(t="no-header"),t
},needFoot:function(){return c.test(this.type)||!this.foot?!1:!0}},created:function(){l.test(this.type)&&this.head&&(this.head=!0),this.width=this.px2rem(this.width),this.top=this.top?this.px2rem(this.top):void 0
},components:{uiMask:a.default}};var p='<div>\n    <ui-mask :show="show"></ui-mask>\n    <div class="fin-{{ is }} {{ type }} {{ prefix }}" :style="{top: top, width: width}" v-show="show" :transition="animate" @touchmove="preventTouchMove($event)">\n        <div class="header" v-if="head">\n            <div class="title">\n                {{ title }}\n            </div>\n            <div class="fin-{{ is }}-close icon icon-close close" @click="closeUi" v-if="close"></div>\n        </div>\n        <div class="{{ headerCss }} content">\n            <slot name="content">{{{ content }}}</slot>\n        </div>\n\n        <div class="foot clearfix" v-if="needFoot">\n            <div v-if="type === \'confirm\'">\n                <span class="cancel" @click.stop="eventEmitter(\'cancel\')">{{ cancel }}</span>\n                <span class="ok" @click.stop="eventEmitter(\'ok\')">{{ ok }}</span>\n            </div>\n            <div v-if="type === \'alert\'">\n                <span class="ok" @click.stop="eventEmitter(\'ok\')">{{ ok }}</span>\n            </div>\n            <div v-if="type === \'prompt\'">\n                <span class="fin-ui-button default large ok" @click="eventEmitter(\'ok\')">{{ ok }}</span>\n                <span class="fin-ui-button primary large cancel" @click="eventEmitter(\'cancel\')">{{ cancel }}</span>\n            </div>\n        </div>\n    </div>\n</div>';
i&&i.exports&&(i.exports.template=p),e&&e.default&&(e.default.template=p),i.exports=e["default"]});
;define("hiloan:components/fin-ui/ui-dialog/index",function(e,i,u){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(i,"__esModule",{value:!0});var o=e("hiloan:components/fin-ui/ui-dialog/ui-dialog.vue"),t=n(o);
i.default=t.default,u.exports=i["default"]});
;define("hiloan:components/fin-ui/ui-loading/ui-loading.vue",function(t,i,e){"use strict";function n(t){return t&&t.__esModule?t:{"default":t}}Object.defineProperty(i,"__esModule",{value:!0});var o=t("hiloan:components/fin-ui/ui-mask/index"),a=n(o);
i.default={props:{show:{type:Boolean,"default":!1},text:{type:String,"default":"正在加载..."},mask:{type:Boolean,"default":!0},opacity:{type:Number,"default":0},icon:{type:String,"default":"icon-loading loading"}},data:function(){return{is:"ui-loading"}
},methods:{showUi:function(){this.show=!0,this.$dispatch("show",this)},hideUi:function(){this.show=!1,this.$dispatch("hide",this)}},components:{uiMask:a.default}};var s='<ui-mask v-if="mask" :show="show" :opacity="opacity">\n</ui-mask>\n<aside class="fin-ui-loading" v-show="show" transition="zoom-center">\n    <i class="{{ icon }}"></i>\n    <span>{{ text }}</span>\n</aside>';
e&&e.exports&&(e.exports.template=s),i&&i.default&&(i.default.template=s),e.exports=i["default"]});
;define("hiloan:components/fin-ui/ui-loading/index",function(e,n,i){"use strict";function u(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var o=e("hiloan:components/fin-ui/ui-loading/ui-loading.vue"),t=u(o);
n.default=t.default,i.exports=n["default"]});
;define("hiloan:components/fin-ui/ui-toast/ui-toast.vue",function(t,e,i){"use strict";function o(t){return t&&t.__esModule?t:{"default":t}}Object.defineProperty(e,"__esModule",{value:!0});var s=t("hiloan:components/fin-ui/ui-mask/index"),n=o(s);
e.default={props:{show:{type:Boolean,"default":!1,twoWay:!0},timeout:{type:Number,"default":2e3},content:{type:String,"default":""},position:{type:String,"default":"middle"},mask:{type:Boolean,"default":!0},opacity:{type:Number,"default":0}},data:function(){return{is:"ui-toast",timer:null}
},methods:{showUi:function(){this.show=!0,this.$dispatch("show",this)},hideUi:function(){this.show=!1,this.$dispatch("hide",this)}},watch:{show:{handler:function(t){var e=this;t&&(clearTimeout(this.timer),this.timer=setTimeout(function(){e.hideUi()
},this.timeout))},immediate:!0}},components:{uiMask:n.default}};var a='<ui-mask v-if="mask" :show="show" :opacity="opacity">\n</ui-mask>\n<aside class="fin-ui-toast {{ position }}" v-show="show" transition="zoom-center">\n    <div class="toast-wrap">\n        <slot name="pre"></slot>\n        <span>{{ content }}</span>\n        <slot></slot>\n    </div>\n</aside>';
i&&i.exports&&(i.exports.template=a),e&&e.default&&(e.default.template=a),i.exports=e["default"]});
;define("hiloan:components/fin-ui/ui-toast/index",function(e,t,u){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var i=e("hiloan:components/fin-ui/ui-toast/ui-toast.vue"),o=n(i);
t.default=o.default,u.exports=t["default"]});
;define("hiloan:components/fin-fg/util/ui/index",function(n,e,o){"use strict";function i(n){return n&&n.__esModule?n:{"default":n}}Object.defineProperty(e,"__esModule",{value:!0});var t=n("hiloan:node_modules/vue/dist/vue.common"),u=i(t),d=n("hiloan:node_modules/es6-promise/dist/es6-promise"),a=n("hiloan:node_modules/object-assign/index"),l=i(a),s=n("hiloan:components/fin-ui/ui-dialog/index"),c=i(s),r=n("hiloan:components/fin-ui/ui-loading/index"),f=i(r),m=n("hiloan:components/fin-ui/ui-toast/index"),h=i(m),p={dialog:c.default,loading:f.default,toast:h.default},v=document,g=v.createElement("div");
document.body.appendChild(g),Object.keys(p).forEach(function(n){var e=p[n];p[n]=function(o){var i=u.default.component(n,e),t=new i({el:g});return l.default(t,o),t.$appendTo(document.body),t.showUi&&t.showUi(),new d.Promise(function(e,o){return t.$on("ok",function(){return e(t)
}),t.$on("show",function(){return e(t)}),t.$on("hide",function(){return e(t)}),t.$on("cancel",function(){return o(t)}),"dialog"!==n?e(t):void 0})}}),e.default=p,o.exports=e["default"]});
;define("hiloan:components/fin-fg/util/request/util/parse-url",function(e,t,r){"use strict";function o(e){var t={};e=e||"";var r=document.createElement("a");r.href=e,t.protocol=r.protocol,t.hostname=r.hostname,t.port=r.port,t.pathname=r.pathname,t.search=r.search.slice(1),t.hash=r.hash.slice(1),t.host=r.host,t.query={};
var o=t.search;if(/^\?/.test(o)){o=o.substr(1).split("&");for(var s=0;s<o.length;s++){var a=o[s].split("="),n=a[0],u=a[1],h=t.query[n];h?"string"==typeof h?t.query[n]=[h,u]:h.push(u):t.query[n]=u&&decodeURI(decodeURI(u))
}}return t}Object.defineProperty(t,"__esModule",{value:!0}),t.default=o,r.exports=t["default"]});
;define("hiloan:components/fin-fg/util/request/query",function(e,t){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0}),t.withMergeQuery=t.withQuery=t.getQuery=t.parse=void 0;
{var a=e("hiloan:node_modules/underscore/underscore"),n=r(a),u=e("hiloan:node_modules/query-string/index"),i=r(u),s=e("hiloan:app/static/config/constant"),o=e("hiloan:components/fin-fg/util/request/util/parse-url"),l=r(o),f=t.parse=function(e){if(n.default.isObject(e))return e;
e||(e=location.href);var t=l.default(e);return t.hash=i.default.parse(t.hash),t.search=n.default.extend({},i.default.parse(t.search)),t},c=(t.getQuery=function(e){var t=f(e),r=t.hash,a=t.search,n=""+i.default.stringify(r),u=""+i.default.stringify(a);
return{hash:r,search:a,hashString:n,searchString:u}},t.withQuery=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments[2],a=f(e),u=a.protocol,s=a.host,o=a.pathname;return u||(u=location.protocol),s||(s=location.host),o||(o=location.pathname),r=n.default.isObject(r)&&!n.default.isEmpty(r)?"#"+i.default.stringify(r):"",t=n.default.extend({},a.search,t),t=i.default.stringify(t),u+"//"+s+o+"?"+t+r
});t.withMergeQuery=function(e,t,r,a){var u=i.default.parse(s.defaultQuery),o=f().search||{},l=n.default.extend({},u,o,Array.isArray(t)?{}:t),h=[];return Array.isArray(t)&&(h=h.concat(t)),Array.isArray(r)&&(h=h.concat(r)),h.length>0&&h.forEach(function(e){return delete l[e]
}),c(e,l,a)}}});
;define("hiloan:components/fin-fg/util/store/keygen",function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var o=t("hiloan:app/static/config/constant"),u=G.constants.uniqueId,s=o.defaultArgs.app,i=o.defaultArgs.projectId;
e.default=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{uid:!0,projectId:!0,custom:""},e=t.uid,n=void 0===e?!0:e,o=t.projectId,r=void 0===o?!0:o,a=t.custom,d=void 0===a?"":a,c=[];
return n&&c.push(u),c.push(s),r&&c.push(i),d&&c.push(d),c.filter(function(t){return!!t}).join("_").toLowerCase()},n.exports=e["default"]});
;define("hiloan:components/fin-fg/util/store/local",function(e,t,u){"use strict";function a(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var l=e("hiloan:node_modules/underscore/underscore"),n=a(l),f=e("hiloan:node_modules/store/store"),o=a(f),d=e("hiloan:app/static/config/constant"),r=e("hiloan:components/fin-fg/util/store/keygen"),i=a(r);
o.default.getItem=function(e,t){var u=i.default(t),a=o.default.get(u);return a&&a[e]},o.default.getAll=function(e){var t=i.default(e);return o.default.get(t)},o.default.setItem=function(e,t,u){var a={};
a[e]=t;var l=i.default(u);return o.default.set(l,n.default.extend({},o.default.get(l),a)),o.default},o.default.removeItem=function(e,t){var u=i.default(t),a=o.default.get(u);return a&&delete a[e],a=n.default.extend({},a),n.default.isEmpty(a)?o.default.remove(u):o.default.set(u,a),o.default
},o.default.removeAll=function(e){var t=i.default(e);return o.default.remove(t),o.default},o.default.removeRecordInput=function(){var e=d.defaultArgs.app;Object.keys(localStorage).forEach(function(t){return-1!==t.indexOf(e+"_")?localStorage.removeItem(t):void 0
})},t.default=o.default,u.exports=t["default"]});
;define("hiloan:components/fin-fg/util/store/index",function(e,t,n){"use strict";function o(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var l=e("hiloan:components/fin-fg/util/store/local"),u=o(l);
t.default={local:u.default},n.exports=t["default"]});
;define("hiloan:components/fin-fg/util/native/method",function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={doPay:"doPay",pay:"pay",qr:"qr",camera:"camera",IDPhoto:"IDPhoto",detectBankCard:"detectBankCard",position:"position",getDeviceInfo:"getDeviceInfo",ua:"ua",getLocalUserAgent:"getContacts",share:"share"},a.exports=t["default"]
});
;define("hiloan:components/fin-fg/util/native/cache",function(e,t,n){"use strict";function o(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var i=e("hiloan:node_modules/underscore/underscore"),a=o(i),r=e("hiloan:components/fin-fg/util/store/index"),s=e("hiloan:components/fin-fg/util/native/method"),u=o(s),c=window.sessionStorage,f={getDeviceInfo:{name:u.default.getDeviceInfo,type:"session"},position:{name:u.default.position,type:"local"}},l=function(e){return"native_"+f[e].name+"_cache"
},d=function(e){var t=f[e];return a.default.extend({},t,{key:l(e)})},m=function(e){if("local"===e.type){var t=r.local.getItem(e.key);try{t=JSON.parse(t)}catch(n){}return t}if("session"===e.type){var o=c.getItem(e.key);
try{o=JSON.parse(o)}catch(n){}return o}return{}},g=function(e,t){return"local"===e.type?r.local.setItem(e.key,JSON.stringify(t))||{}:"session"===e.type?c.setItem(e.key,JSON.stringify(t)):{}};t.default={setItem:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=d(e),o=this.getItem(n.name);
t=a.default.extend({},o,t),g(n,t)},getItem:function(e){var t=d(e);return m(t)},cacheMap:f},n.exports=t["default"]});
;define("hiloan:components/fin-fg/util/native/notify-error",function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a=e("hiloan:app/static/config/notify-native-error/index");t.default={getLocalUserAgent:a.getContacts,position:a.position,IDPhoto:a.camera,camera:a.camera,qr:a.camera},o.exports=t["default"]
});
;define("hiloan:components/fin-fg/util/native/log",function(e,n,t){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"info",t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"log";
return u(e,n,t)};var u=function(){};t.exports=n["default"]});
;define("hiloan:components/fin-fg/util/native/error",function(e,n,t){"use strict";function o(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var l=e("hiloan:components/fin-fg/util/native/notify-error"),i=o(l),r=e("hiloan:components/fin-fg/track/log"),a=o(r),f=e("hiloan:components/fin-fg/util/native/log"),u=(o(f),/^1000[1-3]$/);
n.default={disable:function(e,n){return u.test(n)?(this.globalErrorHandler(e,n),!0):!1},globalErrorHandler:function(e,n){"10002"===n&&i.default[e]&&(a.default.sendDp("z_AGENT_AUTH_FAIL"),i.default[e]())
}},t.exports=n["default"]});
;define("hiloan:components/fin-fg/util/native/browser/position",function(e,t,n){"use strict";function o(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t={enableHighAccuracy:!0,timeout:3e3,maximumAge:6e5};
navigator.geolocation.getCurrentPosition(function(t){var n=t.coords,o=n.latitude,i=n.longitude;return e.resolve({data:{latitude:o,longitude:i}})},function(){return e.resolve({data:{latitude:"",longitude:""}})
},t)};{var i=e("hiloan:node_modules/object-assign/index");o(i)}n.exports=t["default"]});
;define("hiloan:components/fin-fg/util/native/browser/index",function(e,n,r){"use strict";function o(e){if(e&&e.__esModule)return e;var n={};if(null!=e)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=e[r]);
return n.default=e,n}Object.defineProperty(n,"__esModule",{value:!0});var t=e("hiloan:components/fin-fg/util/native/browser/position"),i=o(t);n.default={position:i.default,getDeviceInfo:function(e){return e.resolve({data:{appversioncode:"",appversionname:"",brand:"",cuid:"",imei:"",ip:"",model:"",name:"",os:/iphone/i.test(navigator.userAgent)?"ios":"android",version:""}})
},ua:function(e){return e.resolve({data:navigator.userAgent})},IDPhoto:function(e){return e.resolve("")},camera:function(e){return e.resolve({des:"",errCode:"",image:""})},getContacts:function(e){return e.resolve({all:null,des:"",errCode:"",selected:{name:"",phone:""}})
},detectBankCard:function(e){return e.resolve("")},qr:function(e){return e.resolve({success:""})},doPay:function(e){return e.resolve({})}},r.exports=n["default"]});
;define("hiloan:components/fin-fg/util/native/invoke",function(e,n,t){"use strict";function o(e){return e&&e.__esModule?e:{"default":e}}function a(e,n,t){return new i.Promise(function(o,a){var i=c.default[e];
if(!window.Agent.OS.wallet&&E.default[i])return E.default[i]({resolve:o,reject:a});var d={position:function(n){if(!S.test(n.data.latitude)){var t=_.default[e];return t&&t(),C.default.sendDp("z_AGENT_S_POSITION_ERROR"),a(n)
}},getContacts:function(e){"string"==typeof e&&(e=JSON.parse(decodeURIComponent(escape(atob(e)))));var n=e.selected.phone.replace(/[-\s]/g,""),t=N.exec(n);if(!t)return C.default.sendDp(e.selected.phone+"z_AGENT_S_CONTACTS_ERROR"),l.dialog({type:"alert",content:"请选择正确的联系人手机号码",head:!1}).then(function(e){e.hideUi()
}),a();var i=r.default({},e);return i.selected.phone=t[t.length-1],o(i)}},f=+new Date;C.default.sendDp("z_AGENT_INVOKE"),window.Agent[i]({param:n,success:function(n){C.default.sendDp({z_AGENT_CONSUME_TIME:+new Date-f},"time"),C.default.sendDp("z_AGENT_SUCCESS_CALL");
var r=n,l=!1;return n&&n.data&&n.data.cnt&&(r=n.data.cnt),r&&r.errCode&&(l=g.default.disable(e,r.errCode),l===!0||"error"===r.type)?(C.default.sendSdkError(i,r),a(r)):(i in d&&d[i](r),i in u.default.cacheMap&&t&&u.default.setItem(i,r),o(r))
},error:function(n){var t=n.data.cnt;return C.default.sendExc("端能力调用异常,方法名:`"+i+"`"),C.default.sendDp("z_AGENT_FAIL_CALL"),t&&g.default.disable(e,t.errCode),C.default.sendSdkError(i,n),a(t)}})})}Object.defineProperty(n,"__esModule",{value:!0}),n.default=a;
var i=e("hiloan:node_modules/es6-promise/dist/es6-promise"),d=e("hiloan:node_modules/object-assign/index"),r=o(d),l=e("hiloan:components/fin-fg/util/ui/index"),f=e("hiloan:components/fin-fg/util/native/cache"),u=o(f),s=e("hiloan:components/fin-fg/util/native/method"),c=o(s),p=e("hiloan:components/fin-fg/util/native/notify-error"),_=o(p),h=e("hiloan:components/fin-fg/util/native/error"),g=o(h),m=e("hiloan:components/fin-fg/util/native/log"),v=(o(m),e("hiloan:components/fin-fg/util/native/browser/index")),E=o(v),A=e("hiloan:components/fin-fg/track/log"),C=o(A),S=/^[-]?(\d|([1-9]\d)|(1[0-7]\d)|(180))(\.\d*)?$/,N=/^(?:(\+|00)86\s?)?([1][3456789]\d{9})$/;
t.exports=n["default"]});
;define("hiloan:components/fin-fg/util/native/mock",function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={},n.exports=t["default"]});
;define("hiloan:components/fin-fg/util/native/native-qr-shim",function(e,n,t){"use strict";function o(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0}),n.default=function(e,n){return f.default("getDeviceInfo").then(function(t){return parseFloat(t.data.appversionname)<2.1?(a.dialog({type:"confirm",title:"请升级百度钱包",content:"当前百度钱包为旧版本，无法使用本功能，请下载最新版本!",ok:"下载"}).then(function(){location.href="http://app.baifubao.com/?channel=1015133s&from=eduloandl"
}),i.Promise.reject(t)):f.default(e,n)})};var i=e("hiloan:node_modules/es6-promise/dist/es6-promise"),a=e("hiloan:components/fin-fg/util/ui/index"),u=e("hiloan:components/fin-fg/util/native/invoke"),f=o(u);
t.exports=n["default"]});
;define("hiloan:components/fin-fg/util/native/index",function(e,n,t){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}function r(e){if(!d.default.hasOwnProperty(e))throw Error("[error]:`"+e+"`钱包不支持该功能或util不支持!");
return!0}Object.defineProperty(n,"__esModule",{value:!0});var o=e("hiloan:node_modules/underscore/underscore"),a=i(o),u=e("hiloan:node_modules/es6-promise/dist/es6-promise"),s=e("hiloan:components/fin-fg/util/native/invoke"),l=i(s),f=e("hiloan:components/fin-fg/util/native/method"),d=i(f),m=e("hiloan:components/fin-fg/util/native/cache"),g=i(m),c=e("hiloan:components/fin-fg/util/native/mock"),v=i(c),h=e("hiloan:components/fin-fg/util/native/error"),p=i(h),P=e("hiloan:components/fin-fg/util/native/log"),_=(i(P),e("hiloan:components/fin-fg/util/native/native-qr-shim")),w=i(_),E=!1;
n.default={invoke:function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=arguments[2];return l.default(e,n,t)},get:function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=arguments[2],i=void 0,o=d.default[e];
if(E&&r(e))return i=v.default[o],u.Promise.resolve(i()||{});if(!Agent.OS.wallet){var s=G,l=s.NATIVE;if(l&&l.ignore===!0)return u.Promise.resolve({message:"This app is not BaiduWallet or its SDK!"})}if("qr"!==o){if(t&&e in g.default.cacheMap){var f=g.default.getItem(e);
if(a.default.isObject(f)&&!a.default.isEmpty(f))return u.Promise.resolve(f)}var m=this.invoke(e,n,t);return m}return w.default(e,n)},error:function(e){return p.default.disable(e,"10002"),u.Promise.reject({errCode:"10002"})
},configError:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=window.G.constants;n.nativeErrorConfig=e}},t.exports=n["default"]});
;define("hiloan:components/fin-fg/util/login/index",function(n,o,e){"use strict";function t(n){return n&&n.__esModule?n:{"default":n}}Object.defineProperty(o,"__esModule",{value:!0}),o.default=function(n){return Agent.OS.wallet?(n=i.default({},{tpl:"bp",u:"https://www.baifubao.com"},n),c(n)):void m(n)
};var s=n("hiloan:node_modules/es6-promise/dist/es6-promise"),a=n("hiloan:node_modules/object-assign/index"),i=t(a),r=3,p=window.G&&window.G.constants&&window.G.constants.env,u={online:"wappass.baidu.com/passport/login?adapter=",sandbox:"wappass.baidu.com/passport/login?adapter=",qa:"wappass.qatest.baidu.com/passport/login?adapter=",rd:"wappass.qatest.baidu.com/passport/login?adapter="},d=u[p]||u.online,l="https://"+d+r,c=function(n){return new s.Promise(function(o,e){Agent.bdLogin({option:n,success:function(n){"success"===n.type&&(o(n),location.reload()),e(n)
},error:function(n){"error"===n.type&&alert(n.msg?n.msg:"请升级您的app到最新版本！"),e(n)}})})},m=function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},o=l;n.sms&&(o+="&sms="+n.sms),location.href=o+"&u="+encodeURIComponent(n.u||location.href)
};e.exports=o["default"]});
;define("hiloan:components/fin-fg/util/request/post",function(e,n,t){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0}),n.default=function(e,n,t){if(r.default.isEmpty(I)){I=d.default({},m.defaultArgs);
var i=G,o=i.NATIVE;return o&&o.ignore===!0?A(e,n,t):v.default.get("ua").then(function(e){return e.data&&(I=d.default({},{UA:e.data},I)),v.default.get("getDeviceInfo",{},!0)}).then(function(i){var o=i.data,r=o.appversionname,a=void 0===r?"":r,u=o.appversioncode,f=void 0===u?"":u,s=o.cuid,l=void 0===s?"":s,c=o.imei,g=void 0===c?"":c,v=o.BAIDUCUID,m=void 0===v?"":v;
return I=d.default({},{appVer:f,appName:a,BAIDUCUID:m,cuid:l,imei:g},I),A(e,n,t)}).catch(function(i){return i.errCode?void A(e,n,t):a.Promise.reject(i)})}return A(e,n,t)};var o=e("hiloan:node_modules/underscore/underscore"),r=i(o),a=e("hiloan:node_modules/es6-promise/dist/es6-promise"),u=e("hiloan:node_modules/object-assign/index"),d=i(u),f=e("hiloan:node_modules/query-string/index"),s=i(f),l=e("hiloan:components/fin-fg/util/request/request"),c=i(l),g=e("hiloan:components/fin-fg/util/native/index"),v=i(g),m=e("hiloan:app/static/config/constant"),p=e("hiloan:components/fin-fg/util/login/index"),h=i(p),_=(e("hiloan:components/fin-fg/util/ui/index"),"token"),x=window.G.constants,D=function(){return h.default()
},I={},A=function(e,n){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(x&&x[_]&&!I[_]){var i={};i[_]=x[_],I=d.default({},I,i)}var o=e;o+=-1===o.indexOf("?")?"?":"&";var u={_t:(+new Date).toString(32)};
o+=s.default.stringify(r.default.extend({},I,u));var f=d.default({},n);return c.default.post(o,f,t).then(function(e){if(x&&x.isLogin===!0&&!I[_]){var n=new Headers,t=n.get(_);if(r.default.isString(t)){var i={};
i[_]=t,I=d.default({},I,i)}}return a.Promise.resolve(e)}).catch(function(e){return 1===e.errno&&D(),a.Promise.reject(e)})};t.exports=n["default"]});
;define("hiloan:components/fin-fg/track/log",function(t,e,n){"use strict";function r(t){return t&&t.__esModule?t:{"default":t}}Object.defineProperty(e,"__esModule",{value:!0});var i=t("hiloan:components/fin-fg/util/request/query"),a=t("hiloan:components/fin-fg/util/request/post"),o=r(a),s={hasBdTj:"undefined"!=typeof _hmt,hasDpTj:"undefined"!=typeof alog},u=function(){var t=i.getQuery().search.logParam||"";
return decodeURIComponent(t)}(),c=function(){var t=i.getQuery().search.productId||"common",e=location.pathname.split("/").filter(function(t){return""!==t});e.unshift(t),window.G&&window.G.constants&&window.G.constants.pageId&&e.push(window.G.constants.pageId);
var n=e.join("_").toLowerCase();return function(){return n}}(),f=function(){var t=window.G&&window.G.constants&&window.G.constants.logEmbedding||"";try{t=JSON.parse(t)||{}}catch(e){t={}}var n="undefined"!=typeof t.creditStatus?t.creditStatus:"",r="undefined"!=typeof t.activateStatus?t.activateStatus:"";
return function(){return{creditStatus:n,activateStatus:r}}}(),d={init:function(){document.querySelector("body").addEventListener("click",function(t){for(var e=t.target,n=void 0,r=void 0;e&&(n=e.getAttribute&&e.getAttribute("data-bd-log"),r=e.getAttribute&&e.getAttribute("data-dp-log"),!n&&!r);)e=e.parentNode;
if((n||r)&&!d.shouldIgnore(e)){if(n){try{n=JSON.parse(n)}catch(i){}d.sendBd(n.desc||n,n.type)}if(r){try{r=JSON.parse(r)}catch(i){}d.sendDp(r.desc||r,r.type)}}},!1),this.pageSpeed()},pageSpeed:function(){var t=this;
if(!performance||!performance.timing)return!1;var e=function n(){var e=performance.timing,r=e.navigationStart,i=e.loadEventEnd,a=e.domComplete,o=i-r,s=a-r;t.sendDp({z_LOG_LOADED_TIME:o,z_LOG_DOM_READY_TIME:s},"time"),clearTimeout(n)
};window.addEventListener("load",function(){setTimeout(e,1)},!1)},sendBd:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"click";if(!s.hasBdTj)throw new Error("页面内未引入百度统计代码");if(!t)throw new Error("缺少事件描述参数");
return _hmt.push(["_trackEvent",t,e]),this},sendDp:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"count";if(!s.hasDpTj)throw new Error("页面内未引入DP统计代码");if(!t)throw new Error("缺少事件描述参数");
var n=void 0;return n="count"===e?Array.isArray(t)?t:[t.toString()]:t,alog("cus.fire",e,n),this},sendExc:function(t){if(!s.hasDpTj)throw new Error("页面内未引入DP统计代码");if(!t)throw new Error("缺少异常描述参数");return alog("exception.send","exception",{msg:t,ln:1,js:location.pathname.toString(),flag:"catch"}),this
},ignoreRules:{"class":["disabled"],attribute:["data-disabled","disabled","readonly"]},shouldIgnore:function g(t){var g=!1,e=t.classList,n=this.ignoreRules;for(var r in n){if(n.hasOwnProperty(r))switch(r){case"class":g=n[r].some(function(t){return e.contains(t)
});break;case"attribute":g=n[r].some(function(e){return t.getAttribute(e)})}if(g)break}return g},sendBfbAction:function(t,e){if(!t)throw new Error("缺少事件描述参数");var n=[c(),t].join("_"),r=this.formatBfbValues(e);
"function"==typeof _bfbStatistic&&(r?_bfbStatistic("action",n,r):_bfbStatistic("action",n))},sendBfbPage:function(t){if("function"==typeof _bfbStatistic){var e=c(),n=this.formatBfbValues(t);n?_bfbStatistic("page",e,n):_bfbStatistic("page",e)
}},formatBfbValues:function(t){t=Array.isArray(t)?t:[];for(var e=new Array(5),n=0,r=e.length;r>n;n++)e[n]="undefined"==typeof t[n]||null===t[n]?"":String(t[n]);var i=f();""===e[0]&&(e[0]=String(i.creditStatus)),""===e[1]&&(e[1]=String(i.activateStatus)),""!==u&&(e[2]=String(u));
var a=e.some(function(t){return""!==t});return a?e:!1},sendSdkError:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"fatal";try{var r=["fias","fics","fcss","fucs"];r.indexOf(location.pathname.split("/")[1])&&o.default("/fias/api/bff?method=setlog",{type:n,sdkMethod:t,response:JSON.stringify(e)||""},{"x-silent":!0,"x-message":!1})
}catch(i){}}};window.G&&window.G.constants&&window.G.constants.autoSendBfbLog&&d.sendBfbPage(),e.default=d,n.exports=e["default"]});
;define("hiloan:components/fin-fg/util/request/request",function(e,t,n){"use strict";function o(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0}),e("hiloan:node_modules/whatwg-fetch/fetch");
var r=e("hiloan:node_modules/es6-promise/dist/es6-promise"),a=e("hiloan:node_modules/underscore/underscore"),i=o(a),s=e("hiloan:node_modules/object-assign/index"),u=o(s),d=e("hiloan:node_modules/query-string/index"),l=o(d),m=e("hiloan:components/fin-fg/util/request/util/serialize"),f=o(m),c=e("hiloan:components/fin-fg/util/ui/index"),g=e("hiloan:components/fin-fg/track/log"),h=o(g);
r.Promise.polyfill();var p={credentials:"same-origin"},v="GET",x="POST",y=3e4,_={silent:"x-silent",message:"x-message",timeout:"x-timeout"},E={"x-silent":!1,"x-message":!0,"x-timeout":y},O={Accept:"application/json","Content-Type":"application/x-www-form-urlencoded"},T={"Content-Type":"application/json"},j=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:x,t=arguments[1],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},a=O;
"JSON"===o.dataType?(a=i.default.extend({},a,T),n=JSON.stringify(n)):i.default.isObject(n)&&(n=f.default(n));var s=_.silent,d=_.message,l=_.timeout;o=u.default({},E,o);var m=void 0;o[s]===!1&&(m=c.loading());
var g=o[l],v=function(){m&&m.then(function(e){e.hideUi(),e.$destroy(!0)})},y=o[d];delete o[s],delete o[l],delete o[d];var j=u.default({},{method:e},{headers:a},{body:n},p,o);return new r.Promise(function(e,n){var o=setTimeout(function(){return v(),y===!0&&c.dialog({type:"alert",head:!1,content:"网络不畅，请稍后重试"}).then(function(e){e.hideUi()
}),n({errno:-2,erromsg:"network timeout!"})},g);return fetch(t,j).then(function(t){return v(),clearTimeout(o),200!==t.status?n(t):t.json().then(function(t){var o=t.errno,r=t.errmsg;return o>100?300004===+o&&t.data.home?(y&&c.dialog({type:"alert",head:!1,ok:"返回首页",content:t.errmsg}).then(function(){location.href=t.data.home
}),n(t)):(y&&c.dialog({type:"alert",head:!1,content:t.errmsg}).then(function(e){e.hideUi()}),h.default.sendExc("错误码: "+o+" 错误信息: "+r),n(t)):o>0&&100>=o?(h.default.sendExc("错误码: "+o+" 错误信息: "+r),n(t)):e(t)
})}).catch(function(e){return v(),e.status&&200!==e.status&&(h.default.sendDp("z_REQUEST_SERVER_ERROR"),y===!0)?(alert("网络不给力，请求失败了! \n 请刷新重试！"),n({errno:-1,errmsg:e.status})):n(e)})})},w={};w.post=function(e,t,n){return j(x,e,t,n)
},w.get=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments[2];i.default.isObject(t)&&!i.default.isEmpty(t)&&(t=l.default.stringify(t)),0!==t.length&&-1===e.indexOf("?")&&(e+="?"),e+=t;
var o=u.default({},{method:v},n);return fetch(e,{param:o})},t.default=w,n.exports=t["default"]});
;define("hiloan:components/fin-fg/util/request/apify",function(e,n,t){"use strict";function u(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0}),n.default=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};
e=i.default({},e);var t=Object.keys(e||{});return t.forEach(function(t){var u=i.default(e[t]);e[t]=function(e,t){var r=i.default({},e);return d.default(u,r,o.default.extend({},n,t))},e[t].origin||(e[t].origin=u),e[t].getUrl=function(){return u.toString()
}}),e||{}};var r=e("hiloan:node_modules/underscore/underscore"),o=u(r),a=e("hiloan:node_modules/object-assign/index"),i=u(a),s=e("hiloan:components/fin-fg/util/request/post"),d=u(s);t.exports=n["default"]
});
;define("hiloan:components/fin-fg/util/request/redirect",function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],u=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},a=r.withMergeQuery(e,t,n,u);
location.href=a};var r=e("hiloan:components/fin-fg/util/request/query");n.exports=t["default"]});
;define("hiloan:components/fin-fg/util/request/forward",function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function a(e){if(Array.isArray(e)){for(var t=0,r=Array(e.length);t<e.length;t++)r[t]=e[t];
return r}return Array.from(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=f.parse(location.href),u=n.hash,l=n.search,i=o.default.pick.apply(o.default,[l].concat(a(Object.keys(s.defaultArgs)))),d=o.default.extend({},s.defaultArgs,i,t),c=o.default.extend({},u,r),g=f.withQuery(e,d,c);
location.href=g};var u=e("hiloan:node_modules/underscore/underscore"),o=n(u),s=e("hiloan:app/static/config/constant"),f=e("hiloan:components/fin-fg/util/request/query");r.exports=t["default"]});
;define("hiloan:components/fin-fg/util/base64/index",function(e,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default={decode:function(e){return decodeURIComponent(escape(this.atob(e)))
},encode:function(e){return this.btoa(unescape(encodeURIComponent(e)))},atob:function(e){return window.atob?window.atob(e):e},btoa:function(e){return window.btoa?window.btoa(e):e}},o.exports=n["default"]
});
;define("hiloan:components/fin-fg/util/format/money",function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function t(t,e){var r=[],n=!0,a=!1,u=void 0;try{for(var i,o=t[Symbol.iterator]();!(n=(i=o.next()).done)&&(r.push(i.value),!e||r.length!==e);n=!0);}catch(s){a=!0,u=s
}finally{try{!n&&o["return"]&&o["return"]()}finally{if(a)throw u}}return r}return function(e,r){if(Array.isArray(e))return e;if(Symbol.iterator in Object(e))return t(e,r);throw new TypeError("Invalid attempt to destructure non-iterable instance")
}}();e.default=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:2,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:".",u=arguments.length>4&&void 0!==arguments[4]?arguments[4]:",";
if(t=+t,isNaN(t)||0===t)return"0.00";t=""+(t/100).toFixed(r);for(var i=t.split("."),o=n(i,2),s=o[0],l=o[1],g=/(\d+)(\d{3})/;g.test(s);)s=s.replace(g,"$1"+u+"$2");switch(t=/^0+$/.test(l)?s:""+s+a+l,e){case"cn":t+="元";
break;case"en":t="¥"+t;break;default:t=""+t}return t},r.exports=e["default"]});
;define("hiloan:components/fin-fg/util/format/datetime",function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"yyyy/mm/dd";
e=1e3*e,t=t.toLowerCase(),isNaN(e)&&(e=0);var n=new Date(e),g={"m+":n.getMonth()+1,"d+":n.getDate(),"h+":n.getHours(),"i+":n.getMinutes(),"s+":n.getSeconds()},r=t;/(y+)/.test(r)&&(r=r.replace(RegExp.$1,(""+n.getFullYear()).substring(4-RegExp.$1.length)));
for(var s in g)new RegExp("("+s+")").test(r)&&(r=r.replace(RegExp.$1,1===RegExp.$1.length?g[s]:("00"+g[s]).substring((""+g[s]).length)));return r},n.exports=t["default"]});
;define("hiloan:components/fin-fg/util/format/px2rem",function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return"number"!=typeof e||isNaN(e)?e:(e/o+1e-4).toFixed(4)+"rem"
};var o=37.5;n.exports=t["default"]});
;define("hiloan:components/fin-fg/util/format/index",function(e,t,n){"use strict";function f(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var o=e("hiloan:components/fin-fg/util/format/money"),a=f(o),i=e("hiloan:components/fin-fg/util/format/datetime"),u=f(i),l=e("hiloan:components/fin-fg/util/format/px2rem"),d=f(l);
t.default={money:a.default,datetime:u.default,px2rem:d.default,yuan2fen:d.default},n.exports=t["default"]});
;define("hiloan:components/fin-fg/util/validator/regular",function(e,a,d){"use strict";Object.defineProperty(a,"__esModule",{value:!0}),a.default={phone:/^([1][3456789]\d{9}|[1][3456789]\d\*{4}\d{4})$/,email:/^([a-z0-9A-Z]+[-_|\.]*)+[a-z0-9A-Z]@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\.)+[a-zA-Z]{2,}$/,qq:/^[1-9]\d{1,}$/,weixin:/^[[a-zA-Z\d_-]{5,}$/,bankcard:/^(\d{16}|\d{17}|\d{18}|\d{19})$/},d.exports=a["default"]
});
;define("hiloan:components/fin-fg/util/validator/rule",function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var s=e("hiloan:components/fin-fg/util/validator/regular"),c=r(s);
t.default={empty:{check:function(){return!0}},idcard:{check:function(e){if(e=e.toUpperCase(),""===e)return!0;var t=e,n={11:"北京",12:"天津",13:"河北",14:"山西",15:"内蒙古",21:"辽宁",22:"吉林",23:"黑龙江",31:"上海",32:"江苏",33:"浙江",34:"安徽",35:"福建",36:"江西",37:"山东",41:"河南",42:"湖北",43:"湖南",44:"广东",45:"广西",46:"海南",50:"重庆",51:"四川",52:"贵州",53:"云南",54:"西藏",61:"陕西",62:"甘肃",63:"青海",64:"宁夏",65:"新疆",71:"台湾",81:"香港",82:"澳门",91:"国外"},r=!0;
if(t&&/(^\d{18}$)|(^\d{17}(\d|X|x)$)/i.test(t.trim()))if(n[t.substr(0,2)]){if(18===t.length){t=t.split("");for(var s=[7,9,10,5,8,4,2,1,6,3,7,9,10,5,8,4,2],c=[1,0,"X",9,8,7,6,5,4,3,2],i=0,u=0,a=0,o=0;17>o;o++)u=t[o],a=s[o],i+=u*a;
c[i%11].toString()!==t[17].toString()&&(r=!1)}}else r=!1;else r=!1;return r},message:"请填写正确的身份证号"},phone:{check:function(e){return c.default.phone.test(e.trim())},message:"请填写正确的手机号码"},isInt:{check:function(e){return/^[1-9]\d+$/.test(e)
},message:"请输入整数"},tel:{check:function(e){return/^\d{3,4}[|-]\d{6,}$/.test(e)},message:"请正确填写电话号码"},telArea:{check:function(e){return/^\d{3,4}$/.test(e.split("-")[0])},message:"请正确填写区号"},email:{check:function(e){return""===e||c.default.email.test(e.trim())
},message:"请正确填写您的电子邮箱"},qq:{check:function(e){return c.default.qq.test(e.trim())},message:"请正确输入QQ号"},weixin:{check:function(e){return c.default.weixin.test(e.trim())},message:"请正确输入微信号"},name:{check:function(e){var t=/^[`~!@#$%^&*()+=|{}':;',\[\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？]$/,n=e.split("").some(function(e){return t.test(e)
});return n?!1:/^[\u4e00-\u9fa5(\u00B7|\u2022)|·]{2,20}$/.test(e)},message:"请正确填写您的姓名"},bankcardLength:{check:function(e){return e=e.split("").filter(function(e){return!!e.trim()}).join(""),c.default.bankcard.test(e.trim())
},message:"请正确填写银行卡卡号"},smscode:{check:function(e){return/^\d{6}$/.test(e.trim())},message:"请正确填写验证码"},imgcode:{check:function(e){return/^[0-9A-Za-z]{4}$/.test(e.trim())},message:"图片验证输入不正确"},boskey:{check:function(e){return/^[0-9a-z]{32}$/.test(e.trim())
},message:""},invitecode:{check:function(e){return/^([A-Za-z\d]{6})$/.test(e.trim())},message:"请正确填写教育分期邀请码"},money:{check:function(e){return parseInt(e,10)%100?!1:!0},message:"请正确输入贷款金额"},scholarship:{check:function(e){return parseInt(e,10)<=0||parseInt(e,10)%10?!1:!0
},message:"请正确填写期望金额，需为10的倍数"},idcardtooOld:{check:function(e){var t=(new Date).getFullYear()-parseInt(e.slice(6,10),10);return 22>=t},message:"学员年龄超过22周岁，请学员自行申请贷款"},relationName:{check:function(e){var t=/^[`~!@#$%^&*()+=|{}':;',\[\]<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？]$/,n=["爸爸","爸","爹爹","爹","阿爸","妈妈","妈","娘","阿妈","阿伯","阿爹","恩娘","姆妈","爷爷","爷","阿爷","公公","奶奶","奶","婆婆","婆","娘娘","娘","阿娘","太公","太爷爷","太太","阿太","太婆","太娘娘","太婆","阿太","外公","外翁","外大人","家公","姥爷","外婆","姥姥","姥","哥哥","哥","姐姐","姐","弟弟","弟","妹妹","妹","老公","妻子","妻","丈夫","先生","亲爱的","老婆","老太婆","媳妇","婆婆","公公"];
return-1!==n.indexOf(e)?!1:(e.split("").forEach(function(e){return t.test(e)?!1:void 0}),/^[a-zA-Z\u4e00-\u9fa5(\u00B7|\u2022)\.]{2,20}$/.test(e))},message:"请填写联系人真实姓名"},businessLicenseRule:{check:function(e){return 15===e.length||18===e.length
},message:"长度必须是15位或者18位"},isNum:{check:function(e){return/^[1-9]\d*(\.\d+)?$/.test(e)},message:"请输入数字"},foodLicenceRule:{check:function(e){return e.length>1&&e.length<23},message:"请输入长度不超过22位的信息"}},n.exports=t["default"]
});
;define("hiloan:components/fin-fg/util/validator/async",function(e,n,t){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default={async:{apiName:"remoteValidate",before:function(e){return e
},rule:function(e,n){return e.state!==n?!0:!1},message:"用户名333已存在"},verifyEmailCode:{apiName:"verifyEmailCode",before:function(e){return e},rule:function(e){return 0===e.errno?!0:!1},message:"请输入正确的验证码"}},t.exports=n["default"]
});
;define("hiloan:components/fin-fg/util/validator/custom",function(e,o,t){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(o,"__esModule",{value:!0});var u=e("hiloan:node_modules/underscore/underscore"),a=i(u),n=e("hiloan:node_modules/vue/dist/vue.common"),r=i(n),d=e("hiloan:node_modules/vue-validator/dist/vue-validator.common"),s=i(d),l=e("hiloan:components/fin-fg/util/validator/rule"),f=i(l),c=e("hiloan:components/fin-fg/util/validator/async"),v=i(c);
r.default.use(s.default);var m={},g=function(e,o){var t=void 0,i=void 0;return a.default.isString(o)?i=o:a.default.isFunction(o)?t=o:a.default.isObject(o)&&(t=o.check,i=o.message),t&&(r.default.validator(e,function(e){var o=!0;
return e&&(o=t(e)),o}),m[e]={rule:!0}),i&&(m[e].message=i),m[e]};Object.keys(f.default).forEach(function(e){g(e,f.default[e])}),Object.keys(v.default).forEach(function(e){var o=v.default[e],t=o.apiName,i=void 0===t?"":t,u=o.trigger,n=void 0===u?"":u,r=o.time,d=void 0===r?800:r,s=o.before,l=void 0===s?function(){}:s,f=o.rule,c=void 0===f?function(){}:f,g=o.success,h=void 0===g?function(){}:g,p=o.error,_=void 0===p?function(){}:p,b=o.message,j=void 0===b?{success:"",error:"",loading:"请求中"}:b;
a.default.isString(v.default[e].message)&&(j={success:"",error:v.default[e].message,loading:"请求中"}),m[e]={apiName:i,trigger:n,time:d,before:l,rule:c,success:h,error:_,message:j}}),m.extend=g,o.default=m,t.exports=o["default"]
});
;define("hiloan:components/fin-fg/util/validator/index",function(e,t,l){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var o=e("hiloan:components/fin-fg/util/validator/custom"),u=n(o),a=e("hiloan:components/fin-fg/util/validator/rule"),i=n(a),f=e("hiloan:components/fin-fg/util/validator/regular"),r=n(f);
t.default={custom:u.default,rule:i.default,regular:r.default},l.exports=t["default"]});
;define("hiloan:components/fin-fg/util/eventbus/index",function(e,n,t){"use strict";function u(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var o=e("hiloan:node_modules/vue/dist/vue.common"),d=u(o);
n.default=new d.default,t.exports=n["default"]});
;define("hiloan:components/fin-fg/util/calculate/index",function(t,r,n){"use strict";n.exports={add:function(t,r){var n=0,e=0,i=0;try{n=t.toString().split(".")[1].length}catch(c){n=0}try{e=r.toString().split(".")[1].length
}catch(c){e=0}return i=Math.pow(10,Math.max(n,e)),(t*i+r*i)/i},sub:function(t,r){return t.add(-r)},mul:function(t,r){var n=0,e=t.toString(),i=r.toString();try{n+=e.split(".")[1].length}catch(c){}try{n+=i.split(".")[1].length
}catch(c){}return Number(e.replace(".",""))*Number(i.replace(".",""))/Math.pow(10,n)},div:function(t,r){var n=0,e=0,i=0,c=0;try{n=t.toString().split(".")[1].length}catch(a){}try{e=r.toString().split(".")[1].length
}catch(a){}return i=Number(t.toString().replace(".","")),c=Number(r.toString().replace(".","")),i/c*Math.pow(10,e-n)}}});
;define("hiloan:components/fin-fg/util/index",function(e,n,t){"use strict";function i(e){if(e&&e.__esModule)return e;var n={};if(null!=e)for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(n[t]=e[t]);
return n.default=e,n}function o(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var l=e("hiloan:components/fin-fg/util/request/request"),f=o(l),u=e("hiloan:components/fin-fg/util/request/post"),a=o(u),r=e("hiloan:components/fin-fg/util/request/apify"),s=o(r),d=e("hiloan:components/fin-fg/util/request/util/parse-url"),c=o(d),p=e("hiloan:components/fin-fg/util/request/query"),g=i(p),m=e("hiloan:components/fin-fg/util/request/redirect"),h=o(m),q=e("hiloan:components/fin-fg/util/request/forward"),x=o(q),v=e("hiloan:components/fin-fg/util/native/index"),y=o(v),_=e("hiloan:components/fin-fg/util/store/index"),b=o(_),w=e("hiloan:components/fin-fg/util/ui/index"),M=o(w),O=e("hiloan:components/fin-fg/util/base64/index"),j=o(O),P=e("hiloan:components/fin-fg/util/format/index"),B=o(P),U=e("hiloan:components/fin-fg/util/validator/index"),k=o(U),z=e("hiloan:components/fin-fg/util/login/index"),A=o(z),C=e("hiloan:components/fin-fg/util/eventbus/index"),D=o(C),E=e("hiloan:components/fin-fg/util/calculate/index"),F=o(E);
n.default={request:f.default,post:a.default,apify:s.default,redirect:h.default,forward:x.default,parseUrl:c.default,query:g,"native":y.default,store:b.default,format:B.default,validator:k.default,base64:j.default,ui:M.default,login:A.default,eventBus:D.default,calculate:F.default},t.exports=n["default"]
});
;define("hiloan:components/fin-cs/help-contact/help-contact.vue",function(e,n,t){"use strict";function o(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var i=e("hiloan:node_modules/vue/dist/vue.common"),a=o(i),l=(e("hiloan:components/fin-fg/util/index"),e("hiloan:components/fin-ui/ui-dialog/index")),d=o(l),s=e("hiloan:components/fin-fg/util/index"),u=e("hiloan:node_modules/vue-touch/vue-touch"),r=o(u);
a.default.use(r.default),n.default={props:{onlineUrl:{type:String,required:!0},onlineText:{type:String,required:!1,"default":"联系在线客服"},telText:{type:String,required:!1,"default":"拨打客服电话"},phonenumTxt:{type:String,required:!1,"default":"95055"},env:{type:String,required:!1,"default":"online"},needOnline:{type:Boolean,required:!1,"default":!0},needTel:{type:Boolean,required:!1,"default":!0}},data:function(){return{is:"help-contact",phoneNum:this.phonenumTxt,dialogShow:!1}
},methods:{onCall:function(){Agent.OS.android?this.dialogShow=!0:location.href="tel:"+this.phoneNum},onOk:function(){this.dialogShow=!1,location.href="tel:"+this.phoneNum},onOnline:function(){var e=this;
G.constants.isLogin?location.href=this.onlineUrl:(Agent.ready(function(){s.login().then(function(){location.href=e.onlineUrl})}),Agent.error(function(){var n=e.onlineUrl,t="https://wappass.baidu.com/passport/login?adapter=3";
switch(e.env){case"qa":t="https://wappass.qatest.baidu.com/passport/login?adapter=3";break;case"rd":t="https://passport.rdtest.baidu.com?adapter=3";break;case"sandbox":t="https://wappass.baidu.com/passport/login?adapter=3"
}location.href=t+"&u="+encodeURIComponent(n||location.href)}))}},components:{uiDialog:d.default}};var c='<div class="fin-{{is}}">\n    <div v-if="needOnline" class="online con-item">\n        <a v-touch:tap="onOnline">\n            <i class="icon icon-headphone"></i>\n            {{onlineText}}\n        </a>\n    </div>\n    <div v-if="needTel" class="tel con-item">\n        <a id="phone" v-touch:tap="onCall">\n            <i class="icon icon-telephone"></i>\n            {{telText}}\n        </a>\n    </div>\n</div>\n<ui-dialog :show.sync="dialogShow" :head="false" :content="phonenumTxt" cancel="取消" ok="呼叫" @ok="onOk">\n</ui-dialog>';
t&&t.exports&&(t.exports.template=c),n&&n.default&&(n.default.template=c),t.exports=n["default"]});
;define("hiloan:components/fin-cs/help-contact/index",function(e,t,n){"use strict";function o(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var c=e("hiloan:components/fin-cs/help-contact/help-contact.vue"),l=o(c);
t.default=l.default,n.exports=t["default"]});
;define("hiloan:components/fin-cs/help-content/help-content.vue",function(e,n,t){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var l=e("hiloan:components/fin-cs/help-contact/index"),o=i(l);
n.default={props:{question:{type:String,required:!0},content:{type:Array,required:!0},onlineUrl:{type:String,required:!0},onlineText:{type:String,required:!1,"default":"联系在线客服"},telText:{type:String,required:!1,"default":"拨打客服电话"},phonenumTxt:{type:String,required:!1,"default":"95055"},env:{type:String,required:!1,"default":"online"},needOnline:{type:Boolean,required:!1,"default":!0},needTel:{type:Boolean,required:!1,"default":!0}},data:function(){return{is:"help-content"}
},components:{helpContact:o.default}};var r='<div class="fin-{{is}}">\n    <div class="fin-help-content-wrapper" :class="{noContact: !needOnline &amp;&amp; !needTel}">\n        <i class="icon icon-question"></i>\n        <h2>{{question}}</h2>\n\n        <div class="payment-quick-help-content">\n            <div class="paragraph" v-for="item in content">\n                <h3 v-if="item.title">{{item.title}}</h3>\n                <h4 v-if="item.subtitle">{{item.subtitle}}</h4>\n                <p v-for="para in item.detail" v-html="para"></p>\n            </div>\n        </div>\n    </div>\n    <div v-if="needTel || needOnline" class="service">\n        还没解决您的问题？\n    </div>\n    <help-contact :online-url="onlineUrl" :online-text="onlineText" :tel-text="telText" :phonenum-txt="phonenumTxt" :need-online="needOnline" :need-tel="needTel" :env="env">\n    </help-contact>\n</div>';
t&&t.exports&&(t.exports.template=r),n&&n.default&&(n.default.template=r),t.exports=n["default"]});
;define("hiloan:components/fin-cs/help-content/index",function(e,n,t){"use strict";function o(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var l=e("hiloan:components/fin-cs/help-content/help-content.vue"),u=o(l);
n.default=u.default,t.exports=n["default"]});
;define("hiloan:components/fin-cs/help-list/help-list.vue",function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var o=e("hiloan:node_modules/vue/dist/vue.common"),l=i(o),a=e("hiloan:node_modules/vue-touch/vue-touch"),r=i(a),d=e("hiloan:components/fin-fg/util/index"),s=e("hiloan:components/fin-cs/help-contact/index"),u=i(s);
l.default.use(r.default),t.default={props:{title:{type:Array,required:!0},quelist:{type:Array,required:!0},contentUrl:{type:String,required:!0},onlineUrl:{type:String,required:!0},onlineText:{type:String,required:!1,"default":"联系在线客服"},telText:{type:String,required:!1,"default":"拨打客服电话"},phonenumTxt:{type:String,required:!1,"default":"95055"},env:{type:String,required:!1,"default":"online"},needOnline:{type:Boolean,required:!1,"default":!0},needTel:{type:Boolean,required:!1,"default":!0}},data:function(){return{is:"help-list",primaryKey:location.pathname.toUpperCase(),pageData:{}}
},methods:{gotoContent:function(e){""!==this.contentUrl&&d.redirect(this.contentUrl,{key:e})},recordScrollPosition:function(){var e=window.scrollY;this.pageData[this.primaryKey]=e,sessionStorage.setItem("PAGE_SCROLL_TOP",JSON.stringify(this.pageData))
},reloadScrollPosition:function(){try{this.pageData=JSON.parse(sessionStorage.getItem("PAGE_SCROLL_TOP"))||{}}catch(e){}var t=this.pageData[this.primaryKey];if(t){var n=+t;n<document.body.clientHeight&&n>50&&window.scrollTo(0,n)
}},bindEvent:function(){var e=this;window.onpagehide=function(){e.recordScrollPosition()},window.onpageshow=function(){e.reloadScrollPosition()}}},ready:function(){this.bindEvent()},components:{helpContact:u.default}};
var p='<div class="fin-help-list" :class="{noContact: !needOnline &amp;&amp; !needTel}">\n    <header>\n        <h2></h2>\n        <p class="quick-info">\n            <template v-for="tItem in title">\n                <span>{{tItem}}</span>\n                <i></i>\n            </template>\n        </p>\n    </header>\n    <div class="question-list">\n        <dl v-for="lItem in quelist">\n            <dt>{{lItem.category}}</dt>\n            <template v-for="item in lItem.info">\n                <dd v-touch:tap="gotoContent(item.key)">\n                    <a id="{{item.key}}">{{item.title}}</a>\n                    <i class="icon icon-arrow-right"></i>\n                </dd>\n                <div class="line"></div>\n            </template>\n        </dl>\n    </div>\n    <help-contact :online-url="onlineUrl" :online-text="onlineText" :tel-text="telText" :phonenum-txt="phonenumTxt" :need-online="needOnline" :need-tel="needTel" :env="env">\n    </help-contact>\n</div>';
n&&n.exports&&(n.exports.template=p),t&&t.default&&(t.default.template=p),n.exports=t["default"]});
;define("hiloan:components/fin-cs/help-list/index",function(e,t,n){"use strict";function l(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var i=e("hiloan:components/fin-cs/help-list/help-list.vue"),o=l(i);
t.default=o.default,n.exports=t["default"]});
;define("hiloan:components/fin-cs/help-service/help-service.vue",function(e,t,n){"use strict";function l(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var o=e("hiloan:node_modules/vue/dist/vue.common"),r=l(o),i=e("hiloan:components/fin-fg/util/index"),a=e("hiloan:node_modules/vue-touch/vue-touch"),s=l(a);
r.default.use(s.default),t.default={props:{jumpUrl:{type:String,required:!1,"default":""},textArr:{type:Array,required:!1,"default":function(){return[]}},isWallet:{type:Boolean,"default":!0},supplyH5:{type:Boolean,"default":!1},urlArr:{type:Array,required:!1,"default":function(){return[]
}}},data:function(){return{is:"help-service"}},methods:{onClick:function(e){""!==this.computedUrlArr[e]&&i.redirect(this.computedUrlArr[e])},downWallet:function(){var e=encodeURIComponent(location.href),t="https://co.baifubao.com/content/mywallet/app/smsa.html?channel=1019486w&autoclick=1&from=jyxd&bdwallet_type=1&bdwallet_url_ios="+e;
location.href=t}},created:function(){var e=document.getElementsByClassName("body")[0];e.className=!this.isWallet&&this.supplyH5?"body footer-body-download":"body footer-body"},computed:{isWallet:function(){return Agent.OS.wallet
},computedTextArr:function(){return this.textArr.length?this.textArr:["客服中心"]},computedUrlArr:function(){return this.urlArr.length?this.urlArr:[this.jumpUrl]}}};var u='<div class="fin-{{is}}">\n    <div class="download" v-if="!isWallet &amp;&amp; supplyH5">\n        <section class="link">\n            <span v-touch:tap="downWallet()">\n                点击下载百度钱包，了解详细信息&gt;&gt;\n            </span>\n        </section>\n    </div>\n    <a>\n        <section class="help-service" id="help-service" :class="{\'onlyone\': computedTextArr.length === 1}">\n\n            <template v-for="(index, item) in computedTextArr">\n                <i class="line" v-if="index"></i>\n                <span v-touch:tap="onClick(index)">\n                    <i v-if="computedTextArr.length === 1" class="icon icon-headphone">\n                    </i>\n                    {{item}}\n                </span>\n            </template>\n        </section>\n    </a>\n</div>';
n&&n.exports&&(n.exports.template=u),t&&t.default&&(t.default.template=u),n.exports=t["default"]});
;define("hiloan:components/fin-cs/help-service/index",function(e,n,t){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var l=e("hiloan:components/fin-cs/help-service/help-service.vue"),o=i(l);
n.default=o.default,t.exports=n["default"]});
;define("hiloan:components/fin-cs/index",function(e,n,t){"use strict";function o(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var i=e("hiloan:components/fin-cs/credit-report/index"),l=o(i),c=e("hiloan:components/fin-cs/help-contact/index"),d=o(c),s=e("hiloan:components/fin-cs/help-content/index"),a=o(s),f=e("hiloan:components/fin-cs/help-list/index"),p=o(f),u=e("hiloan:components/fin-cs/help-service/index"),h=o(u);
n.default={creditReport:l.default,helpContact:d.default,helpContent:a.default,helpList:p.default,helpService:h.default},t.exports=n["default"]});
;define("hiloan:components/fin-fg/config/api-sdk",function(e,a,t){"use strict";Object.defineProperty(a,"__esModule",{value:!0}),a.default={getRefundList:"/hiloan/api/bff?method=getRefundList",getCityMenuList:"/hiloan/api/bff?method=getCityMenulist",getBankBranchList:"/hiloan/api/bff?method=getBankBranchList",getPaymentBankList:"/hiloan/api/bff?method=getPaymentBankList",getLocationCity:"/hiloan/api/bff?method=getLocationCity",searchCourseShop:"/hiloan/api/bff?method=search",getNearShop:"/hiloan/api/bff?method=getNearShop",getShopProject:"/hiloan/api/bff?method=getShopProject",getSchoolByCity:"/hiloan/api/bff?method=searchSchool",putList:"/hiloan/api/bff?method=putList",putFile:"/hiloan/api/bff?method=putFile",getFile:"/hiloan/api/bff?method=getFile",getPhotoFile:"/hiloan/api/bff?method=getPhotoFile",setPageData:"/hiloan/api/bff?method=setPageData",getNextPage:"/hiloan/api/bff?method=getNextPage",applyBasic:"/hiloan/api/bff?method=applyBasic",applySupply:"/hiloan/api/bff?method=applySupply",activateBasic:"/hiloan/api/bff?method=activateBasic",activateVerification:"/hiloan/api/bff?method=activateVerification",supplySubmit:"/hiloan/api/bff?method=supplySubmit",applyResult:"/hiloan/api/bff?method=applyResult",activateResult:"/hiloan/api/bff?method=activateResult",checkApply:"/hiloan/api/bff?method=checkApply",creditAddResult:"/hiloan/api/bff?method=creditAddResult",applyCancel:"/hiloan/api/bff?method=applyCancel",paymentEncash:"/hiloan/api/bff?method=paymentEncash",getLoanTrail:"/hiloan/api/bff?method=getLoanTrail",showContract:"/hiloan/api/bff?method=showContract",genContract:"/hiloan/api/bff?method=gencontract",getRepayList:"/hiloan/api/bff?method=getRepayList",getTransactionList:"/hiloan/api/bff?method=getTransactionList",getLoanResult:"/hiloan/api/bff?method=getLoanResult",getPreloanCalculator:"/hiloan/api/bff?method=getPreloanCalculator",getCashierRepay:"/hiloan/api/bff?method=getCashierRepay",getPartCashierPrepay:"/hiloan/api/bff?method=getPartCashierPrepay",getCashierConsolidatedRepay:"/hiloan/api/bff?method=getCashierConsolidatedRepay",getCurrentRepayment:"/hiloan/api/bff?method=getCurrentRepayment",getCurrentRepaymentClass:"/hiloan/api/bff?method=getCurrentRepaymentClass",getBatchAdvanceRepayCalculator:"/hiloan/api/bff?method=getBatchAdvanceRepayCalculator",batchCashierPrepay:"/hiloan/api/bff?method=batchCashierPrepay",getActiveCardquota:"/hiloan/api/bff?method=getActiveCardquota",getCashierPrepay:"/hiloan/api/bff?method=getCashierPrepay",checkOrder:"/hiloan/api/bff?method=checkOrder",openAutoRepay:"/hiloan/api/bff?method=getAutoRepayUrl",forceOpenAutoRepay:"/hiloan/api/bff?method=openAutoRepay",closeAutoRepay:"/hiloan/api/bff?method=closeAutoRepay",getAutoRepayStatus:"/hiloan/api/bff?method=getAutoRepayStatus",sendCode:"/hiloan/api/bff?method=sendSMSCode",verifySMSCode:"/hiloan/api/bff?method=verifySmsCode",getPassWordUrl:"/hiloan/api/bff?method=getPassWordUrl",checkContact:"/hiloan/api/bff?method=checkContact",idcardDiscern:"/hiloan/api/bff?method=idcardDiscern",getPageData:"/hiloan/api/bff?method=getPageData",getAuthInfo:"/hiloan/api/bff?method=getAuthInfo",getSpouseInfo:"/hiloan/api/bff?method=getSpouseInfo",getEnabledTpl:"/hiloan/api/bff?method=getEnabledTpl",signProtocol:"/hiloan/api/bff?method=signProtocol",licenseDiscern:"/hiloan/api/bff?method=licenseDiscern",setCache:"/hiloan/api/bff?method=setCache"},t.exports=a["default"]
});
;define("hiloan:components/fin-fg/config/form/input",function(e,i,l){"use strict";Object.defineProperty(i,"__esModule",{value:!0});var r=e("hiloan:components/fin-fg/util/index"),s=r.validator.custom,d=(s.name,s.email),t=s.idcard,n=s.phone,a=s.telArea,u=s.tel,m=s.isInt,c=(s.extend,s.bankcardLength),o=s.relationName,p=s.isNum;
i.default={companyName:{placeholder:"真实名称",title:"单位名称",fieldname:"companyName",fieldset:"companyName",rules:{required:{rule:!0,message:"请填写单位名称"}}},email:{placeholder:"个人电子邮箱地址",title:"电子邮箱",fieldname:"emailAddress",fieldset:"emailAddress",rules:{required:{rule:!0,message:"请填写邮箱"},email:d}},emailList:{inputType:"url",placeholder:"个人电子邮箱地址",title:"电子邮箱",fieldname:"emailAddress",fieldset:"emailAddress",rules:{required:{rule:!0,message:"请填写邮箱"},email:d},currentView:"uiInputList",list:["qq.com","163.com","sina.com","126.com","gmail.com","sohu.com","sogou.com","189.com","139.com","188.com","hotmail.com"]},idcard:{inputType:"text",placeholder:"中国大陆二代身份证号",title:"身份证号",fieldname:"comp2Prcid",fieldset:"comp2Prcid",rules:{required:{rule:!0,message:"请填写身份证号"},idcard:t}},name:{placeholder:"真实姓名",title:"真实姓名",fieldname:"comp2Name",fieldset:"comp2Name",rules:{required:{rule:!0,message:"请填写真实姓名"},relationName:o}},creditCard:{category:"large-input",tip:"填写信用卡有助于贷款额度",inputType:"tel",placeholder:"本人名下额度最高信用卡",title:"信用卡号",subtitle:"(选填)",fieldname:"creditCard",fieldset:"creditCard",rules:{bankcardLength:c}},monthlyIncome:{inputType:"tel",placeholder:"请输入月收入(元)",title:"月收入",fieldname:"personalIncome",fieldset:"personalIncome",rules:{required:{rule:!0,message:"请输入月收入"},isInt:m}},phone:{inputType:"tel",placeholder:"真实联系方式(可选)",title:"联系方式",fieldname:"phone",fieldset:"phone",rules:{phone:n}},tel:{inputType:"tel",placeholder:"请输入单位电话",title:"单位固话",fieldset:"officePhone",rules:{telArea:a,tel:u}},socialNo:{inputType:"tel",placeholder:"帐号号码",title:"帐号号码",fieldname:"socialNo",fieldset:"socialNo",rules:{required:{rule:!0,message:"请输入账号号码"}}},address:{category:"only-header",inputType:"text",header:"详细地址",placeholder:"详细地址",title:"详细地址",fieldname:"address",fieldset:"address",rules:{required:{rule:!0,message:"请输入详细地址"}}},selectContact:{inputType:"tel",placeholder:"选择联系人",title:"手机号码",fieldname:"relativePhone",fieldset:"relativePhone",rules:{required:{rule:!0,message:"无法访问通讯录，请输入联系人手机号码"},phone:n}},selectPhone:{inputType:"tel",placeholder:"选择联系人",title:"手机号码",rules:{required:{rule:!0,message:"无法访问通讯录，请输入联系人手机号码"},phone:n}},selectPhoneH5:{inputType:"tel",placeholder:"手机号码",title:"手机号码",maxLength:11,rules:{required:{rule:!0,message:"请输入联系人手机号码"},phone:n}},inputMoney:{inputType:"number",title:"&yen;"},drivingLicenseNumber:{inputType:"text",placeholder:"请输入号牌号码",fieldname:"drivingLicenseNumber",fieldset:"drivingLicenseNumber",title:"号牌号码",rules:{required:{rule:!0,message:"请输入号牌号码"}}},drivingLicenseType:{fieldname:"drivingLicenseType"},drivingLicenseName:{placeholder:"请输入所有人姓名",fieldname:"drivingLicenseName",fieldset:"drivingLicenseName",title:"所有人",rules:{required:{rule:!0,message:"请输入所有人姓名"}}},drivingLicenseAddress:{fieldname:"drivingLicenseAddress"},drivingLicenseFunction:{fieldname:"drivingLicenseFunction"},drivingLicenseBrand:{placeholder:"请输入品牌型号",fieldname:"drivingLicenseBrand",fieldset:"drivingLicenseBrand",title:"品牌型号",rules:{required:{rule:!0,message:"请输入品牌型号"}}},drivingLicenseNumber1:{placeholder:"请输入车辆识别代号",fieldname:"drivingLicenseNumber1",fieldset:"drivingLicenseNumber1",title:"车辆识别代号",rules:{required:{rule:!0,message:"请输入车辆识别代号"}}},drivingLicenseNumber2:{placeholder:"请输入发动机号码",fieldname:"drivingLicenseNumber2",fieldset:"drivingLicenseNumber2",title:"发动机号码",rules:{required:{rule:!0,message:"请输入发动机号码"}}},drivingLicenseRegDate:{placeholder:"请输入注册日期",fieldname:"drivingLicenseRegDate",fieldset:"drivingLicenseRegDate",title:"注册日期",rules:{required:{rule:!0,message:"请输入注册日期"}}},drivingLicenseOpenDate:{placeholder:"请输入发证日期",fieldname:"drivingLicenseOpenDate",fieldset:"drivingLicenseOpenDate",title:"发证日期",rules:{required:{rule:!0,message:"请输入发证日期"}}},hpHolderName:{placeholder:"请输入房产证件上的所有人姓名",fieldname:"hpHolderName",fieldset:"hpHolderName",title:"户主姓名",rules:{required:{rule:!0,message:"请输入户主姓名"}}},hpEstateLicense:{placeholder:"请输入房产证号或购房合同编号",fieldname:"hpEstateLicense",fieldset:"hpEstateLicense",title:"证件号码",rules:{required:{rule:!0,message:"请输入证件号码"}}},hpHouseSize:{inputType:"tel",placeholder:"请输入证件登记建筑面积",fieldname:"hpHouseSize",fieldset:"hpHouseSize",title:"房屋面积(m2)",rules:{required:{rule:!0,message:"请输入正确的面积"},isNum:p}},hpHouseAddress:{placeholder:"请输入证件登记实际房屋地址",fieldname:"hpHouseAddress",fieldset:"hpHouseAddress",title:"详细地址",rules:{required:{rule:!0,message:"请输入房屋详细地址"}}}},l.exports=i["default"]
});
;define("hiloan:components/fin-fg/config/form/select",function(e,t,l){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var u=e("hiloan:components/fin-fg/util/index"),i=u.validator.custom,r=(i.name,i.email,i.idcard,i.phone);
t.default={schoolDegree:{currentView:"uiSelect",title:"学历类别",placeholder:"请选择",rules:{required:{rule:!0,message:"请选择学历类别"}},fieldname:"degree",fieldset:"degree",selectTitle:"请选择学历类别",twoList:!0,list:[{text:"普通学历",value:"普通学历",sublist:[{text:"博士",value:"博士"},{text:"硕士",value:"硕士"},{text:"本科",value:"本科"},{text:"专科",value:"专科"},{text:"其他",value:"其他"}]},{text:"研究生学历",value:"研究生学历",sublist:[{text:"博士",value:"博士"},{text:"硕士",value:"硕士"},{text:"本科",value:"本科"},{text:"专科",value:"专科"},{text:"其他",value:"其他"}]},{text:"自考学历",value:"自考学历",sublist:[{text:"博士",value:"博士"},{text:"硕士",value:"硕士"},{text:"本科",value:"本科"},{text:"专科",value:"专科"},{text:"其他",value:"其他"}]},{text:"成人学历",value:"成人学历",sublist:[{text:"博士",value:"博士"},{text:"硕士",value:"硕士"},{text:"本科",value:"本科"},{text:"专科",value:"专科"},{text:"其他",value:"其他"}]},{text:"网络学历",value:"网络学历",sublist:[{text:"博士",value:"博士"},{text:"硕士",value:"硕士"},{text:"本科",value:"本科"},{text:"专科",value:"专科"},{text:"其他",value:"其他"}]},{text:"开放教育",value:"开放教育",sublist:[{text:"博士",value:"博士"},{text:"硕士",value:"硕士"},{text:"本科",value:"本科"},{text:"专科",value:"专科"},{text:"其他",value:"其他"}]}]},schoolTime:{currentView:"uiSelect",placeholder:"请选择",title:"入学时间",rules:{required:{rule:!0,message:"请选择入学时间"}},fieldname:"enrolTime",fieldset:"enrolTime",selectTitle:"请选择入学时间",confirm:!1,list:function(){for(var e=[],t=(new Date).getFullYear(),l=t;l>=1980;l--)e.push({text:""+l,value:""+l});
return e}()},schoolSelect:{title:"所在学校",placeholder:"请选择",border:"border-bottom",rules:{required:{rule:!0,message:"请选择所在学校"}},fieldname:"college",fieldset:"college"},companyPosition:{currentView:"uiSelect",placeholder:"选择职位",title:"工作职位",rules:{required:{rule:!0,message:"请选择公司类型"}},fieldname:"companyPosition",fieldset:"companyPosition",selectTitle:"请选择职业",confirm:!1,list:[{text:"董事长/总经理",value:"01"},{text:"总监/部门经理",value:"02"},{text:"一般主管/团队领导",value:"03"},{text:"资深员工",value:"04"},{text:"一般员工",value:"05"},{text:"一线操作人员 ",value:"06"},{text:"无职位 ",value:"06"}]},companyType:{currentView:"uiSelect",placeholder:"单位类型",title:"单位类型",rules:{required:{rule:!0,message:"请选择公司类型"}},fieldname:"companyType",fieldset:"companyType",selectTitle:"请选择单位类型",list:[{text:"政府机关和事业单位",value:"01"},{text:"国有企业",value:"02"},{text:"民营企业",value:"03"},{text:"外商独资企业",value:"04"},{text:"外商合资企业",value:"05"},{text:"无工作",value:"06"}]},companyYears:{currentView:"uiSelect",placeholder:"选择时间",title:"工作年限",rules:{required:{rule:!0,message:"请选择工作年限"}},fieldname:"workingYears",fieldset:"workingYears",selectTitle:"请选择工作年限",list:[{text:"无年限",value:"00"},{text:"1年以下",value:"01"},{text:"1-2年",value:"02"},{text:"2-5年",value:"03"},{text:"5-8年",value:"04"},{text:"8-10年",value:"05"},{text:"10年以上 ",value:"06"}]},currentCity:{currentView:"cityPicker",placeholder:"请选择地区",title:"所在地区",rules:{required:{rule:!0,message:"请选择地区"}},fieldname:"currentCity",fieldset:"currentCity"},bankProvince:{currentView:"uiSelect",placeholder:"选择",title:"开户银行所在省",rules:{required:{rule:!0,message:"请选择开户银行所在省"}},selectTitle:"请选择开户银行所在省"},bankCity:{currentView:"uiSelect",placeholder:"选择",title:"开户银行所在市",rules:{required:{rule:!0,message:"请选择开户银行所在市"}},selectTitle:"请选择开户银行所在市"},companyCity:{currentView:"cityPicker",placeholder:"请选择地区",title:"所在地区",rules:{required:{rule:!0,message:"请选择地区"}},fieldname:"companyCity",fieldset:"companyCity"},eduLevel:{currentView:"uiSelect",placeholder:"选择",title:"教育水平",rules:{required:{rule:!0,message:"请选择教育水平"}},fieldname:"educationLevel",fieldset:"educationLevel",selectTitle:"请选择教育水平",list:[{text:"小学/初中",value:"小学/初中"},{text:"高中/中专/技校/职高",value:"高中/中专/技校/职高"},{text:"大专",value:"大专"},{text:"大学本科",value:"大学本科"},{text:"硕士/博士/博士后",value:"硕士/博士/博士后"},{text:"文盲/半文盲",value:"文盲/半文盲"}]},selectContact:{title:"手机号码",placeholder:"选择联系人",border:"border-bottom",rules:{required:{rule:!0,message:"无法访问通讯录，请输入联系人手机号码"},phone:r},fieldname:"college",fieldset:"college"},installment:{title:"选择分期",tip:"首次还款时间:付款成功后次月15日前",placeholder:"选择分期",rules:{required:{rule:!0,message:"请选择一个分期方案"}},fieldname:"periods",fieldset:"periods"},kinship:{currentView:"uiSelect",placeholder:"选择关系",title:"所属关系",rules:{required:{rule:!0,message:"请选择联系人所属关系"}},fieldname:"relativeShip",fieldset:"relativeShip",list:[{text:"我的父母",value:"父母"},{text:"我的子女 (18岁以上)",value:"子女"},{text:"我的配偶",value:"配偶"},{text:"兄弟姐妹",value:"兄弟姐妹"}]},relationship:{currentView:"uiSelect",placeholder:"选择关系",title:"所属关系",rules:{required:{rule:!0,message:"请选择联系人所属关系"}},fieldname:"friendShip",fieldset:"friendShip",list:[{text:"同学",value:"同学"},{text:"同事",value:"同事"},{text:"朋友",value:"朋友"}]},selectPhone:{title:"手机号码",icon:"icon-phonebook",placeholder:"选择联系人",rules:{required:{rule:!0,message:"请选择联系人"}}}},l.exports=t["default"]
});
;define("hiloan:components/fin-fg/config/form/index",function(e,n){"use strict";function o(e){if(e&&e.__esModule)return e;var n={};if(null!=e)for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(n[o]=e[o]);
return n.default=e,n}function t(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0}),n.select=n.input=void 0;var i=e("hiloan:node_modules/underscore/underscore"),f=t(i),c=e("hiloan:components/fin-fg/config/form/input"),r=o(c),u=e("hiloan:components/fin-fg/config/form/select"),l=o(u),s=e("hiloan:app/static/config/form/index"),a=function(e,n){Object.keys(n).forEach(function(o){e[o]=e[o]?f.default.extend({},e[o],n[o]):n[o]
})};a(r,s.input),a(l,s.select);n.input=r,n.select=l});
;define("hiloan:components/fin-fg/config/index",function(e,n,i){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var t=e("hiloan:components/fin-fg/config/form/index");n.default={input:t.input,select:t.select},i.exports=n["default"]
});
;define("hiloan:components/fin-fg/directive/log/index",function(e,n,t){"use strict";function o(e){return e&&e.__esModule?e:{"default":e}}var i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e
}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},d=e("hiloan:node_modules/vue/dist/vue.common"),l=o(d),r=e("hiloan:node_modules/hammerjs/hammer"),s=o(r),f=e("hiloan:components/fin-fg/track/log"),a=o(f);
l.default.directive("log",{bind:function(){this.el.hammer||(this.el.hammer=new s.default(this.el)),this.mc=this.el.hammer},update:function(e){try{if(window.G&&window.G.constants&&window.G.constants.autoSendBfbLog){var n=void 0,t=void 0;
if("string"==typeof e)n=e;else{if("object"!==("undefined"==typeof e?"undefined":i(e)))return void console.warn("[v-log directive] 值必须为字符串或对象, 获取值为"+("undefined"==typeof e?"undefined":i(e))+":"+e);n=e.eventKey,t=e.eventValue
}if(void 0===n)return void console.warn("[v-log directive] 缺少日志事件描述的key");this.handler&&this.mc.off("tap",this.handler),this.handler=function(){a.default.sendBfbAction(n,["","","",t,""])},this.mc.on("tap",this.handler)
}}catch(o){}},unbind:function(){this.handler&&this.mc.off("tap",this.handler),Object.keys(this.mc.handlers).length||(this.mc.destroy(),this.el.hammer=null)}}),t.exports={}});
;define("hiloan:components/fin-fg/error-reporter/index",function(){"use strict";!function(){function n(n,e,r,i){var a=n&&n.message||n;if(!t(a)){var d="credit_loan_"+Date.now(),c=window[d]=new Image;c.src=o+"/fms/api/reportError?msg="+a+"\n        &file="+e+"&line="+r+"&col="+i+"&t="+d,delete window[d]
}}if(window.G&&window.G.constants&&window.G.constants.autoReportError){var t=function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return[/Unexpected Number/i].some(function(t){return n.match(t)
})},o=function(){var n=window.G&&window.G.constants&&window.G.constants.env||"online",t={online:"https://front.baidu.com",sandbox:"https://qyfront.baidu.com",qa:"http://testqa.fbu_node.otp.baidu.com",rd:"http://cp01-front.epc.baidu.com:8086"};
return t[n]}();window.G.creditLoanErrReporter=n,window.addEventListener("error",function(t,o,e,r){n(t,o,e,r)})}}()});
;define("hiloan:components/fin-fg/filter/datetime",function(e){"use strict";function t(e){return e&&e.__esModule?e:{"default":e}}var n=e("hiloan:node_modules/vue/dist/vue.common"),i=t(n),o=e("hiloan:components/fin-fg/util/format/datetime"),f=t(o);
i.default.filter("formatDateTime",function(){return f.default.apply(void 0,arguments)})});
;define("hiloan:components/fin-fg/filter/money",function(n){"use strict";function e(n){return n&&n.__esModule?n:{"default":n}}var o=n("hiloan:node_modules/vue/dist/vue.common"),t=e(o),f=n("hiloan:components/fin-fg/util/format/money"),i=e(f);
t.default.filter("formatMoney",function(){return i.default.apply(void 0,arguments)})});
;define("hiloan:components/fin-fg/filter/index",function(n){"use strict";n("hiloan:components/fin-fg/filter/datetime"),n("hiloan:components/fin-fg/filter/money")});
;define("hiloan:components/fin-fg/mixins/input",function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={props:{type:{type:String,"default":"text",coerce:function(e){return e?e:"text"
}},placeholder:{type:String,"default":""},inputCss:{type:String,"default":""},edit:{type:Boolean,"default":!1},readonly:Boolean,min:Number,max:Number,maxlength:Number,value:{type:String,"default":"",coerce:function(e){return e+""
}},fieldname:{type:String,"default":""},rules:{type:Object,"default":function(){return{}}},recordInput:{type:Boolean,"default":!0},base64:{type:Boolean,"default":!1},format:{type:Function}}},n.exports=t["default"]
});
;define("hiloan:components/fin-fg/mixins/input-base-mixins",function(t,e,n){"use strict";function o(t){return t&&t.__esModule?t:{"default":t}}Object.defineProperty(e,"__esModule",{value:!0});var a=t("hiloan:components/fin-fg/mixins/input"),l=o(a);
e.default=[l.default,{props:{category:{type:String,"default":""},border:{type:[Boolean,String],"default":!1},borderTop:{type:Boolean,"default":!1},borderBottom:{type:Boolean,"default":!1},header:{type:String,"default":""},icon:{type:Boolean,"default":!1},title:{type:String,"default":""},titleWidth:{type:String,"default":""},subtitle:{type:String,"default":""},buttonText:{type:String,"default":"按钮"},edit:{type:Boolean,"default":!1},disabled:Boolean,fieldset:{type:String,"default":""},tip:{type:String,"default":""},topError:{type:String,"default":""},topLoading:{type:Boolean,"default":!1},async:{type:Object,"default":function(){return{}
}},collect:{type:Boolean,"default":!0},format:{type:Function},repairValidation:{type:Boolean,"default":!1}}}],n.exports=e["default"]});
;define("hiloan:components/fin-fg/mixins/select",function(e,t,l){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={props:{icon:{type:String,"default":""},border:{type:String,"default":""},size:{type:String,"default":""},title:{type:String,"default":""},placeholder:{type:String,"default":""},disabled:{type:Boolean,"default":!1},tip:{type:String,"default":""},value:{type:String,"default":""},text:{type:String,"default":""},fieldname:{type:String,"default":""},rules:{type:Object,"default":function(){return{}
}},topError:{type:Boolean,"default":!1},recordInput:{type:Boolean,"default":!0},base64:{type:Boolean,"default":!1},async:{type:Object,"default":function(){return{}}},titleWidth:{type:String,"default":""}}},l.exports=t["default"]
});
;define("hiloan:components/fin-fg/mixins/input-valid-notice",function(o,t,n){"use strict";function e(o){return o&&o.__esModule?o:{"default":o}}Object.defineProperty(t,"__esModule",{value:!0});var i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(o){return typeof o
}:function(o){return o&&"function"==typeof Symbol&&o.constructor===Symbol&&o!==Symbol.prototype?"symbol":typeof o},u=o("hiloan:node_modules/vue/dist/vue.common"),l=e(u),f=o("hiloan:components/fin-fg/util/index");
t.default={methods:{invalidToValid:function(){var o=this;l.default.nextTick(function(){var t=void 0;"object"===i(o.$validation)&&(t=o.$validation.modified),t&&o.focusToBlur&&f.eventBus.$emit("invalidToValid",o)
})}}},n.exports=t["default"]});
;define("hiloan:components/fin-fg/mixins/index",function(n,i,e){"use strict";function t(n){return n&&n.__esModule?n:{"default":n}}Object.defineProperty(i,"__esModule",{value:!0});var o=n("hiloan:components/fin-fg/mixins/input"),f=t(o),s=n("hiloan:components/fin-fg/mixins/input-base-mixins"),u=t(s),l=n("hiloan:components/fin-fg/mixins/select"),a=t(l),d=n("hiloan:components/fin-fg/mixins/input-valid-notice"),c=t(d);
i.default={input:f.default,inputBaseMixins:u.default,select:a.default,inputValidNotice:c.default},e.exports=i["default"]});
;define("hiloan:components/fin-fg/plugin/input/adjust-title-width",function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{select:!0},e=arguments[1],i=t.width,n=t.select;
e=e||this;var l=e.$root.$children,u=["rm-input","rm-input-base","ui-select-input"];n||u.pop();var r=function c(t,e){t.forEach(function(t){-1!==u.indexOf(t.is)?e(t):c(t.$children,e)})},s=0,o=0;r(l,function(t){t.title&&t.title.length>s&&(s=t.title.length),t.subtitle&&t.subtitle.length>o&&(o=t.subtitle.length)
}),4===o?o=32:3===o?o=18:o>4&&(o=36);var a=17*s+o;i&&(a=i),r(l,function(t){t.titleWidth||(t.titleWidth=a/37.5+"rem")})},i.exports=e["default"]});
;define("hiloan:components/fin-fg/plugin/index",function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var u=e("hiloan:components/fin-fg/plugin/input/adjust-title-width"),d=i(u);
t.default={adjustTitleWidth:d.default},n.exports=t["default"]});
;define("hiloan:components/fin-fg/track/wallet",function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"action";
_bfbStatistic(e,t)},n.exports=e["default"]});
;define("hiloan:components/fin-fg/util/format/yuan2fen",function(e,r,n){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(e){var r=+e;if(isNaN(r))throw new Error("Error: `"+e+"` is not available!");
var n=r/100;return 0>n?0:n},n.exports=r["default"]});
;define("hiloan:components/fin-fg/util/native/getNativeFields",function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}function o(e,t,n){function i(e,t,n){var i="callPhoneInfo";
Agent.invoke(i,e,function(o,a){r(a,e,t,n,i)})}var o={};Object.keys(e).forEach(function(e){o[e]=null}),setTimeout(function(){i(o,t,n)},500)}function r(e,t,n,i,o){function r(e,t){var n={};for(var i in e)if(t&&e.hasOwnProperty(i)){if("safeSdk"===i){n.secureFactor=t&&t.safeSdk||"";
continue}if("wifi"===i){n.wifiFinger=t&&t.wifi;continue}if("location"===i){n.longitude=t.longitude||"",n.latitude=t.latitude||"";continue}if("method_name"===i)continue;n[i]=t[i]||""}else n[i]="";return n
}var u=e&&0===parseInt(e.result,10)&&e.cnt&&e.cnt.data,l=u&&e.cnt.data||{};if(u){l=c.default.decode(l);try{l=JSON.parse(l)}catch(f){l={}}if(t.hasOwnProperty("location"))try{var d=JSON.parse(l.location);
l.longitude=d.longitude,l.latitude=d.latitude}catch(f){l.longitude=l.location&&l.location.longitude,l.latitude=l.location&&l.location.latitude}}else s.default.sendSdkError(o,l);if(t.hasOwnProperty("wifi")){var g=l&&l.wifi&&l.wifi.wifi_scan||[];
Array.isArray(g)&&(l.wifi=JSON.stringify(g.splice(0,10)))}t.hasOwnProperty("deviceSource")&&(l.deviceSource=a()?3:2),t.hasOwnProperty("sourceFlag")&&(l.sourceFlag=a()?3:2),t.hasOwnProperty("explorerUserAgent")&&(l.explorerUserAgent=navigator.userAgent);
var p=r(t,l);n(p)}function a(){return/BaiduWallet/i.test(navigator.userAgent)}Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return new u.Promise(function(t,n){e&&a()?Agent&&Agent.invoke?o(e,t,n):n(null):t({})
})};var u=e("hiloan:node_modules/es6-promise/dist/es6-promise"),l=e("hiloan:components/fin-fg/util/base64/index"),c=i(l),f=e("hiloan:components/fin-fg/track/log"),s=i(f);n.exports=t["default"]});
;define("hiloan:components/fin-fg/util/store/server",function(){"use strict"});
;define("hiloan:components/fin-fg/util/store/session",function(){"use strict"});
;define("hiloan:components/fin-fk/free-repay/free-repay.vue",function(t,e,i){"use strict";function s(t){return t&&t.__esModule?t:{"default":t}}Object.defineProperty(e,"__esModule",{value:!0}),t("hiloan:components/fin-fg/filter/index");
var n=t("hiloan:components/fin-ui/ui-mask/index"),a=s(n);e.default={props:{show:{type:Boolean,"default":!1},min:{type:Number,"default":500},max:{type:Number,required:!0,"default":500},discount:{type:Number,"default":0},tip:{type:String,"default":"您可以部分还款，但剩余部分仍需在本期内还完"},btnText:{type:String,"default":"确定还款"},title:{type:String,"default":"本期还款"},label:{type:String,"default":"本期应还"}},data:function(){return{is:"free-repay",value:this.max}
},attached:function(){this.calcStepPix()},methods:{open:function(t){var e=this;Object.keys(t).forEach(function(i){["min","max","discount","tip","btnText","title","label"].indexOf(i)>-1&&(e[i]=t[i])}),this.show=!0
},close:function(){this.show=!1},calcStepPix:function(){var t=getComputedStyle(document.querySelector("body")),e=parseInt(t.width,10),i=getComputedStyle(this.$els.track);this.marginLeft=parseInt(i["margin-left"],10);
var s=getComputedStyle(this.$els.slider);this.sliderWidth=parseInt(s.width,10),this.trackWidth=e-2*this.marginLeft-this.sliderWidth;var n=this.max-this.min;this.step=this.max>1e5?1e4:1e3,this.stepCount=n/this.step,this.stepInt=Math.floor(this.stepCount),this.stepPixel=this.trackWidth/this.stepCount
},evtHander:function(t){t.preventDefault();var e=void 0,i=t.changedTouches[0].clientX-this.marginLeft,s=i-this.sliderWidth/2,n=s/this.stepPixel,a=Math.floor(n),h=void 0;this.stepInt<1?(h=s/this.trackWidth,h>.5?(s=this.trackWidth,e=this.max):(s=0,e=this.min)):(h=n-a,a>=this.stepInt?(s=this.trackWidth,e=this.max):h>.5?(s=(a+1)*this.stepPixel,e=this.min+(a+1)*this.step):(s=a*this.stepPixel,e=this.min+a*this.step)),this.moveTo(s),this.updateMoney(e)
},moveTo:function(t){t=Math.max(0,t),t=Math.min(this.trackWidth,t),this.$els.slider.style.left=t+"px"},updateMoney:function(t){t=Math.max(this.min,t),t=Math.min(this.max,t),this.value=t>this.min&&t<this.max?100*Math.round(t/100):t
},handleRepay:function(){1!==this.$els.paybtn.getAttribute("clicked")&&(this.$els.paybtn.setAttribute("clicked",1),this.show=!1,this.$dispatch("freeRepay",this.value))}},watch:{min:function(){this.calcStepPix()
},max:function(t){this.value=t,this.calcStepPix()},show:function(t){t&&(this.value=this.max,this.moveTo(this.trackWidth))}},components:{uiMask:a.default}};var h='<section class="fin-{{ is }}" v-show="show">\n\n    <ui-mask :show="show" @tap="close"></ui-mask>\n\n    <div class="main">\n        <div class="top">\n            <span class="text">{{title}}</span>\n            <div class="icon icon-close" v-touch:tap="close"></div>\n        </div>\n\n        <div class="money">\n            <span>{{value | formatMoney \'en\' 2 \'.\' \',\'}}</span>\n        </div>\n\n        <div class="range" v-el:track="" @touchend="evtHander">\n            <span v-bind:class="{\'highlight\':value!==max}" v-el:slider="" @touchmove="evtHander"></span>\n        </div>\n\n        <p class="mark">\n            <em class="left">{{min | formatMoney \'en\' 2 \'.\' \',\'}}</em>\n            <em class="right">本期应还{{max | formatMoney \'en\' 2 \'.\' \',\'}}</em>\n        </p>\n\n        <p class="info" v-show="discount">\n            优惠券已减免{{discount | formatMoney \'en\' 2 \'.\' \',\'}}\n        </p>\n\n        <div class="tip" v-el:tip="" v-show="value !== max" transition="fade">{{tip}}</div>\n\n        <div class="btn blue" v-el:paybtn="" v-touch:tap="handleRepay">{{btnText}}</div>\n    </div>\n\n</section>';
i&&i.exports&&(i.exports.template=h),e&&e.default&&(e.default.template=h),i.exports=e["default"]});
;define("hiloan:components/fin-fk/free-repay/index",function(e,n,f){"use strict";function t(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var r=e("hiloan:components/fin-fk/free-repay/free-repay.vue"),o=t(r);
n.default=o.default,f.exports=n["default"]});
;define("hiloan:components/fin-fk/installment-detail-popup/installment-detail-popup.vue",function(i,n,e){"use strict";function t(i){return i&&i.__esModule?i:{"default":i}}Object.defineProperty(n,"__esModule",{value:!0});
var s=i("hiloan:node_modules/vue/dist/vue.common"),o=t(s),a=i("hiloan:node_modules/iscroll/build/iscroll"),l=(t(a),i("hiloan:node_modules/vue-touch/vue-touch")),m=t(l),d=i("hiloan:node_modules/object-assign/index"),u=t(d),p=i("hiloan:app/static/config/api"),c=(t(p),i("hiloan:components/fin-ui/ui-mask/index")),r=t(c);
o.default.use(m.default),n.default={data:function(){return{is:"installment-detail-popup",paidTime:0,title:"分期详情",money:0,unpaid:0,interest:0,paidInterest:0,list:[],maskShow:!1,mainShow:!1}},methods:{hideUi:function(){this.mainShow=!1,this.maskShow=!1
},showUi:function(i){u.default(this,i),this.mainShow=!0,this.maskShow=!0}},computed:{currentPeriod:function(){var i=this.list.filter(function(i){return 122===i.bStatus})[0];return i&&i.period}},components:{uiMask:r.default}};
var f="<section class=\"fin-{{is}}\" v-show=\"mainShow\">\n    <div class=\"fin-{{ is }}-title\">\n        <i class=\"close icon icon-close\" @click=\"hideUi\"></i>\n        <h3>{{title}}</h3>\n        <p v-show=\"unpaid\">待还金额{{unpaid | formatMoney 'en' 2 '.' ','}}<span></span></p>\n    </div>\n    <div class=\"fin-{{ is }}-content\">\n        <ul>\n            <li v-bind:class=\"{ 'done': item.status==4, 'overdue': item.status==7, 'current': item.period==currentPeriod }\" v-for=\"(index, item) in list\">\n                <div class=\"left-item\">\n                    <p class=\"high-light\">\n                        <font class=\"num f20\">{{item.period}}</font>期\n                        <i class=\"status-icon\"></i>\n                    </p>\n                    <p>{{item.dueDate | formatDateTime}}</p>\n                </div>\n                <div class=\"right-item\">\n                    <p class=\"high-light num\">{{item.money| formatMoney 'en' 2 '.' ',' }}</p>\n                    <p>本金{{item.prinamt - item.paidPrincipal| formatMoney 'en' 2 '.' ',' }}\n                        <span v-if=\"(item.fee - item.paidFee) > 0\">\n                            + 手续费{{item.fee - item.paidFee | formatMoney 'en' 2 '.' ',' }}\n                        </span>\n                        <span v-if=\"(item.discount - item.paidDiscount) > 0\">\n                            - 优惠券{{item.discount - item.paidDiscount | formatMoney 'en' 2 '.' ',' }}\n                        </span>\n                        + 利息{{item.intamt - item.paidInterest | formatMoney 'en' 2 '.' ',' }}\n                        <span v-if=\"(item.interest-item.paidPenalty) > 0\">\n                            + <font class=\"high-light\">罚息{{item.interest-item.paidPenalty | formatMoney 'en' 2 '.' ',' }}</font>\n                        </span>\n                    </p>\n                </div>\n            </li>\n        </ul>\n    </div>\n</section>\n<ui-mask :show.sync=\"maskShow\"></ui-mask>";
e&&e.exports&&(e.exports.template=f),n&&n.default&&(n.default.template=f),e.exports=n["default"]});
;define("hiloan:components/fin-fk/installment-detail-popup/index",function(e,t,n){"use strict";function l(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var i=e("hiloan:components/fin-fk/installment-detail-popup/installment-detail-popup.vue"),o=l(i);
t.default=o.default,n.exports=t["default"]});
;define("hiloan:components/fin-fk/installment-detail/installment-detail.vue",function(t,i,n){"use strict";function e(t){return t&&t.__esModule?t:{"default":t}}Object.defineProperty(i,"__esModule",{value:!0});
var a=t("hiloan:node_modules/vue/dist/vue.common"),s=e(a),o=t("hiloan:node_modules/iscroll/build/iscroll"),l=(e(o),t("hiloan:node_modules/vue-touch/vue-touch")),m=e(l),d=t("hiloan:node_modules/object-assign/index"),r=e(d),u=t("hiloan:app/static/config/api"),p=(e(u),t("hiloan:components/fin-ui/ui-mask/index")),f=e(p);
s.default.use(m.default),i.default={data:function(){return{is:"installment-detail",paidTime:0,money:0,unpaid:0,list:[],show:!1,main:!1}},computed:{unpaid:function h(){var t=this.list,h=0,i=!0,n=!1,e=void 0;
try{for(var a,s=t[Symbol.iterator]();!(i=(a=s.next()).done);i=!0){var o=a.value;4!==o.status&&(h+=o.total-o.repaidTotal)}}catch(l){n=!0,e=l}finally{try{!i&&s.return&&s.return()}finally{if(n)throw e}}return h
}},methods:{hideUi:function(){this.main=!1,this.show=!1},showUi:function(t){r.default(this,t),this.main=!0,this.show=!0}},components:{uiMask:f.default}};var c='<section class="fin-{{is}}" v-show="main">\n    <div class="fin-{{ is }}-title">\n        <i class="close icon icon-close" v-touch:tap.stop="hideUi"></i>\n        <h3>分期详情</h3>\n        <p>{{paidTime | formatDateTime}} 借款¥{{money | formatMoney}} <span class="ml17">待还总额¥{{unpaid | formatMoney}}<span></span></span></p>\n    </div>\n    <div class="fin-{{ is }}-content">\n        <ul>\n            <li v-bind:class="{ \'done\': item.status==4, \'overdue\': item.status==7, \'current\': item.period==currentPeriod }" v-for="(index, item) in list">\n                <div class="left-item">\n                    <p class="high-light">\n                        <font class="f20">{{item.period}}</font>期\n                        <i class="status-icon"></i>\n                    </p>\n                    <p>{{item.dueDateTime | formatDateTime}}</p>\n                </div>\n                <div class="right-item" v-if="item.status==4">\n                    <p class="high-light">¥{{item.total| formatMoney}}</p>\n                    <p>本金{{item.prinamt| formatMoney}} + 利息{{item.intamt | formatMoney}}\n                        <span v-if="(item.discount) > 0">\n                            - 优惠券{{item.discount | formatMoney}}\n                        </span>\n                        <span v-if="(item.interest-item.repaidPenalty) > 0">\n                            + <font class="high-light">罚息{{item.interest | formatMoney}}</font>\n                        </span>\n                    </p>\n                </div>\n                <div class="right-item" v-else="">\n                    <p class="high-light">¥{{item.total - item.repaidTotal | formatMoney}}</p>\n                    <p>本金{{item.prinamt - item.repaidPrinciple| formatMoney}}\n                        + 利息{{item.intamt - item.repaidInterest | formatMoney}}\n                        <span v-if="(item.discount) > 0">\n                            - 优惠券{{item.discount | formatMoney}}\n                        </span>\n                        <span v-if="(item.interest-item.repaidPenalty) > 0">\n                            + <font class="high-light">罚息{{item.interest - item.repaidPenalty | formatMoney}}</font>\n                        </span>\n                    </p>\n                </div>\n            </li>\n        </ul>\n    </div>\n</section>\n<ui-mask :show.sync="show"></ui-mask>';
n&&n.exports&&(n.exports.template=c),i&&i.default&&(i.default.template=c),n.exports=i["default"]});
;define("hiloan:components/fin-fk/installment-detail/index",function(e,t,n){"use strict";function l(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var i=e("hiloan:components/fin-fk/installment-detail/installment-detail.vue"),a=l(i);
t.default=a.default,n.exports=t["default"]});
;define("hiloan:components/fin-fk/iou-detail-popup/iou-detail-popup.vue",function(i,n,o){"use strict";function s(i){return i&&i.__esModule?i:{"default":i}}Object.defineProperty(n,"__esModule",{value:!0});
var e=i("hiloan:node_modules/vue/dist/vue.common"),t=s(e),l=i("hiloan:node_modules/iscroll/build/iscroll"),a=(s(l),i("hiloan:node_modules/vue-touch/vue-touch")),u=s(a),d=i("hiloan:node_modules/object-assign/index"),c=s(d),p=i("hiloan:components/fin-fg/util/index"),h=i("hiloan:components/fin-ui/ui-mask/index"),f=s(h);
t.default.use(u.default),n.default={data:function(){return{is:"iou-detail-popup",list:[],maskShow:!1,mainShow:!1}},methods:{jump:function(i){p.redirect(i)},hideUi:function(){this.mainShow=!1,this.maskShow=!1
},showUi:function(i){c.default(this,{list:i}),this.mainShow=!0,this.maskShow=!0}},components:{uiMask:f.default}};var m='<section class="fin-{{is}}" v-show="mainShow">\n    <div class="fin-{{ is }}-title">\n        <i class="close icon icon-close" v-touch:tap.stop="hideUi"></i>\n        <h3>借款明细</h3>\n    </div>\n    <div class="fin-{{ is }}-content">\n        <ul class="group" v-for="(i, group) in list">\n            <li v-for="k in group">\n                <span class="dc-light">{{k.name}}</span>\n                <span class="link" v-if="k.link" @click="jump(k.link)">查看</span>\n                <span class="dc-deep" v-else="">{{k.value}}</span>\n            </li>\n        </ul>\n    </div>\n</section>\n<ui-mask :show.sync="maskShow"></ui-mask>';
o&&o.exports&&(o.exports.template=m),n&&n.default&&(n.default.template=m),o.exports=n["default"]});
;define("hiloan:components/fin-fk/iou-detail-popup/index",function(e,o,u){"use strict";function t(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(o,"__esModule",{value:!0});var i=e("hiloan:components/fin-fk/iou-detail-popup/iou-detail-popup.vue"),n=t(i);
o.default=n.default,u.exports=o["default"]});
;define("hiloan:components/fin-fk/prepayment/prepayment.vue",function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default={props:{datasource:{type:Object,"default":function(){return{pripal:"",totalFee:"",overdueFee:""}
}}},data:function(){return{is:"prepayment",durationPeriod:""}},methods:{},components:{},created:function(){var t=this.datasource;this.durationPeriod=t.firstBills===t.lastBills?"第"+t.firstBills+"期":t.firstBills+"-"+t.lastBills+"期"
}};var a='<section class="fin-{{is}}">\n    <h6 class="tips"><span class="icon icon-info-revert"></span> 提前结清还款无法撤销</h6>\n    <div class="container">\n        <div class="header"><div class="inner"></div></div>\n        <div class="detail">\n            <div class="title">\n                <h2 v-html="datasource.title"></h2>\n                <span>\n                    贷款总额 {{ datasource.creditMoney | formatMoney \'cn\' }} &nbsp; | &nbsp; 贷款期数 <span v-html="datasource.lastBills"></span>期\n                </span>\n            </div>\n            <div class="list">\n                <table width="100%">\n                    <tbody>\n                    <tr>\n                        <td>剩余({{ durationPeriod }})贷款本金</td>\n                        <td>{{ datasource.pripal | formatMoney \'en\' }}</td>\n                    </tr>\n                    <tr>\n                        <td>提前还款手续费</td>\n                        <td>{{ datasource.violate | formatMoney \'en\' }}</td>\n                    </tr>\n                    <tr v-if="datasource.charges">\n                        <td>未还手续费</td>\n                        <td>{{ datasource.charges | formatMoney \'en\' }}</td>\n                    </tr>\n                    </tbody>\n                    <tfoot>\n                    <tr>\n                        <td>提前还款总额</td>\n                        <td class="total">{{ datasource.paidOffAmout | formatMoney \'en\' }}</td>\n                    </tr>\n                    </tfoot>\n                </table>\n            </div>\n        </div>\n        <div class="footer"></div>\n    </div>\n</section>';
e&&e.exports&&(e.exports.template=a),n&&n.default&&(n.default.template=a),e.exports=n["default"]});
;define("hiloan:components/fin-fk/prepayment/index",function(e,n,t){"use strict";function f(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var o=e("hiloan:components/fin-fk/prepayment/prepayment.vue"),u=f(o);
n.default=u.default,t.exports=n["default"]});
;define("hiloan:components/fin-fk/repay/repay.vue",function(t,a,o){"use strict";function n(t){return t&&t.__esModule?t:{"default":t}}Object.defineProperty(a,"__esModule",{value:!0});var e=t("hiloan:node_modules/vue/dist/vue.common"),i=(n(e),t("hiloan:app/static/config/api")),s=n(i),c=t("hiloan:node_modules/object-assign/index"),l=(n(c),t("hiloan:components/fin-ui/ui-dialog/index")),u=n(l),d=t("hiloan:node_modules/query-string/index"),r=(n(d),t("hiloan:components/fin-fg/util/store/local")),f=n(r),p=t("hiloan:components/fin-fg/util/index");
a.default={is:"repay",data:function(){return{config:{type:"",param:{}},backPathname:"fucs"===location.pathname.split("/")[1]?"/fucs/repayment/repaymentResult":"/fms/tpl/repay/result",pollCount:0,pollCountMax:6,showPcPayDialog:!1}
},methods:{checkOrder:function(){this.wait({tid:this.tradeInfo.newTid,scene:"MAIN"})},hidePcPayDialog:function(){this.showPcPayDialog=!1},gotoPcPay:function(){var t="https://www.baifubao.com/wallet-fe/0/large_payment/0",a=p.query.withMergeQuery(this.backPathname,{tid:this.tradeInfo.newTid,status:"success"}),o="https://www.baifubao.com/cashdesk/pc/largepayresult",n=encodeURIComponent("https://icash.baidu.com/cloan/trans/bankquota");
window.location.href=t+"?page_title=大额支付&has_steps=1&cashdesk_params="+encodeURIComponent(this.tradeInfo.orderInfo)+"&return_url="+encodeURIComponent(a)+"&page_url="+encodeURIComponent(o)+"&quata_url="+n
},wait:function(t){var a=this;setTimeout(function(){return a.pollCount++,a.pollCount>a.pollCountMax?void(a.pollCount=0):void s.default.checkOrder(t,{"x-message":a.xMessage}).then(function(o){var n=o.data&&o.data.tstatus;
2===n?p.redirect(a.backPathname,{tid:t.tid,status:"success"}):3===n?p.redirect(a.backPathname,{tid:t.tid,status:"fail"}):5===n&&a.wait(t)}).catch(function(t){console.log(t)})},2500)},bankLimitAmount:function(){var t=this;
f.default.setItem("orderInfo",this.totalAmount),p.native.get("ua",{}).then(function(a){s.default.getActiveCardquota({ua:a.data.split(",")[0]}).then(function(a){var o=+a.data.quotaInfo.singleLimit;+t.totalAmount>o&&-1!==o?t.showPcPayDialog=!0:t.doRepayNa(t.tradeInfo.orderInfo)
})}).catch(function(t){console.log(t)})},doRepayNa:function(){var t=this;p.native.get("doPay",this.tradeInfo.orderInfo).then(function(a){0===parseInt(a.data.statecode,10)&&t.checkOrder()})},repayStart:function(){var t=this;
return s.default[this.config.payType](this.config.param).then(function(a){return a.data&&null!=a.data.orderInfo?(t.tradeInfo=a.data,void t.tradeInfo.orderInfo.split("&").forEach(function(a){/total_amount/i.test(a)&&(t.totalAmount=a.split("=")[1],t.bankLimitAmount())
})):void 0}).catch(function(t){1!==t.errno&&console.log(t)}),!0}},created:function(){var t=this;this.$root.$on("repayStart",function(a){t.config=a,t.repayStart()})},components:{uiDialog:u.default}};var h='<div class="fin-{{ is }}">\n    <!-- PC还款弹窗 -->\n    <ui-dialog type="dialog" :head="false" :show.sync="showPcPayDialog">\n        <div class="dialog-pay-pc" slot="content">\n            <div class="pc-pay-close icon icon-close" v-touch:tap="hidePcPayDialog"></div>\n\n            <section class="pc-pay-logo">\n            </section>\n\n            <section class="pc-pay-head">\n                <h2>还款金额超过<br>手机端支付限额</h2>\n                <p>请前往百度钱包电脑版，完成支付</p>\n            </section>\n\n            <div class="fin-ui-button full" v-touch:tap="gotoPcPay">电脑网银支付</div>\n        </div>\n    </ui-dialog>\n\n    <!-- 错误提示 -->\n    <ui-dialog v-ref:error-dialog="" type="alert" :head="false" title="" ok="重试">\n    </ui-dialog>\n</div>';
o&&o.exports&&(o.exports.template=h),a&&a.default&&(a.default.template=h),o.exports=a["default"]});
;define("hiloan:components/fin-fk/repay/index",function(e,n,t){"use strict";function f(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var o=e("hiloan:components/fin-fk/repay/repay.vue"),u=f(o);
n.default=u.default,t.exports=n["default"]});
;define("hiloan:components/fin-fk/single-repayment/single-repayment-detail/single-repayment-detail.vue",function(e,a,t){"use strict";Object.defineProperty(a,"__esModule",{value:!0}),e("hiloan:components/fin-fg/filter/index"),a.default={data:function(){return{is:"fk-single-repayment-detail"}
},props:{repayListData:{type:Object,required:!0,"default":{}},loanPriceLabelText:{type:String,required:!1,"default":"贷款总额"},loanOrderNumber:{type:String,required:!1,"default":"订单编号"},loanDateLabelText:{type:String,required:!1,"default":"申请日期"},loanPeriodLabelText:{type:String,required:!1,"default":"还款期数"},loanZeroPeriodLabelText:{type:String,required:!1,"default":"延期还款期数"},loanUnpaidLabelText:{type:String,required:!1,"default":"待还款"},detailMoneyFormatType:{type:String,required:!1,"default":""},detailDateFormatType:{type:String,required:!1,"default":"yyyy/mm/dd"},notice:{type:String,required:!1,"default":""}}};
var n='<div class="fin-{{ is }}">\n        <div class="course-name">{{ repayListData.courseName }}</div>\n        <div class="school-name">{{ repayListData.shopName }}</div>\n        <div class="loan-price flex-box">\n            <span class="label">{{ loanPriceLabelText }}</span>\n            <p class="course-money flex-1 tr">\n                <span class="price-tag">￥</span>\n                <span class="money" v-text="repayListData.money | formatMoney detailMoneyFormatType"></span>\n            </p>\n        </div>\n\n        <!-- 订单编号 -->\n        <div class="loan-period flex-box" v-if="repayListData.loanOrderNumber">\n            <span class="label">{{ loanOrderNumber }}</span>\n            <p class="course-period flex-1 tr">{{ repayListData.loanOrderNumber }}</p>\n        </div>\n\n        <div class="loan-date flex-box">\n            <span class="label">{{ loanDateLabelText }}</span>\n            <p class="course-date flex-1 tr" v-text="repayListData.paidTime | formatDateTime detailDateFormatType"></p>\n        </div>\n        <div class="loan-period flex-box">\n            <span class="label">{{ loanPeriodLabelText }}</span>\n            <p class="course-period flex-1 tr">{{ repayListData.periods }}期</p>\n        </div>\n\n        <!-- 前零后高延期还款数 -->\n        <div class="loan-period flex-box" v-if="repayListData.lastPeriod">\n            <span class="label">{{ loanZeroPeriodLabelText }}</span>\n            <p class="course-period flex-1 tr">{{ repayListData.lastPeriod }}期 (前{{ repayListData.lastPeriod }}期无需还款)</p>\n        </div>\n\n        <div class="loan-unpaid flex-box">\n            <span class="label">{{ loanUnpaidLabelText }}</span>\n            <p class="course-unpaid flex-1 tr">\n                <span class="price-tag">￥</span>\n                <span class="money" v-text="repayListData.unpaid | formatMoney"></span>\n            </p>\n        </div>\n\n        <!-- 放款中还款日 -->\n        <div class="loan-period flex-box" v-if="parseInt(repayListData.tStatus, 10) === 10">\n            <span class="label">还款日</span>\n            <p class="course-period flex-1 tr">每月{{repayListData.bills[0].dueDate | formatDateTime \'dd\'}}日之前</p>\n        </div>\n\n        <!-- 放款中预计首次还款日 -->\n        <div class="loan-period flex-box" v-if="parseInt(repayListData.tStatus, 10) === 10">\n            <span class="label">预计首次还款时间</span>\n            <p class="course-period flex-1 tr">\n                <span v-text="repayListData.bills[0].dueDate | formatDateTime detailDateFormatType"></span>之前\n            </p>\n        </div>\n\n        <!-- 插槽 退款还款须知 -->\n        <slot name="notice">{{{ notice }}}</slot>\n</div>';
t&&t.exports&&(t.exports.template=n),a&&a.default&&(a.default.template=n),t.exports=a["default"]});
;define("hiloan:components/fin-fk/single-repayment/single-repayment-detail/index",function(e,n,t){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});
var l=e("hiloan:components/fin-fk/single-repayment/single-repayment-detail/single-repayment-detail.vue"),a=i(l);n.default=a.default,t.exports=n["default"]});
;define("hiloan:components/fin-ui/ui-tab/ui-tab.vue",function(t,e,i){"use strict";function n(t){return t&&t.__esModule?t:{"default":t}}Object.defineProperty(e,"__esModule",{value:!0});var s=t("hiloan:node_modules/vue/dist/vue.common"),a=n(s),u=t("hiloan:node_modules/vue-touch/vue-touch"),o=n(u);
a.default.use(o.default),e.default={props:{index:{type:[Number,String],"default":1,twoWay:!0},items:{type:Array,"default":function(){return[]}},size:{type:String,"default":""},tabChange:{type:Function,"default":function(){return function(){}
}}},data:function(){return{is:"ui-tab",transitionType:"",tabIndex:0,navWidth:[],navPos:[]}},computed:{getTabSize:function(){var t={};return"full"===this.size&&(t={width:100*(1/this.items.length).toFixed(6)+"%"}),t
},getNavSize:function(){return{width:(this.navWidth[this.tabIndex]||0)+"%"}},getBarPos:function(){return{left:(this.navPos[this.tabIndex]||0)+"%"}}},methods:{activate:function(t){var e=this.tabChange(this.index,this.items[t].index);
e!==!1&&(this.index=this.items[t].index)},isActive:function(t,e){var i="";return t.index===this.index&&(this.tabIndex=e,i="active"),i}},ready:function(){var t=this;this.$nextTick(function(){for(var e=t.$el.children[0],i=e.children,n=e.offsetWidth,s=0,a=0;a<i.length;a++)t.navWidth.push(100*(i[a].offsetWidth/n).toFixed(6)),t.navPos.push(100*(s/n).toFixed(6)),s+=i[a].offsetWidth
}),setTimeout(function(){t.transitionType="js-trans-start"},0)}};var d='<nav class="fin-{{ is }} {{ size }} {{ transitionType }}" v-show="items.length">\n    <ul class="{{ is }}-box">\n        <li v-for="(n, item) in items" v-touch:tap="activate(n)" :class="[isActive(item, n)]" :style="[getTabSize]">\n            {{{ item.name }}}\n        </li>\n    </ul>\n    <div class="{{ is }}-bar" :style="[getBarPos, getNavSize]"></div>\n</nav>';
i&&i.exports&&(i.exports.template=d),e&&e.default&&(e.default.template=d),i.exports=e["default"]});
;define("hiloan:components/fin-ui/ui-tab/index",function(e,t,u){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var i=e("hiloan:components/fin-ui/ui-tab/ui-tab.vue"),o=n(i);
t.default=o.default,u.exports=t["default"]});
;define("hiloan:components/fin-rm/risk-inform/show",function(t,r,i){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var e="init",o="error",n="",s="true";r.default={props:{topError:{type:String,"default":n}},computed:{tipState:function(){var t=e;
return this.$validation.invalid&&"true"===this.topError?t=o:"false"===this.topError&&(t=e),t},showTip:function(){var t=!1;return(this.tipState===e&&this.tip||this.tipState===o)&&(t=!0),t},tipContent:function(){var t=this.tip;
if(this.tipState===o){var r=this.$validation.value.errors;r.length>0&&(t=r[0].message)}return t}},events:{"show-error-message":function(){this.topError=s}}},i.exports=r["default"]});
;define("hiloan:components/fin-rm/risk-inform/risk-inform.vue",function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var a=e("hiloan:node_modules/vue/dist/vue.common"),o=i(a),l=e("hiloan:node_modules/vue-touch/vue-touch"),s=i(l),c=e("hiloan:node_modules/vue-validator/dist/vue-validator.common"),r=i(c),d=e("hiloan:node_modules/vue-validator-manage/dist/vue-validator-manage.common"),u=i(d),h=e("hiloan:components/fin-fg/util/index"),m=e("hiloan:components/fin-fg/util/request/redirect"),f=i(m),v=e("hiloan:components/fin-rm/risk-inform/show"),p=i(v);
o.default.use(s.default),o.default.use(r.default),o.default.use(u.default);var g=h.validator.custom.extend("checkA",{check:function(e){return"1"===e},message:"请勾选"});t.default={mixins:[p.default],props:{required:{type:Boolean,"default":!0},type:{type:String},link:{type:String},showAgreementChecked:{type:Boolean,"default":!0},checked:{type:Boolean,"default":!0},userName:{type:String,"default":""},marginTop:{type:String,"default":""},content:{type:String},agreementLabel:{type:String,"default":"已阅读并同意"},agreementContent:{type:String,"default":"借款协议"},title:{type:String,"default":"风险告知书"},rules:{type:Object,"default":function(){return{checkA:g}
}},fieldname:{type:String,"default":"agreement"},value:{type:String,"default":""}},data:function(){return{is:"risk-inform"}},computed:{realRules:function(){var e=this.rules;return this.required&&(e={required:{rule:!0},checkA:g}),e
},marginTop2Rem:function(){var e="";return this.marginTop&&(e="margin-top:"+window.lib.flexible.px2rem(this.marginTop)+"rem"),e},showContent:function(){return this.content?this.content:"本人"+this.userName+"已阅读并明确知悉风险告知书所告知事项"
}},methods:{jump:function(e){f.default(e)},toggle:function(e){if(e&&"a"===e.target.tagName.toLowerCase())return!1;this.checked=!this.checked;var t=this.checked?"checked":"unchecked";this.$dispatch(t,this,this.checked),this.value="0",this.checked&&(this.value="1")
}},watch:{value:function(e){h.store.local.setItem(this.fieldname,e)}},created:function(){var e=h.store.local.getItem(this.fieldname);e&&e.trim()&&(this.value=e),this.checked=!0,"1"!==this.value&&(this.checked=!1)
}};var k='<div class="fin-{{is}}" :style="marginTop2Rem">\n    <validator name="validation">\n        <div class="risk-inform" v-if="type !== \'agreement\'">\n            <p class="title">\n                <slot name="title">\n                    提交前请阅读<a href="javascript: void(0);" v-touch:tap.stop="jump(link)">《{{title}}》</a>并进行确认:\n                </slot>\n            </p>\n            <div v-touch:tap.stop="toggle" class="content" :class="[checked ? \'checked\' : \'unchecked\']">\n                <div>\n                    <i class="icon" :class="[checked ? \'icon-radio-on\' : \'icon-radio-off\']"></i>\n                </div>\n                <p class="content-desc">\n                    <slot name="content">\n                        {{showContent}}\n                    </slot>\n                </p>\n            </div>\n        </div>\n        <div v-else="" class="agreement" v-touch:tap.stop="toggle">\n            <div>\n                <i class="icon" :class="[checked ? \'icon-checkbox-on\' : \'icon-checkbox-off\']" v-show="showAgreementChecked"></i>\n            </div>\n            <p class="content-container">\n                <slot name="content">\n                    {{ agreementLabel }}<a href="javascript: void(0);" v-touch:tap.stop="jump(link)">《{{\n                    agreementContent }}》</a>\n                </slot>\n            </p>\n        </div>\n\n        <input type="hidden" v-model="value" v-validate:value="realRules" v-fieldname="fieldname">\n    </validator>\n</div>';
n&&n.exports&&(n.exports.template=k),t&&t.default&&(t.default.template=k),n.exports=t["default"]});
;define("hiloan:components/fin-rm/risk-inform/index",function(e,n,i){"use strict";function o(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var r=e("hiloan:components/fin-rm/risk-inform/risk-inform.vue"),t=o(r);
n.default=t.default,i.exports=n["default"]});
;define("hiloan:components/fin-fk/single-repayment/single-repayment-payback/single-repayment-payback.vue",function(a,e,t){"use strict";function n(a){return a&&a.__esModule?a:{"default":a}}Object.defineProperty(e,"__esModule",{value:!0});
var s=a("hiloan:node_modules/vue/dist/vue.common"),p=n(s),r=a("hiloan:node_modules/vue-touch/vue-touch"),i=n(r);a("hiloan:components/fin-fg/filter/index");var l=a("hiloan:components/fin-fg/util/index");
p.default.use(i.default),e.default={props:{tid:{type:String,required:!0,"default":""},repayListData:{type:Object,required:!0,"default":{userStatus:4}},paybackMoneyFormatType:{type:String,required:!1,"default":""},paybackDateFormatType:{type:String,required:!1,"default":"yyyy/mm/dd"},paybackDetailJumpUrl:{type:String,required:!0,"default":""},refundBankCardText:{type:String,required:!1,"default":"绑定且最近交易的银行卡"}},data:function(){return{is:"single-repayment-payback"}
},methods:{goToPaybackDetail:function(){l.redirect(this.paybackDetailJumpUrl,{tid:this.tid})}}};var d='<section class="fin-{{is}}">\n    <div class="repayment-item pay-back" v-if="repayListData.userStatus !== 4 &amp;&amp; repayListData.userStatus">\n        <div class="payback-main-content" v-touch:tap.stop="goToPaybackDetail">\n            <div class="repayment-intro-wrapper">\n                <p class="payback">受理退款成功</p>\n                <p class="duedate-wrapper payback" v-if="repayListData.userStatus === 3">您还需还款\n                   <span class="price-tag">￥</span>\n                   <span class="money" v-text="repayListData.debtAmount | formatMoney paybackMoneyFormatType"></span>\n                </p>\n            </div>\n            <div class="repayment-btn-wrapper payback">\n                详情\n            </div>\n        </div>\n        <div class="pay-back-detail" v-if="repayListData.userStatus === 1 || repayListData.userStatus === 2">\n            <div class="pay-back-money flex-box">\n                <span class="label">您将收到退款</span>\n                <p class="course-date flex-1 tr">\n                    <span class="price-tag">￥</span>\n                    <span class="money" v-text="repayListData.returnAmount | formatMoney paybackMoneyFormatType"></span>\n                </p>\n            </div>\n            <div class="pay-back-credit flex-box">\n                <span class="label">到账银行卡</span>\n                <p class="flex-1 tr">{{ repayListData.refundCredit ? repayListData.refundCredit : refundBankCardText }}</p>\n            </div>\n            <div class="flex-box">\n                <span class="label">退款到账时间</span>\n                <p class="course-period flex-1 tr">预计\n                    <span v-text="repayListData.refundArrivalDate | formatDateTime paybackDateFormatType">&gt;</span>\n                </p>\n            </div>\n        </div>\n    </div>\n</section>';
t&&t.exports&&(t.exports.template=d),e&&e.default&&(e.default.template=d),t.exports=e["default"]});
;define("hiloan:components/fin-fk/single-repayment/single-repayment-payback/index",function(e,n,a){"use strict";function t(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});
var i=e("hiloan:components/fin-fk/single-repayment/single-repayment-payback/single-repayment-payback.vue"),l=t(i);n.default=l.default,a.exports=n["default"]});
;define("hiloan:components/fin-fk/single-repayment/single-repayment-list/single-repayment-list.vue",function(t,e,n){"use strict";function a(t){return t&&t.__esModule?t:{"default":t}}Object.defineProperty(e,"__esModule",{value:!0});
var i=t("hiloan:node_modules/vue/dist/vue.common"),s=a(i),o=t("hiloan:node_modules/vue-touch/vue-touch"),r=a(o),p=t("hiloan:node_modules/underscore/underscore"),m=a(p),d=t("hiloan:components/fin-fg/util/index"),l=t("hiloan:node_modules/object-assign/index"),y=a(l);
t("hiloan:components/fin-fg/filter/index");var u=t("hiloan:components/fin-ui/ui-tab/index"),c=a(u),f=t("hiloan:components/fin-ui/ui-dialog/index"),v=a(f),x=t("hiloan:components/fin-rm/risk-inform/index"),T=a(x),h=t("hiloan:components/fin-fk/single-repayment/single-repayment-payback/index"),g=a(h);
s.default.use(r.default),e.default={data:function(){return{validation:"",is:"fk-single-repayment-list",paidList:[],allUnpaidList:[],showCheckMore:!0,currentTabIndex:1,showTodayLoanDialog:!1}},props:{tid:{type:String,required:!0,"default":""},repayListData:{type:Object,required:!0,"default":function(){return{bills:[],userStatus:4}
}},listFormatConfig:{type:Object,required:!1,"default":function(){return{}},coerce:function(t){return m.default.extend({listMoneyFormatType:"cn",listDateFormatType:"yyyy/mm/dd",paybackMoneyFormatType:"",paybackDateFormatType:"yyyy/mm/dd"},t)
}},checkMoreItemCount:{type:Number,required:!1,"default":6},repaymentListText:{type:Object,required:!1,"default":function(){return{}},coerce:function(t){return m.default.extend({listNavUnpaidText:"待还款",listNavPaidText:"已还款",listAllPaidText:"该笔贷款已还清，快去看看其它课程吧",listNoPaidText:"您还没有已还款记录",moneyReleasingText:"培训学校还未进行对账收款，您暂时不需要还款",prinamtText:"本金",feeText:"本期手续费",intamtText:"本期利息",interestText:"逾期费",discountText:"优惠券",refundBankCardText:"绑定且最近交易的银行卡"},t)
}},paybackDetailJumpUrl:{type:String,required:!1,"default":""},paidDuedateText:{type:String,required:!1,"default":"还款日"},canRepayment:{type:Number,required:!1,"default":1},interestRule:{type:String,required:!1,"default":"M"},autoRepayStatus:{type:Number},autoRepayUrl:{type:String},autoRepayAddon:{type:Boolean,"default":!1}},methods:{checkMore:function(){this.showCheckMore=!1,this.checkMoreItemCount=this.allUnpaidList.length
},doRepay:function(t,e){if(e){if(0===this.canRepayment)return void(this.showTodayLoanDialog=!0);if("D"===this.interestRule)return void d.redirect("/fucs/repayment/calculator",{tid:this.tid,money:t.curPrinamt});
this.$dispatch("repayMoney",t)}},closeTodayLoanDialog:function(){this.showTodayLoanDialog=!1}},computed:{unPaidList:function(){var t=[],e=this.repayListData.bills;if(this.paidList=[],this.repayListData.lastPeriod){var n={lastPeriod:1===this.repayListData.lastPeriod?"1":"1 - "+this.repayListData.lastPeriod};
this.repayListData.zeroPaid?this.paidList.unshift(n):t.push(n);for(var a=this.repayListData.lastPeriod;a<e.length;a++)48!==e[a].bStatus?t.push(y.default(e[a],{amountType:this.repayListData.amountType})):this.paidList.unshift(e[a])
}else for(var i=0;i<e.length;i++)48!==e[i].bStatus?t.push(y.default(e[i],{amountType:this.repayListData.amountType})):this.paidList.unshift(e[i]);return this.allUnpaidList=t,4!==this.repayListData.userStatus&&2!==+this.repayListData.refundMode&&(this.repaymentListText.listNavUnpaidText="退款"),"D"===this.interestRule&&(this.repaymentListText.interestText="罚息"),t.slice(0,this.checkMoreItemCount)
}},components:{singleRepaymentPayback:g.default,uiTab:c.default,riskInform:T.default,uiDialog:v.default}};var L='<div class="fin-{{ is }}">\n    <div class="repayment-list-nav">\n        <ui-tab v-ref:ui-tab="" :index.sync="currentTabIndex" :items="[\n             {\n                 name: repaymentListText.listNavUnpaidText,\n                 index: 1\n             },\n             {\n                 name: repaymentListText.listNavPaidText,\n                 index: 2\n             }\n            ]">\n        </ui-tab>\n    </div>\n\n    <!-- 放款中 -->\n    <div class="repayment-list" :class="{\'active\': parseInt(repayListData.tStatus, 10) === 10}">\n         <p class="empty-zone-releasing">\n           {{ repaymentListText.moneyReleasingText }}\n        </p>\n    </div>\n\n    <div class="repayment-list unpaid" v-show="parseInt(repayListData.tStatus, 10) !== 10" :class="{\'active\': currentTabIndex === 1}">\n\n        <!-- 退款详情 -->\n        <single-repayment-payback :tid="tid" :repay-list-data="repayListData" :payback-money-format-type="listFormatConfig.paybackMoneyFormatType" :payback-date-format-type="listFormatConfig.paybackDateFormatType" :payback-detail-jump-url="paybackDetailJumpUrl" :refund-bank-card-text="repaymentListText.refundBankCardText">\n        </single-repayment-payback>\n\n        <div class="repayment-item" v-for="(index, item) in unPaidList">\n            <div class="main-content" v-if="!item.lastPeriod">\n                <div class="repayment-intro-wrapper">\n                    <p v-if="item.paidMoney &amp;&amp; item.unpaidMoney !== item.money" class="period-money">第 {{ item.period }} 期 剩余\n                        <span class="money" v-text="item.money - item.paidMoney | formatMoney listFormatConfig.listMoneyFormatType">\n                        </span><br>\n                        已还\n                        <span class="money" v-text="item.paidMoney | formatMoney listFormatConfig.listMoneyFormatType">\n                        </span>\n                    </p>\n                    <p v-else="" class="period-money">第 {{ item.period }} 期 应还款\n                        <span class="money" v-text="item.money - item.paidMoney | formatMoney listFormatConfig.listMoneyFormatType">\n                        </span>\n                    </p>\n\n                    <p class="duedate-wrapper">最后还款日\n                        <span class="duedate" v-text="item.dueDate | formatDateTime listFormatConfig.listDateFormatType">\n                        </span>\n                        <span class="overdue" v-show="item.bStatus === 144">\n                              已逾期\n                        </span>\n                    </p>\n                </div>\n                <div class="repayment-btn-wrapper fin-ui-button small" :disabled="item.bStatus === 80 || index !== 0" :class="{\n                        \'primary\': item.bStatus === 80 || index !== 0,\n                        \'danger\': item.bStatus === 144 &amp;&amp; index === 0\n                     }" v-touch:tap.stop="doRepay(item, (index === 0 &amp;&amp; (item.bStatus === 112 || item.bStatus === 144)))">\n                     {{ item.bStatus === 80 ? \'待还款\' : \'还款\' }}\n                </div>\n            </div>\n\n            <!-- 前零后高 -->\n            <div class="main-content" v-if="item.lastPeriod">\n                <div class="repayment-intro-wrapper">\n                    <p class="period-money">第 {{ item.lastPeriod }} 期 应还款 <span class="price-tag">￥</span><span class="money">0.00</span></p>\n                    <p class="duedate-wrapper">期间无需还款</p>\n                </div>\n                <div class="repayment-btn-wrapper noneed-repay fin-ui-button small">\n                        无需还款\n                </div>\n            </div>\n\n            <div class="footer-content" v-if="!item.lastPeriod &amp;&amp; !(allUnpaidList.length > checkMoreItemCount &amp;&amp; showCheckMore &amp;&amp; index === checkMoreItemCount - 1)">\n                <p class="repayment-fee-intro">\n                    包含\n                    <span v-if="item.curPrinamt">\n                        {{ repaymentListText.prinamtText }}\n                        <span class="prinamt" v-text="item.curPrinamt | formatMoney listFormatConfig.listMoneyFormatType">\n                        </span>\n                    </span>\n                    <span v-if="item.fee">\n                        {{ (item.curPrinamt ? \'+ \' : \'\') + repaymentListText.feeText }}\n                        <span class="fee" v-text="item.fee - item.paidFee | formatMoney listFormatConfig.listMoneyFormatType">\n                        </span>\n                    </span>\n                    <span v-if="item.intamt">\n                        {{ (item.curPrinamt || item.fee ? \'+ \' : \'\') + repaymentListText.intamtText }}\n                        <span class="intamt" v-text="item.intamt - item.paidInterest | formatMoney listFormatConfig.listMoneyFormatType">\n                        </span>\n                    </span>\n                    <span v-if="item.interest">\n                        {{ (item.curPrinamt || item.fee || item.intamt ? \'+ \' : \'\') + repaymentListText.interestText }}\n                        <span class="interest" v-text="item.interest - item.paidPenalty | formatMoney listFormatConfig.listMoneyFormatType">\n                        </span>\n                    </span>\n                    <span v-if="item.discount">\n                        - <font class="orange">{{repaymentListText.discountText}}{{item.discount | formatMoney \'cn\'}}</font>\n                    </span>\n                </p>\n                <div class="autorepay-addon" v-if="autoRepayAddon">\n                  <risk-inform v-if="(autoRepayStatus === 0) &amp;&amp; (index === 0) &amp;&amp; (item.bStatus === 112 || item.bStatus === 144)" v-ref:riskinform="" type="agreement" v-fieldset="autorepayaddon" value="1">\n                      <span slot="content">\n                          开启自动还款, 查看<a :href="autoRepayUrl" v-touch:tap.stop="protocolHandler">《有钱花自动还款协议》</a>\n                      </span>\n                  </risk-inform>\n                </div>\n            </div>\n        </div>\n\n        <p class="empty-zone" v-show="!unPaidList.length &amp;&amp; repayListData.userStatus === 4">\n           {{ repaymentListText.listAllPaidText }}\n        </p>\n        <div class="load-more" v-if="allUnpaidList.length > checkMoreItemCount" v-show="showCheckMore" v-touch:tap.stop="checkMore()">\n            <span class="load-more-arrow">查看全部</span>\n        </div>\n    </div>\n    <div class="repayment-list paid" v-show="parseInt(repayListData.tStatus, 10) !== 10" :class="{\'active\': currentTabIndex === 2}">\n\n        <div class="repayment-item" v-for="(index, item) in paidList">\n            <div class="main-content" v-if="!item.lastPeriod">\n                <div class="repayment-intro-wrapper">\n                    <p class="period-money">第 {{ item.period }} 期 应还款\n                        <span class="money" v-text="item.paidMoney | formatMoney listFormatConfig.listMoneyFormatType">\n                        </span>\n                    </p>\n                    <p class="duedate-wrapper">{{ paidDuedateText }}\n                        <span class="duedate" v-text="item.paidTime | formatDateTime listFormatConfig.listDateFormatType">\n                        </span>\n                    </p>\n                </div>\n            </div>\n\n            <!-- 前零后高 -->\n            <div class="main-content" v-if="item.lastPeriod">\n                <div class="repayment-intro-wrapper">\n                    <p class="period-money">第 {{ item.lastPeriod }} 期 应还款 <span class="price-tag">￥</span><span class="money">0.00</span></p>\n                    <p class="duedate-wrapper">期间无需还款</p>\n                </div>\n                <div class="repayment-btn-wrapper noneed-repay fin-ui-button small">\n                        无需还款\n                </div>\n            </div>\n\n            <div class="footer-content" v-if="!item.lastPeriod">\n                <p class="repayment-fee-intro">\n                    包含\n                    <span v-if="item.prinamt">\n                        {{ repaymentListText.prinamtText }}\n                        <span class="prinamt" v-text="item.prinamt | formatMoney listFormatConfig.listMoneyFormatType">\n                        </span>\n                    </span>\n                    <span v-if="item.fee">\n                        {{ (item.prinamt ? \'+ \' : \'\') + repaymentListText.feeText }}\n                        <span class="fee" v-text="item.fee | formatMoney listFormatConfig.listMoneyFormatType">\n                        </span>\n                    </span>\n                    <span v-if="item.intamt">\n                        {{ (item.prinamt || item.fee ? \'+ \' : \'\') + repaymentListText.intamtText }}\n                        <span class="intamt" v-text="item.intamt | formatMoney listFormatConfig.listMoneyFormatType">\n                        </span>\n                    </span>\n                    <span v-if="item.interest">\n                        {{ (item.prinamt || item.fee || item.intamt ? \'+ \' : \'\') + repaymentListText.interestText }}\n                        <span class="interest" v-text="item.interest | formatMoney listFormatConfig.listMoneyFormatType">\n                        </span>\n                    </span>\n                    <span v-if="item.paidDiscount">\n                        {{ (item.prinamt || item.fee || item.intamt || item.interest ? \'- \' : \'\') + repaymentListText.discountText }}\n                    </span>\n                    <span v-if="item.paidDiscount" class="discount" v-text="item.paidDiscount | formatMoney listFormatConfig.listMoneyFormatType">\n                    </span>\n                </p>\n            </div>\n        </div>\n\n        <p class="empty-zone" v-show="!paidList.length">\n           {{ repaymentListText.listNoPaidText }}\n        </p>\n    </div>\n    <!-- 退款相关提示文案（临时方案） -->\n    <p class="refund-tip" :class="{\'active\': currentTabIndex === 2}" v-if="paidList.length &amp;&amp; +repayListData.userStatus !== 4" v-text="\'以上还款金额包括机构的退款\'">\n    </p>\n    <ui-dialog type="alert" :show.sync="showTodayLoanDialog" title="提示" content="当天借款需第二天才能还款" ok="知道了" @ok="closeTodayLoanDialog">\n    </ui-dialog>\n</div>';
n&&n.exports&&(n.exports.template=L),e&&e.default&&(e.default.template=L),n.exports=e["default"]});
;define("hiloan:components/fin-fk/single-repayment/single-repayment-list/index",function(e,n,t){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});
var l=e("hiloan:components/fin-fk/single-repayment/single-repayment-list/single-repayment-list.vue"),s=i(l);n.default=s.default,t.exports=n["default"]});
;define("hiloan:components/fin-fk/single-repayment/single-repayment.vue",function(e,t,a){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});
var i=e("hiloan:node_modules/vue/dist/vue.common"),o=n(i),r=e("hiloan:node_modules/vue-touch/vue-touch"),d=n(r),s=e("hiloan:node_modules/underscore/underscore"),l=n(s),u=e("hiloan:node_modules/object-assign/index"),c=n(u),p=e("hiloan:app/static/config/api"),y=n(p),f=e("hiloan:node_modules/query-string/index"),m=(n(f),e("hiloan:components/fin-fg/util/base64/index")),g=(n(m),e("hiloan:components/fin-fg/util/index")),h=e("hiloan:components/fin-fg/util/store/local"),x=n(h),v=e("hiloan:components/fin-fg/util/native/index"),P=n(v),b=e("hiloan:components/fin-ui/ui-dialog/index"),T=n(b),S=e("hiloan:components/fin-fk/single-repayment/single-repayment-detail/index"),R=n(S),C=e("hiloan:components/fin-fk/single-repayment/single-repayment-list/index"),D=n(C),q=e("hiloan:components/fin-fk/free-repay/index"),N=n(q);
o.default.use(d.default),t.default={data:function(){return{is:"fk-single-repayment",loading:!1,isDataloaded:!1,repayListData:{bills:[],userStatus:4},tradeInfo:{},checkOrderPollingCount:0,repayItem:{},showDialogPaySuccess:!1,showDialogPayPc:!1,redirectPcUrl:"",period:0,couponId:""}
},props:{tid:{type:String,required:!0,"default":""},courseName:{type:String,required:!0,"default":""},shopName:{type:String,required:!0,"default":""},loanOrderNumber:{type:String,required:!1,"default":""},userStatus:{type:Number,required:!1,"default":4},debtAmount:{type:Number,required:!1,"default":0},returnAmount:{type:Number,required:!1,"default":0},refundArrivalDate:{type:Number,required:!1,"default":0},refundCredit:{type:String,required:!1,"default":""},repaymentDetailText:{type:Object,required:!1,"default":function(){return{}
},coerce:function(e){return l.default.extend({loanPriceLabelText:"贷款总额",loanDateLabelText:"申请日期",loanPeriodLabelText:"还款期数",loanZeroPeriodLabelText:"延期还款期数",loanUnpaidLabelText:"待还款"},e)}},detailFormatConfig:{type:Object,required:!1,"default":function(){return{}
},coerce:function(e){return l.default.extend({detailmoneyFormatType:"",detaildateFormatType:"yyyy/mm/dd"},e)}},notice:{type:String,required:!1,"default":""},listFormatConfig:{type:Object,required:!1,"default":function(){return{}
},coerce:function(e){return l.default.extend({listMoneyFormatType:"cn",listDateFormatType:"yyyy/mm/dd",paybackMoneyFormatType:"",paybackDateFormatType:"yyyy/mm/dd"},e)}},checkMoreItemCount:{type:Number,required:!1,"default":6},repaymentListText:{type:Object,required:!1,"default":function(){return{}
},coerce:function(e){return l.default.extend({listNavUnpaidText:"待还款",listNavPaidText:"已还款",listAllPaidText:"该笔贷款已还清，快去看看其它课程吧",listNoPaidText:"您还没有已还款记录",moneyReleasingText:"培训学校还未进行对账收款，您暂时不需要还款",prinamtText:"本金",feeText:"本期手续费",intamtText:"本期利息",interestText:"逾期费",refundBankCardText:"绑定且最近交易的银行卡"},e)
}},checkOrderMaxPollingCount:{type:Number,required:!1,"default":6},paybackDetailJumpUrl:{type:String,required:!1,"default":""},interfaceConfig:{type:Object,required:!1,"default":function(){return{}},coerce:function(e){return l.default.extend({getRepayList:{},getCashierRepay:{},checkOrder:{}},e)
}},xSilent:{type:Boolean,required:!1,"default":!1},largePayControl:{type:Boolean,required:!1,"default":!1},largePayUrl:{type:String,required:!1,"default":""},period:{type:String,required:!1,"default":""},xMessage:{type:Boolean,required:!1,"default":!0},dialogPaySuccessEnable:{type:Boolean,"default":!1},dialogText:{type:Object,required:!1,"default":function(){return{}
},coerce:function(e){return l.default.extend({dialogPaySuccessTitle:"还款成功",dialogPaySuccessInfo:"每月按时还款能提升您的信用额度",dialogPaySuccessAmountTitle:"",dialogPaySuccessProjectName:"项目名称"},e)}},needRepayDetail:{type:Boolean,required:!1,"default":!0},canFreePay:{type:Boolean,required:!1,"default":!1},freePayShow:{type:Boolean,required:!1,"default":!1},freePayMin:{type:Number,required:!1,"default":500},freePayMax:{type:Number,required:!1,"default":500},freePayDiscount:{type:Number,"default":0},canRepayment:{type:Number,required:!1,"default":1},interestRule:{type:String,required:!1,"default":"M"},autoRepayStatus:{type:Number},autoRepayUrl:{type:String},autoRepayAddon:{type:Boolean,"default":!1},refundMode:{type:Number}},components:{uiDialog:T.default,singleRepaymentDetail:R.default,singleRepaymentList:D.default,freeRepay:N.default},created:function(){var e=this,t=c.default({},this.interfaceConfig.getRepayList);
this.loading||(this.$dispatch("getRepayListLoading"),y.default.getRepayList(t,{"x-silent":this.xSilent,"x-message":this.xMessage}).then(function(t){t.data.courseName=e.courseName,t.data.shopName=e.shopName,t.data.loanOrderNumber=e.loanOrderNumber,t.data.userStatus=e.userStatus,t.data.refundMode=e.refundMode,t.data.debtAmount=e.debtAmount,t.data.returnAmount=e.returnAmount,t.data.refundArrivalDate=e.refundArrivalDate,t.data.refundCredit=e.refundCredit,e.isDataloaded=!0,e.repayListData=t.data,e.couponId=t.data.couponId,e.$dispatch("getRepayListLoaded",t),e.loading=!1
}).catch(function(t){e.isDataloaded=!0,1!==t.errno&&e.$dispatch("getRepayListError",t)}))},methods:{bankLimitAmount:function(e,t){var a=this;a.amount=e,x.default.setItem("orderInfo",t),P.default.get("ua",{}).then(function(e){var t={ua:e.data.split(",")[0]};
return y.default.getActiveCardquota(t,{})}).then(function(e){var n=parseInt(e.data.quotaInfo.singleLimit,10),i=parseInt(a.amount,10);i>n&&-1!=n?a.doRepayPc(t):a.doRepayNa(t.orderInfo)}).catch(function(e){console.log(e)
})},doRepayNa:function(e){var t=this;P.default.get("doPay",e).then(function(e){0===parseInt(e.data.statecode,10)&&t.checkOrder(),t.$dispatch("doRepayNaSuccess",e)})},doRepayPc:function(){this.showDialogPayPc=!0
},redirectPcpay:function(){g.redirect(this.largePayUrl,{periods:this.period})},wait:function(e){var t=this;setTimeout(function(){return t.checkOrderPollingCount++,t.checkOrderPollingCount>t.checkOrderMaxPollingCount?(t.checkOrderPollingCount=0,void t.$dispatch("checkOrderEnded")):void(t.loading||(t.loading=!0,y.default.checkOrder(e,{"x-silent":t.xSilent,"x-message":t.xMessage}).then(function(a){t.loading=!1;
var n=a.data&&a.data.tstatus;2===n||3===n?(t.$dispatch("checkOrderSuccess"),t.dialogPaySuccessEnable&&(t.showDialogPaySuccess=!0)):5===n&&t.wait(e)})))},2500)},checkOrder:function k(){var k=c.default({},this.interfaceConfig.checkOrder);
k.tid=this.tradeInfo.newTid,this.$dispatch("checkOrderStarted"),this.wait(k)},closeDialogPaySuccess:function(){this.showDialogPaySuccess=!1,this.$nextTick(function(){location.reload()})},closeDialogPayPc:function(){this.showDialogPayPc=!1
},freePay:function(e){e.unpaidMoney<=this.freePayMin?this.doGetCashierRepay():this.$refs.freerepay.open({discount:e.discount,max:e.unpaidMoney-e.discount})},doGetCashierRepay:function(){var e=this;this.loading||(this.loading=!0,this.$dispatch("getCashierRepayLoading"),y.default.getCashierRepay(this.interfaceConfig.getCashierRepay,{"x-silent":this.xSilent,"x-message":this.xMessage}).then(function(t){return e.loading=!1,e.$dispatch("getCashierRepayLoaded"),t.data&&null!=t.data.orderInfo?(e.tradeInfo=t.data,void(e.largePayControl?e.tradeInfo.orderInfo.split("&").forEach(function(t){/total_amount/i.test(t)&&(e.totalAmount=t.split("=")[1],e.bankLimitAmount(e.totalAmount,e.tradeInfo))
}):e.doRepayNa(e.tradeInfo.orderInfo))):void e.$dispatch("getCashierRepayError",{errmsg:"生成订单失败,请重试"})}).catch(function(t){1!==t.errno&&e.$dispatch("getCashierRepayError",t)}))}},events:{repayMoney:function(e){this.repayItem=e;
var t=e.period;this.period=e.period,c.default(this.interfaceConfig.getCashierRepay,{periods:t,payType:1,discountAmt:e.discount,prinamt:e.prinamt,amountType:e.amountType,money:e.money-e.paidMoney}),this.couponId&&c.default(this.interfaceConfig.getCashierRepay,{couponIds:JSON.stringify([this.couponId])}),this.canFreePay?this.freePay(e):this.doGetCashierRepay()
},freeRepay:function(e){var t=c.default(this.interfaceConfig.getCashierRepay,{money:parseInt(e,10)});this.doGetCashierRepay(t,!0)}},computed:{dialogPaySuccessAmountName:function(){return this.dialogText.dialogPaySuccessAmountTitle||this.repayItem.period+"/"+this.repayListData.periods+"期还款"
}}};var I='<div class="fin-{{ is }}">\n    <single-repayment-detail v-if="needRepayDetail" :loan-price-label-text="repaymentDetailText.loanPriceLabelText" :loan-date-label-text="repaymentDetailText.loanDateLabelText" :loan-period-label-text="repaymentDetailText.loanPeriodLabelText" :loan-zero-period-label-text="repaymentDetailText.loanZeroPeriodLabelText" :loan-unpaid-label-text="repaymentDetailText.loanUnpaidLabelText" :detail-money-format-type="detailFormatConfig.detailMoneyFormatType" :detail-date-format-type="detailFormatConfig.detaildateFormatType" :repay-list-data="repayListData">\n        <div slot="notice">\n            <slot name="notice">{{{ notice }}}</slot>\n        </div>\n    </single-repayment-detail>\n\n    <single-repayment-list v-ref:single-repayment-list="" :tid="tid" :refund-mod="refundMod" :repay-list-data="repayListData" :list-format-config="listFormatConfig" :check-more-item-count="checkMoreItemCount" :repayment-list-text="repaymentListText" :payback-detail-jump-url="paybackDetailJumpUrl" :can-repayment="canRepayment" :interest-rule="interestRule" :auto-repay-status="autoRepayStatus" :auto-repay-url="autoRepayUrl" :auto-repay-addon="autoRepayAddon">\n    </single-repayment-list>\n\n    <free-repay v-ref:freerepay="" :min="freePayMin" :max="freePayMax" :discount="freePayDiscount" :show="freePayShow">\n    </free-repay>\n\n    <section class="load-mask" v-show="!isDataloaded" transition="fade">\n    </section>\n\n    <!-- 还款成功弹窗 -->\n    <ui-dialog type="dialog" :head="false" :show.sync="showDialogPaySuccess">\n        <div class="dialog-pay-success" slot="content">\n            <!-- 关闭按钮 -->\n            <div class="result-close icon icon-close" v-touch:tap="closeDialogPaySuccess"></div>\n\n            <!-- 付款标题 -->\n            <section class="result-head">\n                <div class="result-type"><i class="icon icon-success"></i></div>\n                <div class="title-wrap">\n                    <div v-if="dialogText.dialogPaySuccessTitle" class="result-title" :class="{\'middle\': !dialogText.dialogPaySuccessInfo}">{{ dialogText.dialogPaySuccessTitle }}</div>\n                    <div v-if="dialogText.dialogPaySuccessInfo" class="result-info">{{ dialogText.dialogPaySuccessInfo }}</div>\n                </div>\n            </section>\n\n            <!-- line -->\n            <section class="result-line">\n                <div class="l"></div>\n                <div class="m"><div></div></div>\n                <div class="r"></div>\n            </section>\n\n            <!-- 付款信息 -->\n            <section class="result-table">\n                <!-- 还款金额 -->\n                <div class="table-row">\n                    <p class="l">{{ dialogPaySuccessAmountName }}</p>\n                    <p class="r orange" v-text="repayItem.money | formatMoney \'en\' 2 \'.\' \',\'"></p>\n                </div>\n                <!-- 还款项目 -->\n                <div class="table-row">\n                    <p class="l">{{ dialogText.dialogPaySuccessProjectName }}</p>\n                    <p class="r">{{ courseName }}</p>\n                </div>\n            </section>\n\n            <!-- 运营活动位置插槽 -->\n            <slot name="pay-success-ad"></slot>\n            <!-- 锯齿底部 -->\n            <section class="result-line-bottom">\n            </section>\n        </div>\n    </ui-dialog>\n\n\n    <!-- PC还款弹窗 -->\n    <ui-dialog type="dialog" :head="false" :show.sync="showDialogPayPc">\n        <div class="dialog-pay-pc" slot="content">\n            <!-- 关闭按钮 -->\n            <div class="pc-pay-close icon icon-close" v-touch:tap="closeDialogPayPc"></div>\n\n            <!-- pclog -->\n            <section class="pc-pay-logo">\n            </section>\n\n\n            <!-- pctitle -->\n            <section class="pc-pay-head">\n                <h2>还款金额超过<br>手机端支付限额</h2>\n                <p>请前往百度钱包电脑版，完成支付</p>\n            </section>\n\n            <div class="fin-ui-button full" v-touch:tap="redirectPcpay">电脑网银支付</div>\n\n        </div>\n    </ui-dialog>\n\n\n</div>';
a&&a.exports&&(a.exports.template=I),t&&t.default&&(t.default.template=I),a.exports=t["default"]});
;define("hiloan:components/fin-fk/single-repayment/index",function(e,n,t){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var f=e("hiloan:components/fin-fk/single-repayment/single-repayment.vue"),l=i(f);
n.default=l.default,t.exports=n["default"]});
;define("hiloan:components/fin-rm/input-base/async",function(s,t,n){"use strict";function e(s){return s&&s.__esModule?s:{"default":s}}Object.defineProperty(t,"__esModule",{value:!0});var a=s("hiloan:node_modules/underscore/underscore"),i=e(a),c=s("hiloan:app/static/config/api"),r=e(c),u="init",y="loading",h="true",o="false";
t.default={data:function(){return{hasAsync:!1,asyncState:u,asyncMessage:"",timer:null}},methods:{asyncFn:function(){var s=this;if(this.validation.valid){var t=this.async.before.call(this,this.value);if(t===!1)return!1;
this.asyncState=y,this.asyncMessage=this.async.message.loading;var n=this.value;r.default[this.async.apiName](t,{"x-silent":!0}).then(function(t){return s.value!==n?!1:void(s.async.rule.call(s,t,s.value)?(s.asyncState=h,s.asyncMessage=s.async.message.success,s.async.success.call(s,t)):(s.asyncState=o,s.asyncMessage=s.async.message.error,s.async.error.call(s,t)))
})}},startAsync:function(){var s=this;this.hasAsync&&this.asyncState===u&&(clearTimeout(this.timer),this.asyncMessage="",setTimeout(function(){s.asyncFn()}))},closeAsync:function(){clearTimeout(this.timer),this.asyncMessage="",this.asyncState=u,this.async.trigger="close"
},delayAsync:function(s){var t=this;this.hasAsync&&(s||(s=800),this.asyncState=u,this.asyncMessage="",clearTimeout(this.timer),this.timer=setTimeout(function(){t.asyncFn()},s))},blurAsync:function(){this.async.trigger&&"blur"!==this.async.trigger||this.edit||this.startAsync()
},inputAsync:function(){this.async.trigger&&"input"!==this.async.trigger||this.delayAsync(this.async.time)}},watch:{async:{handler:function(){this.hasAsync=i.default.isEmpty(this.async)?!1:!0},deep:!0}},created:function(){this.hasAsync=i.default.isEmpty(this.async)?!1:!0
},compiled:function(){var s=this,t=this.$refs.input;t.$on("blur",function(){s.blurAsync()}),t.$on("input",function(){s.inputAsync()})}},n.exports=t["default"]});
;define("hiloan:components/fin-rm/input-base/show",function(t,i,s){"use strict";Object.defineProperty(i,"__esModule",{value:!0});var o="loading",n="false",a="init",e="loading",r="error",h="",u="true",d="false";
i.default={data:function(){return{validation:{},focused:!1}},computed:{tipState:function(){var t=a;return this.validation.invalid?(this.topError===u||this.validation.touched||this.value&&this.validation.pristine)&&(t=r,!this.value&&this.validation.touched&&this.readonly?t=a:this.topError!==u&&!this.value&&this.focused&&(t=a)):this.asyncState===n?t=r:this.asyncState===o&&(t=e),this.topError===d&&t===r&&(t=a),t
},showTip:function(){var t=!0;return this.tipState!==a||this.tip?this.asyncState!==o||this.topLoading||this.tip||(t=!1):t=!1,t},tipContent:function(){var t="";return this.tipState===a?t=this.tip:this.tipState===e?t=this.topLoading?this.asyncMessage:this.tip:this.tipState===r&&(this.validation.value&&this.validation.value.errors?t=this.validation.value.errors[0].message:this.asyncState===n&&(t=this.asyncMessage)),t
}},methods:{closeLoading:function(){this.topLoading=!1},openLoading:function(){this.topLoading=!0},showError:function(){this.topError=u},closeError:function(){this.topError=d},defaultError:function(){this.topError=h
}},events:{"show-error-message":function(){this.topError=u},focus:function(){return this.focused=!0,!0},blur:function(){return this.focused=!1,!0}},ready:function(){var t=this;this.validation=this.$refs.input.$validation,setTimeout(function(){t.startAsync()
})}},s.exports=i["default"]});
;define("hiloan:components/fin-rm/input-base/input.vue",function(t,e,i){"use strict";function n(t){return t&&t.__esModule?t:{"default":t}}Object.defineProperty(e,"__esModule",{value:!0});var o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t
}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a=t("hiloan:node_modules/underscore/underscore"),s=n(a),u=t("hiloan:node_modules/vue/dist/vue.common"),l=n(u),d=t("hiloan:node_modules/vue-touch/vue-touch"),r=n(d),c=t("hiloan:node_modules/vue-validator/dist/vue-validator.common"),h=n(c),f=t("hiloan:node_modules/vue-validator-manage/dist/vue-validator-manage.common"),m=n(f),p=t("hiloan:components/fin-fg/util/index"),v=t("hiloan:components/fin-fg/mixins/index"),y=p.store.local;
l.default.use(r.default),l.default.use(h.default),l.default.use(m.default);var b=Object.assign?Object.assign:s.default.extend,g=[b({},v.input,v.inputValidNotice)];e.default={mixins:g,data:function(){return{is:"input",showClose:!1,timer:null}
},computed:{editColor:function(){return this.edit?"edit-color":""}},methods:{clearValue:function(){clearTimeout(this.timer),this.value="",this.$dispatch("input",this),this.showClose=!1},focus:function(){this.focusToBlur=!1,this.$dispatch("focus",this),this.value&&!this.readonly&&(this.showClose=!0)
},blur:function(){var t=this;this.$dispatch("blur",this),this.timer=setTimeout(function(){t.showClose=!1}),this.focusToBlur=!0},input:function(){this.$dispatch("input",this),this.showClose=this.value?!0:!1
},getValue:function(){return this.value},getValidation:function(){return this.$validation},editFn:function(){this.edit&&(this.$dispatch("edit",this),this.edit=!1,this.value="")}},watch:{value:function(t){this.recordInput&&y.setItem(this.fieldname,t)
}},created:function(){if(this.recordInput){var t=y.getItem(this.fieldname);"object"===("undefined"==typeof t?"undefined":o(t))&&(t=t.value),t&&t.trim()&&(this.edit=!1,this.$dispatch("edit",this),this.value=t)
}}};var x='<span class="fin-{{is}} {{inputCss}}">\n    <validator name="validation">\n        <span class="wrap-flex" v-touch:tap="editFn">\n            <input :type="type" class="{{editColor}}" :placeholder="placeholder" :readonly="readonly||edit" :min="min" :max="max" :maxlength="maxlength" @blur="blur" @focus="focus" @input="input" v-model="value" :value="value" v-fieldname="fieldname" :base64="base64" :format="format" :detect-change="detectChange" v-validate:value="rules" @valid="invalidToValid">\n        </span>\n        <i class="icon icon-close-revert" v-if="!edit" v-show="showClose" v-touch:tap="clearValue">\n        </i>\n    </validator>\n</span>';
i&&i.exports&&(i.exports.template=x),e&&e.default&&(e.default.template=x),i.exports=e["default"]});
;define("hiloan:components/fin-rm/input-base/input-base.vue",function(t,e,n){"use strict";function i(t){return t&&t.__esModule?t:{"default":t}}Object.defineProperty(e,"__esModule",{value:!0});var o=t("hiloan:node_modules/underscore/underscore"),a=i(o),s=t("hiloan:node_modules/vue/dist/vue.common"),r=i(s),d=t("hiloan:node_modules/vue-touch/vue-touch"),l=i(d),u=t("hiloan:node_modules/vue-validator/dist/vue-validator.common"),c=i(u),h=t("hiloan:node_modules/vue-validator-manage/dist/vue-validator-manage.common"),f=i(h),p=t("hiloan:components/fin-fg/mixins/index"),m=t("hiloan:components/fin-rm/input-base/async"),v=i(m),b=t("hiloan:components/fin-rm/input-base/show"),y=i(b),x=t("hiloan:components/fin-rm/input-base/input.vue"),B=i(x);
r.default.use(l.default),r.default.use(c.default),r.default.use(f.default);var g=Object.assign?Object.assign:a.default.extend,_=[g({},p.inputBaseMixins[0]),g({},p.inputBaseMixins[1]),v.default,y.default];
e.default={mixins:_,data:function(){return{is:"rm-input-base"}},components:{rmInput:B.default},computed:{cFieldname:function(){return this.collect?this.fieldname:""},styleObject:function(){var t={};return this.titleWidth&&(t.width=this.titleWidth),t
},errorClass:function(){var t="";return"error"===this.tipState&&(t="error"),t},hasHeader:function(){var t=!1;return("only-header"===this.category||"header-button"===this.category)&&(t=!0),t},hasButton:function(){var t=!1;
return("only-button"===this.category||"header-button"===this.category)&&(t=!0),t},hasBorder:function(){return this.border||this.showTip?"has-border":""},hasBorderBottom:function(){var t="";return this.borderBottom&&(t="border-bottom"),this.hasBorder&&(t=""),t
},hasBorderTop:function(){return this.borderTop?"border-top":""},tipBorder:function(){return this.hasBorderBottom?"":"tip-border"}},methods:{iconClick:function(){this.$dispatch("icon-click",this)},buttonClick:function(){this.disabled||this.$dispatch("button-click",this)
}},beforeDestroy:function(){if(this.repairValidation){var t=this.fieldname;this.rules={empty:{rule:!0,message:""}},r.default.delete(this.$root.fieldsData,t),r.default.delete(this.$root.validation,t),delete this.$root._fieldsData[t]
}},ready:function(){if(this.repairValidation){var t=["valid","invalid","touched","untouched","modified","dirty","pristine","asyncDetail","asyncResult"],e=this.$root;e.$watch("validation",function(n){var i=Object.keys(n).filter(function(e){return-1===t.indexOf(e)
}),o=i.some(function(t){return!n[t].valid});e.validation.valid=o?!1:!0},{deep:!0})}}};var C='<!-- 【category分类】 -->\n<!-- 垂直有button:header-button -->\n<!-- 水平有button:only-button -->\n<!-- 垂直无button:only-header -->\n<!-- 输入错误样式：error -->\n<div class="fin-{{is}} {{category}} {{errorClass}} {{hasBorderBottom}} {{hasBorderTop}}">\n    <!--【1. 垂直时头部的信息】-->\n    <header class="head-area" v-if="hasHeader">\n        {{header}}\n        <i class="icon icon-info" v-if="icon" v-touch:tap="iconClick">\n        </i>\n    </header>\n    <section class="input-area {{hasBorder}}">\n        <!-- 【2. 输入区】 -->\n        <label>\n            <!-- 【输入框前面的文字】 -->\n            <span class="info" :style="styleObject" v-if="category!==\'only-header\'">\n                    {{title}}\n                <em class="subtitle" v-if="subtitle" v-text="subtitle">\n                </em>\n            </span>\n            <rm-input :type="type" v-ref:input="" :input-css="inputCss" :placeholder="placeholder" :readonly="readonly" :edit="edit" :min="min" :max="max" :maxlength="maxlength" :value.sync="value" :rules="rules" :fieldname="cFieldname" :record-input="recordInput" :base64="base64" :format="format" v-fieldset="fieldset"></rm-input>\n        </label>\n        <!-- 【3. 按钮区】 -->\n        <!-- 为了扩大热区,加事件时给这个div加 -->\n        <div class="control" v-if="hasButton" v-touch:tap="buttonClick">\n            <div class="fin-ui-button  primary x-small" :disabled="disabled" v-text="buttonText">\n            </div>\n        </div>\n    </section>\n    <!-- 【4. 信息提示区】 -->\n    <div v-show="showTip">\n        <!-- TODO:待出图 -->\n        <section class="tip" v-if="tipState===\'loading\'&amp;&amp;topLoading">\n            <i class="icon icon-info"></i>\n            <p v-text="tipContent"></p>\n        </section>\n\n        <section class="tip {{tipBorder}}" v-else="">\n            <i class="icon icon-info"></i>\n            <p v-html="tipContent"></p>\n        </section>\n    </div>\n</div>';
n&&n.exports&&(n.exports.template=C),e&&e.default&&(e.default.template=C),n.exports=e["default"]});
;define("hiloan:components/fin-rm/input-base/index",function(e,n,t){"use strict";function u(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var i=e("hiloan:components/fin-rm/input-base/input-base.vue"),o=u(i);
n.default=o.default,t.exports=n["default"]});
;define("hiloan:components/fin-ui/ui-select-input/async",function(s,a,e){"use strict";function t(s){return s&&s.__esModule?s:{"default":s}}Object.defineProperty(a,"__esModule",{value:!0});var n=s("hiloan:node_modules/underscore/underscore"),i=t(n),c=s("hiloan:app/static/config/api"),u=t(c),r="init",l="loading",o="true",y="false";
a.default={data:function(){return{hasAsync:!1,asyncState:r,asyncMessage:"",timer:null}},methods:{asyncFn:function(){var s=this;if(this.$validation.valid){var a=this.async.before.call(this,this.value);if(a===!1)return!1;
this.asyncState=l,this.asyncMessage=this.async.message.loading,u.default[this.async.apiName](a,{"x-silent":!0}).then(function(a){return s.asyncState!==l?!1:void(s.async.rule.call(s,a,s.value)?(s.asyncState=o,s.asyncMessage=s.async.message.success,s.async.success.call(s,a)):(s.asyncState=y,s.asyncMessage=s.async.message.error,s.async.error.call(s,a)))
})}},startAsync:function(){var s=this;this.hasAsync&&(clearTimeout(this.timer),this.asyncState=r,this.asyncMessage="",this.timer=setTimeout(function(){s.asyncFn()}))}},compiled:function(){i.default.isEmpty(this.async)?this.hasAsync=!1:(this.hasAsync=!0,this.startAsync())
}},e.exports=a["default"]});
;define("hiloan:components/fin-ui/ui-select-input/ui-select-input.vue",function(t,e,i){"use strict";function s(t){return t&&t.__esModule?t:{"default":t}}Object.defineProperty(e,"__esModule",{value:!0});
var a=t("hiloan:node_modules/vue/dist/vue.common"),n=s(a),l=t("hiloan:node_modules/vue-touch/vue-touch"),o=s(l),r=t("hiloan:node_modules/vue-validator/dist/vue-validator.common"),u=s(r),d=t("hiloan:node_modules/vue-validator-manage/dist/vue-validator-manage.common"),h=s(d),c=t("hiloan:components/fin-fg/util/index"),v=t("hiloan:components/fin-fg/mixins/index"),f=t("hiloan:components/fin-ui/ui-select-input/async"),p=s(f);
n.default.use(o.default),n.default.use(u.default),n.default.use(h.default);var m=c.store.local;e.default={mixins:[v.select,p.default,v.inputValidNotice],data:function(){return{is:"ui-select-input"}},computed:{styleObject:function(){var t={};
return this.titleWidth&&(t.width=this.titleWidth),t},textShow:function(){var t=this.text;return this.text||(t=this.placeholder),t},hasBorder:function(){var t="";return(this.tip||this.hasErrorClass)&&(t="border-bottom"),t
},hasPlaceholder:function(){return this.disabled?"placeholder":""},largeTip:function(){var t="";return"large"===this.size&&(t="large-tip"),t},message:function(){var t=this.tip;if(this.hasErrorClass)if(this.$validation.invalid){var e=this.$validation.value.errors||[];
e.length>0&&(t=e[0].message)}else"false"===this.asyncState&&(t=this.asyncMessage);return t},errorClass:function(){var t="";return this.hasErrorClass&&(t="error"),t},hasErrorClass:function(){var t=!1;return this.$validation.invalid?(this.topError||this.$validation.dirty)&&(t=!0):"false"===this.asyncState&&(t=!0),t
},showMessage:function(){var t=!1;return(this.hasErrorClass||this.tip)&&(t=!0),t}},methods:{selectClick:function(){this.focusToBlur=!0,this.$dispatch("select-click")}},events:{"show-error-message":function(){this.topError=!0
}},watch:{value:function(t){this.recordInput&&(this.text||(this.text=t),m.setItem(this.fieldname,{value:t,text:this.text})),this.hasAsync&&this.startAsync()}},created:function(){if(this.recordInput){var t=m.getItem(this.fieldname);
if(t&&t.value)this.value=t.value,this.text=t.text;else{this.text||(this.text=this.value);var e=this.value,i=void 0===e?"":e,s=this.text,a=void 0===s?"":s;m.setItem(this.fieldname,{value:i,text:a})}}}};
var x='<div class="ui-select-input ">\n    <validator name="validation">\n        <div class="select clearfix {{size}} {{border}} {{hasBorder}} {{hasPlaceholder}}" v-touch:tap="selectClick">\n            <span class="label" v-text="title" :style="styleObject">\n            </span>\n            <p class="con" v-text="textShow" :class="{placeholder:!text}">\n            </p>\n            <i class="icon {{icon}}"></i>\n        </div>\n        <section class="tip  {{largeTip}} {{errorClass}}" v-if="showMessage">\n            <i class="icon icon-info"></i>\n            <p v-text="message"></p>\n        </section>\n        <input type="hidden" v-fieldname="fieldname" v-model="value" :base64="base64" v-validate:value="rules" @valid="invalidToValid">\n    </validator>\n</div>';
i&&i.exports&&(i.exports.template=x),e&&e.default&&(e.default.template=x),i.exports=e["default"]});
;define("hiloan:components/fin-ui/ui-select-input/index",function(e,t,u){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var i=e("hiloan:components/fin-ui/ui-select-input/ui-select-input.vue"),l=n(i);
t.default=l.default,u.exports=t["default"]});
;define("hiloan:components/fin-ui/ui-select/ui-select.vue",function(e,t,s){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var l=e("hiloan:node_modules/vue/dist/vue.common"),n=i(l),o=e("hiloan:node_modules/iscroll/build/iscroll"),c=i(o),u=e("hiloan:node_modules/vue-touch/vue-touch"),d=i(u),a=e("hiloan:components/fin-ui/ui-mask/index"),h=i(a);
n.default.use(d.default),t.default={props:{title:{type:String,required:!1,"default":""},list:{type:Array,required:!0,"default":function(){return[]}},confirm:{type:Boolean,required:!1,"default":null},twoList:{type:Boolean,required:!1,"default":null}},data:function(){return{is:"ui-select",showItem:!1,selectedNum:-1,selectedNumSecond:-1,listSecond:this.list.length&&this.list[0].sublist,subtitle:this.title}
},components:{"ui-mask":h.default},methods:{cancel:function(){this.hide()},choose:function(e){var t=this;this.selectedNum=e,this.twoList?(this.listSecond=this.list[e].sublist,this.selectedNumSecond=-1,n.default.nextTick(function(){t.subScroll.refresh()
})):(this.$dispatch("selected",this,this.list[e]),this.hide())},chooseTwo:function(e){this.selectedNumSecond=e;var t={};t.text=this.list[this.selectedNum].text+"-"+this.listSecond[e].text,t.value=this.list[this.selectedNum].value+"-"+this.listSecond[e].value,this.$dispatch("selected",this,t),this.hide()
},show:function(e){if(-1===this.selectedNum)if(this.twoList)if(""===e)this.selectedNum=0;else{var t=e.split("-");this.selectedNum=this.queryIndex(t[0],this.list),2===t.length&&-1!==this.selectedNum?this.selectedNumSecond=this.queryIndex(t[1],this.listSecond):this.selectedNum=0
}else""!==e&&(this.selectedNum=this.queryIndex(e,this.list));document.addEventListener("touchmove",this.disableTouchmove,!1),this.showItem=!0},hide:function(){this.showItem=!1,document.removeEventListener("touchmove",this.disableTouchmove)
},disableTouchmove:function(e){e.preventDefault()},queryIndex:function(e,t){for(var s=0;s<t.length;s++)if(e===t[s].value)return s;return-1}},watch:{list:function(){this.iscroll&&this.iscroll.refresh()}},ready:function(){var e=this,t=function(e,t){var s={};
return s=t===!0?{scrollbars:!0,fadeScrollbars:!0,preventDefaultException:{className:/(^|\s)underline(\s|$)/}}:{scrollbars:!1,preventDefaultException:{className:/(^|\s)underline(\s|$)/}},new c.default(e,s)
};this.$nextTick(function(){e.twoList&&(e.subScroll=t(e.$els.selectb,!0)),e.iscroll=e.list.length<7?t(e.$els.selecta,!1):t(e.$els.selecta,!0)})}};var r='<div>\n  <ui-mask :show="showItem" v-touch:tap="cancel">\n  </ui-mask>\n\n  <div class="fin-ui-select" :class="{\'show-select\':showItem}">\n      <div class="title-box">\n          <span class="cancel" v-touch:tap="cancel">\n              取消\n          </span>\n          <span class="select-title">\n              {{ subtitle }}\n          </span>\n      </div>\n      <div class="scrollbars-box" :class="{\'two-list left\':twoList}">\n          <div class="content-box" v-el:selecta="">\n              <ul class="options">\n                  <li :class="{\'selected\':selectedNum === index}" v-touch:tap="choose(index)" v-for="(index, item) in list">\n                      <span class="underline">\n                          {{ item.text }}\n                      </span>\n                  </li>\n              </ul>\n          </div>\n      </div>\n      <div class="scrollbars-box two-list right" v-if="twoList">\n          <div class="content-box" v-el:selectb="">\n              <ul class="options">\n                  <li :class="{\'selected\':selectedNumSecond === index}" v-touch:tap="chooseTwo(index)" v-for="(index, item) in listSecond">\n                      <span class="underline">\n                          {{ item.text }}\n                      </span>\n                  </li>\n              </ul>\n          </div>\n      </div>\n  </div>\n</div>';
s&&s.exports&&(s.exports.template=r),t&&t.default&&(t.default.template=r),s.exports=t["default"]});
;define("hiloan:components/fin-ui/ui-select/index",function(e,t,u){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var i=e("hiloan:components/fin-ui/ui-select/ui-select.vue"),l=n(i);
t.default=l.default,u.exports=t["default"]});
;define("hiloan:components/fin-rm/city-picker/city-picker.vue",function(t,e,n){"use strict";function i(t){return t&&t.__esModule?t:{"default":t}}Object.defineProperty(e,"__esModule",{value:!0});var s=t("hiloan:node_modules/vue/dist/vue.common"),o=i(s),r=t("hiloan:node_modules/iscroll/build/iscroll"),a=i(r),c=t("hiloan:node_modules/vue-touch/vue-touch"),u=i(c),l=t("hiloan:app/static/config/api"),d=i(l),h=t("hiloan:components/fin-ui/ui-mask/index"),p=i(h);
o.default.use(u.default);var v="province",f="country",m="city",y=[v,m,f];e.default={data:function(){return{is:"city-picker",province:[],city:[],country:[],selectMap:{},showCity:!1,showCountry:!1,innerHeight:"300px",code:null,show:!1,pIndex:0,cIndex:0,sIndex:null}
},methods:{getDataList:function(t){var e={type:t};return this.code&&(e.code=this.code),d.default.getCityMenuList(e,{"x-silent":!0})},preFilterData:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:f;
this.getDataList(e).then(function(n){var i=e.charAt(0).toUpperCase()+e.slice(1);if(t.code=null,0===n.data.length&&e===f)return delete t.selectMap[f],t.showCountry=!1,t.selected(),!1;if(t.repaintUi(e,n.data,i),e!==f){var s=y.indexOf(e)+1;
t.code=n.data[0].code,e===m&&(t.cIndex=0),y[s]&&e&&t.preFilterData(y[s])}})},repaintUi:function(t,e,n){var i=this;this.$set(t,e),this["show"+n]=!0,o.default.nextTick(function(){i.bindIScroll(i.$els[t])
})},bindIScroll:function(t){t&&new a.default(t.children[0])},getSelectItem:function(t,e){var n=t+"["+e+"]",i=this.$get(n+".name"),s=this.$get(n+".code");return{name:i,code:s}},from:function(t,e,n){switch(this.code=n,t){case m:this.showCountry=!1,this.city=[],this.country=[],this.pIndex=e,this.cIndex=this.sIndex=null,this.storeCurrentData(v,e),delete this.selectMap[m];
break;case f:this.country=[],this.cIndex=e,this.sIndex=null,this.storeCurrentData(m,e);break;case"select":this.sIndex=e,this.selectMap.hasOwnProperty(m)||this.storeCurrentData(m,0),this.storeCurrentData(f,e),this.selected()
}"select"!==t&&this.preFilterData(t,e)},store:function(t,e){this.selectMap[t]=e},storeCurrentData:function(t,e){this.store(t,this.getSelectItem(t,e))},selected:function(){this.hideUi(),this.$dispatch("selected",this,this.getValue())
},getValue:function(){var t=this.selectMap,e=t.province,n=t.city,i=t.country;return i=i?"-"+i:"",{text:e.name+"-"+n.name+i,value:this.selectMap}},hideUi:function(){document.ontouchmove=null,this.show=!1
},showUi:function(){document.ontouchmove=function(){return!1},this.show=!0,this.repaint()},repaint:function(){var t=this,e=this.$els,n=e.wrapper,i=e.province,s=e.city,r=e.country,a=e.header;o.default.nextTick(function(){var e=n.offsetHeight;
n.style.height=e+"px",t.innerHeight=e-a.offsetHeight+"px"}),setTimeout(function(){t.bindIScroll(i),t.bindIScroll(s),t.bindIScroll(r)},1)},getList:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:v,e=arguments[1],n={type:t};
return e&&(n.code=e),d.default.getCityMenuList(n,{"x-silent":!0})}},created:function(){var t=this,e=function(e,n){return t.getList(e,n)};e(v).then(function(e){var n=e.data;return t.$set(v,n),t.storeCurrentData(v,0),t.getList(m,n[0].code)
}).then(function(e){var n=e.data;return t.$set(m,n),t.storeCurrentData(m,0),t.getList(m,n[0].code)}).then(function(e){var n=e.data;t.$set(f,n),t.storeCurrentData(f,0)})},components:{uiMask:p.default}};
var g='<div ontouchmove="return false">\n    <section class="fin-{{ is }}" v-show="show" v-el:wrapper="">\n        <div class="fin-{{ is }}-title" v-el:header="">\n            <span class="close" v-touch:tap.stop="hideUi">取消</span>\n            <h3>选择城市</h3>\n        </div>\n        <div class="fin-{{ is }}-content">\n            <div class="fin-{{ is }}-province" v-el:province="">\n                <div :style="{height: innerHeight}">\n                    <ul>\n                        <li v-for="(index, item) in province" v-touch:tap.stop="from(\'city\', index, item.code)" class="{{ index === pIndex ? \'current\' : \'\' }}">\n                            <span>{{ item.name }}</span>\n                        </li>\n                    </ul>\n                </div>\n            </div>\n            <div class="fin-{{ is }}-city" v-el:city="">\n                <div :style="{height: innerHeight}">\n                    <ul>\n                        <li v-for="(index, item) in city" v-touch:tap.stop="from(\'country\', index, item.code)" class="{{ index === cIndex ? \'current\' : \'\'}} ">\n                            <span>{{ item.name }}</span>\n                        </li>\n                    </ul>\n                </div>\n            </div>\n            <div class="fin-{{ is }}-country" v-el:country="">\n                <div :style="{height: innerHeight}">\n                    <ul>\n                        <li v-for="(index, item) in country" v-touch:tap.stop="from(\'select\', index, item.code)" class="{{index === sIndex ? \'current\' : \'\'}}">\n                            <span>{{ item.name }}</span>\n                        </li>\n                    </ul>\n                </div>\n            </div>\n    </div></section>\n    <ui-mask :show.sync="show" @tap="hideUi()"></ui-mask>\n</div>';
n&&n.exports&&(n.exports.template=g),e&&e.default&&(e.default.template=g),n.exports=e["default"]});
;define("hiloan:components/fin-rm/city-picker/index",function(e,t,i){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var c=e("hiloan:components/fin-rm/city-picker/city-picker.vue"),o=n(c);
t.default=o.default,i.exports=t["default"]});
;define("hiloan:components/fin-rm/rm-select/rm-select.vue",function(e,t,i){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e
}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},l=e("hiloan:node_modules/underscore/underscore"),c=n(l),s=e("hiloan:node_modules/vue/dist/vue.common"),r=n(s),u=e("hiloan:components/fin-fg/config/index"),f=e("hiloan:components/fin-fg/mixins/index"),a=e("hiloan:components/fin-fg/util/index"),d=e("hiloan:components/fin-ui/ui-select-input/index"),p=n(d),h=e("hiloan:components/fin-ui/ui-select/index"),m=n(h),y=e("hiloan:components/fin-rm/city-picker/index"),v=n(y);
t.default={mixins:[f.select],props:{type:{type:String,"default":""},currentView:{type:String,"default":""},fieldset:{type:String,"default":""},selectTitle:{type:String,"default":""},list:{type:Array,"default":function(){return[]
}},confirm:{type:Boolean,"default":!0},twoList:{type:Boolean,"default":!1},jumpUrl:{type:String,"default":""},collect:{type:Boolean,"default":!0}},data:function(){return{is:"rm-select",showContent:!0}},computed:{cFieldname:function(){return this.collect?this.fieldname:""
}},components:{uiSelectInput:p.default,uiSelect:m.default,cityPicker:v.default},methods:{selectClick:function(){if(this.currentView){var e=function(){if("uiSelect"===this.currentView)this.$refs.selectContent.show(this.value);
else if("cityPicker"===this.currentView){var e=this.$refs.selectContent;e.showUi(),r.default.nextTick(function(){e.bindIScroll(e.$els.province)})}},t=document.body||document.documentElement,i=this.$el.querySelectorAll(".fix-height")[0],n=(i.clientHeight,this);
this.showContent=!1;var o=function l(){i.clientHeight<603&&t.clientHeight>=603?setTimeout(function(){l()},50):(n.showContent=!0,e.call(n))};setTimeout(function(){o()},50)}else if("schoolSelect"===this.type)return a.redirect(this.jumpUrl),!0;
return!0},selected:function(e,t){if("cityPicker"===this.currentView){var i=t.value.province.name,n=t.value.city.name,o=t.value.country&&t.value.country.name,l=function(e){return e.length>5?e.slice(0,4)+"..":e
};this.value=o?i+"-"+n+"-"+o:i+"-"+n,i=l(i),n=l(n),o=o&&l(o),this.text=o?i+"-"+n+"-"+o:i+"-"+n,i===n&&(this.text=n+"-"+o)}else this.text=t.text,this.value=t.value;return!0}},created:function(){var e=this,t="";
if(Object.keys(u.select).some(function(i){return i===e.type?(t=i,!0):!1}),t){var i=u.select[t];Object.keys(i).forEach(function(t){"string"==typeof e[t]?e[t]?"emptyString"===e[t]&&(e[t]=""):e[t]=i[t]:"object"===o(e[t])&&c.default.isEmpty(e[t])&&(e[t]=i[t])
})}if(this.icon||(this.icon="icon-arrow-right"),"uiSelect"===this.currentView&&!this.text){var n="";this.list.forEach(function(t){return t.value===e.value?(n=t.text,!0):void 0}),this.text=n}}};var x='<div class="fin-{{is}}">\n    <ui-select-input :icon="icon" :border="border" :size="size" :title="title" :placeholder="placeholder" :disabled="disabled" :tip="tip" :value.sync="value" :text.sync="text" :fieldname="cFieldname" v-fieldset="fieldset" :rules="rules" :top-error="topError" :record-input="recordInput" :base64="base64" :async="async" :title-width="titleWidth" @select-click="selectClick">\n    </ui-select-input>\n    <!-- 必须统一接口 -->\n    <component v-ref:select-content="" v-show="showContent" :is="currentView" :list="list" :title="selectTitle" :confirm="confirm" :two-list="twoList" @selected="selected">\n    </component>\n    <div class="fix-height" style="position: fixed;bottom:0;top:0"></div>\n</div>';
i&&i.exports&&(i.exports.template=x),t&&t.default&&(t.default.template=x),i.exports=t["default"]});
;define("hiloan:components/fin-rm/rm-select/index",function(e,t,n){"use strict";function l(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var o=e("hiloan:components/fin-rm/rm-select/rm-select.vue"),r=l(o);
t.default=r.default,n.exports=t["default"]});
;define("hiloan:components/fin-rm/area/area.vue",function(e,t,r){"use strict";function a(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var l=e("hiloan:node_modules/vue/dist/vue.common"),n=a(l),o=e("hiloan:node_modules/vue-validator/dist/vue-validator.common"),d=a(o),u=e("hiloan:node_modules/vue-validator-manage/dist/vue-validator-manage.common"),i=a(u),s=e("hiloan:components/fin-rm/input-base/index"),f=a(s),p=e("hiloan:components/fin-rm/rm-select/index"),m=a(p);
n.default.use(d.default),n.default.use(i.default),t.default={props:{header:{type:String,"default":"详细地址"},icon:{type:Boolean,"default":!1},title:{type:String,"default":"所在地区"},placeholders:{type:Array,"default":function(){return["请选择地区","请填写详细地址"]
}},tip:{type:String,"default":""},border:{type:Boolean,"default":!1},borderTop:{type:Boolean,"default":!1},borderBottom:{type:Boolean,"default":!0},maxlength:{type:Number,"default":50},values:{type:Array,"default":function(){return["",""]
}},text:{type:String,"default":""},rules:{type:Array,"default":function(){return[{required:{rule:!0,message:"请选择地区"}},{required:{rule:!0,message:"请填写详细地址"}}]}},fieldnames:{type:Array,"default":function(){return["currentCity","currentAddress"]
}},fieldsets:{type:Array,"default":function(){return["currentCity","currentAddress"]}}},data:function(){return{is:"rm-area"}},computed:{fieldset1:function(){return this.fieldsets[1]}},components:{inputBase:f.default,rmSelect:m.default}};
var c='<div class="fin-{{is}}">\n    <rm-select type="currentCity" :title="title" :placeholder="placeholders[0]" :text="text" :value="values[0]" :fieldname="fieldnames[0]" :fieldset="fieldsets[0]" :rules="rules[0]" border="border-bottom">\n    </rm-select>\n    <input-base category="only-header" :tip="tip" :icon="icon" :header="header" :border="border" :maxlength="maxlength" :border-top="borderTop" :border-bottom="borderBottom" :placeholder="placeholders[1]" :value.sync="values[1]" :fieldname="fieldnames[1]" :fieldset="fieldset1" :rules="rules[1]">\n    </input-base>\n</div>';
r&&r.exports&&(r.exports.template=c),t&&t.default&&(t.default.template=c),r.exports=t["default"]});
;define("hiloan:components/fin-rm/area/index",function(e,n,a){"use strict";function t(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var o=e("hiloan:components/fin-rm/area/area.vue"),r=t(o);
n.default=r.default,a.exports=n["default"]});
;define("hiloan:components/fin-ui/ui-input-list/ui-input-list.vue",function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function t(t,e){var i=[],n=!0,r=!1,u=void 0;
try{for(var l,a=t[Symbol.iterator]();!(n=(l=a.next()).done)&&(i.push(l.value),!e||i.length!==e);n=!0);}catch(o){r=!0,u=o}finally{try{!n&&a["return"]&&a["return"]()}finally{if(r)throw u}}return i}return function(e,i){if(Array.isArray(e))return e;
if(Symbol.iterator in Object(e))return t(e,i);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}();e.default={props:{list:{type:Array,"default":function(){return[]}},show:{type:Boolean,"default":!1},inputValue:{type:String,"default":""}},data:function(){return{is:"ui-input-list"}
},computed:{realyList:function(){var t=this.inputValue.split("@"),e=n(t,2),i=e[0],r=e[1],u=this.list;return r&&(u=this.list.filter(function(t){return-1!==t.indexOf(r)?!0:!1})),u.map(function(t){return i+"@"+t
})}},methods:{itemClick:function(t){this.$dispatch("selected",t,this)}}};var r='<ul class="fin-{{is}}" v-show="show">\n    <li v-for="item in realyList" @click="itemClick(item)">\n        {{item}}\n    </li>\n</ul>';
i&&i.exports&&(i.exports.template=r),e&&e.default&&(e.default.template=r),i.exports=e["default"]});
;define("hiloan:components/fin-ui/ui-input-list/index",function(e,i,t){"use strict";function u(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(i,"__esModule",{value:!0});var n=e("hiloan:components/fin-ui/ui-input-list/ui-input-list.vue"),l=u(n);
i.default=l.default,t.exports=i["default"]});
;define("hiloan:components/fin-rm/rm-input/list",function(t,i,n){"use strict";function e(t){return t&&t.__esModule?t:{"default":t}}Object.defineProperty(i,"__esModule",{value:!0});var s=t("hiloan:components/fin-ui/ui-input-list/index"),u=e(s);
i.default={props:{list:{type:Array,"default":function(){return[]}}},events:{focus:function(){return this.currentView&&this.showListFn(!0),!0},input:function(){return this.currentView&&this.showListFn(!0),!0
},blur:function(){var t=this;return this.currentView&&setTimeout(function(){t.showListFn(!1)},10),!0}},components:{uiInputList:u.default},methods:{showListFn:function(t){return this.showList=t,this.topError=t?"false":"",this.value||(this.showList=!1,this.topError=""),!0
},selected:function(t){return this.value=t,this.showList=!1,!0}}},n.exports=i["default"]});
;define("hiloan:components/fin-rm/rm-input/rm-input.vue",function(t,e,o){"use strict";function n(t){return t&&t.__esModule?t:{"default":t}}Object.defineProperty(e,"__esModule",{value:!0});var i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t
}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},r=t("hiloan:node_modules/underscore/underscore"),s=n(r),a=t("hiloan:node_modules/vue/dist/vue.common"),l=(n(a),t("hiloan:components/fin-rm/rm-input/list")),u=n(l),p=t("hiloan:components/fin-rm/input-base/index"),d=n(p),c=t("hiloan:components/fin-fg/mixins/index"),f=t("hiloan:components/fin-fg/config/index"),m=Object.assign?Object.assign:s.default.extend,b=[m({},c.inputBaseMixins[0]),m({},c.inputBaseMixins[1]),u.default];
e.default={mixins:b,props:{type:{type:String,"default":""},inputType:{type:String,"default":""},currentView:{type:String,"default":""}},data:function(){return{is:"rm-input",showList:!1}},components:{inputBase:d.default},created:function(){var t=this,e="";
if(Object.keys(f.input).some(function(o){return o===t.type?(e=o,!0):!1}),e){var o=f.input[e];Object.keys(o).forEach(function(e){"string"==typeof t[e]?t[e]?("icon"!==e||t.icon||(t.icon="icon-arrow-right"),"emptyString"===t[e]&&(t[e]="")):t[e]=o[e]:"object"===i(t[e])&&s.default.isEmpty(t[e])&&(t[e]=o[e])
})}},beforeCompile:function(){var t=this,e=this.border;if("string"==typeof e){var o={top:"borderTop",bottom:"borderBottom",offset:"border"},n=[],i=Object.keys(o);e.replace(/(\S)+/g,function(t){-1!==i.indexOf(t)&&n.push(t)
}),n.length>0?(this.border=!1,this.borderTop=!1,this.borderBottom=!1,n.forEach(function(e){var n=o[e];t[n]=!0})):this.border=!1}}};var y='<div class="fin-{{is}}">\n    <input-base :type="inputType" :placeholder="placeholder" :input-css="inputCss" :edit="edit" :readonly="readonly" :min="min" :max="max" :maxlength="maxlength" :value.sync="value" :fieldname="fieldname" :fieldset="fieldset" :rules="rules" :repair-validation="repairValidation" :tip="tip" :top-error="topError" :top-loading="topLoading" :async="async" :record-input="recordInput" :base64="base64" :format="format" :collect="collect" :border="border" :border-top="borderTop" :border-bottom="borderBottom" :title="title" :title-width="titleWidth" :subtitle="subtitle" :category="category" :header="header" :icon="icon" :button-text="buttonText" :disabled="disabled">\n    </input-base>\n    <!-- 必须统一接口 -->\n    <component v-ref:input-tips="" :is="currentView" :input-value.sync="value" :list="list" :show="showList" @selected="selected">\n    </component>\n</div>';
o&&o.exports&&(o.exports.template=y),e&&e.default&&(e.default.template=y),o.exports=e["default"]});
;define("hiloan:components/fin-rm/rm-input/index",function(e,n,t){"use strict";function u(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var i=e("hiloan:components/fin-rm/rm-input/rm-input.vue"),o=u(i);
n.default=o.default,t.exports=n["default"]});
;define("hiloan:components/fin-rm/bankcard-info/bankcard-info.vue",function(e,a,n){"use strict";function t(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(a,"__esModule",{value:!0});var i=e("hiloan:node_modules/vue/dist/vue.common"),s=t(i),l=e("hiloan:node_modules/vue-touch/vue-touch"),u=t(l),c=e("hiloan:node_modules/vue-validator/dist/vue-validator.common"),o=t(c),m=e("hiloan:app/static/config/api"),r=t(m),k=e("hiloan:components/fin-ui/ui-select-input/index"),b=t(k),d=e("hiloan:components/fin-rm/rm-input/index"),h=t(d),p=e("hiloan:components/fin-rm/rm-select/index"),f=t(p);
s.default.use(o.default),s.default.use(u.default);var N="province",v="city";a.default={props:{bankcard:{type:Object,"default":function(){return{bankName:"",bankProvince:"",bankCity:"",bankNameDetail:""}
}}},data:function(){return{is:"bankcard-info",headTitle:"",bankProvinceValue:this.bankcard.bankProvince,provinceData:[],provinceMapWithCode:{},bankCityValue:this.bankcard.bankCity,cityData:[],showBankNameSelect:!1,hotBank:[],generalBank:[],bankNameValue:this.bankcard.bankName,bankNameText:this.bankcard.bankName,bankNameRules:{required:{rule:!0,message:"请选择开户银行名称"}},showSubBankNameSelect:!1,subBankNameData:[],subBankNameFilterData:[],subBankNameInputValue:"",timerSubBankNameInput:null,subBankNameValue:this.bankcard.bankNameDetail,subBankNameText:this.bankcard.bankNameDetail,bankNameDetailRules:{required:{rule:!0,message:"请选择支行名称"}}}
},components:{uiSelectInput:b.default,rmInput:h.default,rmSelect:f.default},watch:{bankNameValue:function(e,a){a&&e!==a&&this.clearSubBankNameData()},bankProvinceValue:function(e,a){var n=this;if(a&&e!==a&&(this.$refs.bankCity.text="",this.bankCityValue="",this.clearSubBankNameData()),!a||e!==a){var t=this.provinceMapWithCode[e];
this.getAddList(v,t).then(function(e){n.cityData=e.data.map(function(e){return{text:e.name,value:e.name}})})}},bankCityValue:function(e,a){a&&e!==a&&this.clearSubBankNameData()}},computed:{bankInfoComplete:function(){var e=this.bankNameValue&&this.bankProvinceValue&&this.bankCityValue;
return e&&(this.$refs.subBankName.tip=""),e}},methods:{selectBankName:function(){this.showBankNameSelect=!0},closeBankName:function(){this.showBankNameSelect=!1},chooseBankName:function(e){this.closeBankName(),this.bankNameValue=e.bankName,this.bankNameText=e.displayName
},chooseSubBankName:function(e){this.closeSubBankName(),this.subBankNameValue=e,this.subBankNameText=e,this.subBankNameInputValue=""},closeSubBankName:function(){this.showSubBankNameSelect=!1},clearSubBankNameData:function(){this.subBankNameValue="",this.subBankNameText="",this.subBankNameInputValue="",this.subBankNameData=[],this.subBankNameFilterData=[]
},getAddList:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:N,a=arguments[1],n={type:e};return a&&(n.code=a),r.default.getCityMenuList(n,{"x-silent":!0})},selectSubBankName:function(){var e=this;
if(!this.bankInfoComplete)return void(this.$refs.subBankName.tip="请先选择前三个选项");var a={bankName:this.bankNameValue,province:this.bankProvinceValue,city:this.bankCityValue};r.default.getBankBranchList(a).then(function(a){a.data&&(e.showSubBankNameSelect=!0,e.subBankNameFilterData=a.data)
})}},events:{input:function(e){var a=this;"subBanknameSelect"===e.fieldname&&(clearTimeout(this.timerSubBankNameInput),this.timerSubBankNameInput=setTimeout(function(){var e={bankName:a.bankNameValue,province:a.bankProvinceValue,city:a.bankCityValue,bankNameDetail:a.subBankNameInputValue};
r.default.getBankBranchList(e).then(function(e){e.data&&(a.subBankNameFilterData=e.data)})},500))}},created:function(){var e=this;r.default.getAuthInfo(null,{"x-silent":!0}).then(function(a){var n=a.data.bankcard;
n=n.replace(/\D/g,""),e.headTitle="请填写您的收款银行卡（尾号"+n+"）信息"}),r.default.getPaymentBankList(null,{"x-silent":!0}).then(function(a){a.data.forEach(function(a){1===+a.isHot?e.hotBank.push(a):e.generalBank.push(a)
})}),this.getAddList().then(function(a){e.provinceData=a.data.map(function(a){return e.provinceMapWithCode[a.name]=a.code,{text:a.name,value:a.name}})})}};var B='<section class="fin-rm-bankcard-info">\n    <h2 class="head-title" :class="{\'hold\': !headTitle}"><i class="icon icon-info-revert"></i>{{ headTitle }}</h2>\n    <ui-select-input icon="icon-arrow-right" border="border-bottom" title="开户银行名称" placeholder="选择" :value.sync="bankNameValue" :text.sync="bankNameText" fieldname="bankName" v-fieldset="bankName" :rules="bankNameRules" @select-click="selectBankName">\n    </ui-select-input>\n    <rm-select type="bankProvince" fieldname="bankProvince" fieldset="bankProvince" border="border-bottom" :value.sync="bankProvinceValue" :list="provinceData">\n    </rm-select>\n    <rm-select v-ref:bank-city="" type="bankCity" fieldname="bankCity" fieldset="bankCity" border="border-bottom" :value.sync="bankCityValue" :list="cityData">\n    </rm-select>\n    <ui-select-input v-ref:sub-bank-name="" class="sub-bank-name" :class="{\'disabled\': !bankInfoComplete}" icon="icon-arrow-right" title="支行名称" placeholder="选择" :value.sync="subBankNameValue" :text.sync="subBankNameText" fieldname="bankNameDetail" v-fieldset="bankNameDetail" :rules="bankNameDetailRules" @select-click="selectSubBankName">\n    </ui-select-input>\n\n    <!-- 选择弹层: 开户银行名称 -->\n    <div class="bankname-select" @click="closeBankName()" v-show="showBankNameSelect" transition="fade">\n        <p class="title">热门银行</p>\n        <ul class="options">\n            <li v-for="item in hotBank" @click.stop="" v-touch:tap="chooseBankName(item)" :class="{\'selected\': bankNameValue === item.bankName}">\n                    <span class="underline">\n                        {{ item.displayName }}\n                    </span>\n            </li>\n        </ul>\n        <p class="title-other">其他银行</p>\n        <ul class="options">\n            <li v-for="item in generalBank" @click.stop="" v-touch:tap="chooseBankName(item)" :class="{\'selected\': bankNameValue === item.bankName}">\n                    <span class="underline">\n                        {{ item.displayName }}\n                    </span>\n            </li>\n        </ul>\n    </div>\n\n    <!-- 选择弹层: 开户支行名称选择 -->\n    <div class="sub-bankname-select" v-show="showSubBankNameSelect" @click="closeSubBankName()" transition="fade">\n        <div class="input-wrap" @click.stop="">\n            <rm-input v-ref:sub-bankname-select="" placeholder="输入支行名称关键词" fieldname="subBanknameSelect" :record-input="false" :value.sync="subBankNameInputValue">\n            </rm-input>\n        </div>\n        <div class="no-result" v-show="subBankNameFilterData.length === 0" transition="pop">\n            <p>暂未检索到任何信息</p>\n            <p>请检查输入信息是否正确</p>\n        </div>\n        <div class="separate-scroll">\n            <ul class="options" v-else="" transition="fade">\n                <li v-for="item in subBankNameFilterData" @click.stop="" v-touch:tap="chooseSubBankName(item)" :class="{\'selected\': subBankNameValue === item}">\n                    <span class="underline">\n                        {{ item }}\n                    </span>\n                </li>\n            </ul>\n        </div>\n    </div>\n</section>';
n&&n.exports&&(n.exports.template=B),a&&a.default&&(a.default.template=B),n.exports=a["default"]});
;define("hiloan:components/fin-rm/bankcard-info/index",function(e,n,o){"use strict";function a(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var t=e("hiloan:components/fin-rm/bankcard-info/bankcard-info.vue"),f=a(t);
n.default=f.default,o.exports=n["default"]});
;define("hiloan:components/fin-rm/select-phone/patch",function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var n=e.$root,a=e.fieldsetSelect,i=e.fieldsetInput;
if(n.validation.asyncDetail[a]=t+"",n.validation.asyncDetail[i]=t+"",t){var l=[],o=Object.keys(n.validation.asyncDetail);o.forEach(function(e){l.push(n.validation.asyncDetail[e])});var s="true",c=l.every(function(e){return"true"===e
}),r=l.every(function(e){return"loading"===e});s=c?"true":r?"loading":"init",n.validation.asyncResult=s}else n.validation.asyncResult="false"},n.exports=t["default"]});
;define("hiloan:components/fin-rm/select-phone/select-phone.vue",function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var l=e("hiloan:node_modules/underscore/underscore"),o=i(l),u=e("hiloan:components/fin-fg/util/index"),a=e("hiloan:app/static/config/api"),s=i(a),c=e("hiloan:components/fin-fg/util/ui/index"),r=e("hiloan:components/fin-rm/rm-select/index"),h=i(r),p=e("hiloan:components/fin-rm/rm-input/index"),f=i(p),d=e("hiloan:components/fin-rm/select-phone/patch"),m=i(d);
t.default={props:{maxNum:{type:Number,"default":350},value:{type:String,"default":""},fieldname:{type:String,"default":"relativePhone"},collect:{type:Boolean,"default":!0},base64:{type:Boolean,"default":!1},recordInput:{type:Boolean,"default":!0},phoneBook:{type:String,"default":""},checking:{type:Boolean,"default":!1},manualInput:{type:Boolean,"default":!1}},data:function(){return{is:"select-phone",auth:!0,topError:"",uuid:"phoneBook",rulesInputInit:{},checkPhone:{apiName:"checkContact",before:function(e){return this.base64&&(e=u.base64.encode(e)),{mobile:e}
},rule:function(e){var t=0===e.data.mobile;return m.default(this.$parent.$parent,t),t},message:{error:"请勿填写申请者本人手机号"}}}},computed:{fieldnameSelect:function(){return this.auth?this.fieldname:""},fieldnameInput:function(){return this.auth?"":this.fieldname
},iCollect:function(){return this.collect&&!/\*{1,}/.test(this.value)},fieldsetSelect:function(){return this.fieldname+"_select"},fieldsetInput:function(){return this.fieldname+"_input"},checkPhoneSelect:function(){var e={};
return this.checking&&this.auth&&(e=this.checkPhone),e},checkPhoneInput:function(){var e={};return this.checking&&!this.auth&&(e=this.checkPhone),e}},methods:{openManualInput:function(){this.collect=!0,this.auth=!1,this.topError="true"
},selectClick:function(){var e=this;u.native.get("getLocalUserAgent",{type:2,maxNum:this.maxNum,base64:1}).then(function(t){return e.value=t.selected.phone,e.collect=!0,e.putFile(t)}).then(function(t){e.phoneBook=t.data.uuid,e.$dispatch("put-file",e)
}).catch(function(t){e.manualInput?e.openManualInput():t.errCode&&"10002"!==t.errCode&&e.openManualInput()})},putFile:function(e){var t="putFile";s.default.putList&&(t="putList");var n=e.all;return Array.isArray(n[0])||(n=[n]),n||n[0]?s.default[t]({file:u.base64.encode(JSON.stringify(n)),fileType:"pb"},{"x-timeout":3e4}):(c.dialog({type:"alert",content:"获取联系人手机号码失败",head:!1}).then(function(e){e.hideUi()
}),new Promise(function(e,t){t(!0)}))}},components:{rmSelect:h.default,rmInput:f.default},watch:{value:function(e){this.recordInput&&this.collect&&(u.store.local.setItem(this.fieldname,e),u.store.local.setItem(this.uuid,this.phoneBook)),this.auth||(this.topError=e?"":"true")
},auth:function(e){var t=u.validator.custom.empty;e||(this.rulesSelect={empty:t},this.rulesInput=this.rulesInputInit)}},compiled:function(){var e=this.$refs.input;if(this.rulesInputInit=o.default.extend({},e.rules),this.recordInput&&this.collect){var t=u.store.local.getItem(this.fieldname);
"string"==typeof t&&t.trim()&&(this.value=t),this.phoneBook=u.store.local.getItem(this.uuid)}}};var v='<section class="fin-{{is}}">\n    <!-- 从通讯录选择手机联系人 -->\n    <rm-select type="selectPhone" v-show="auth" v-ref:select="" :value.sync="value" :text.sync="value" :fieldname="fieldnameSelect" :rules="rulesSelect" :collect="iCollect" :base64="base64" :record-input="false" :fieldset="fieldsetSelect" @select-click="selectClick" :async="checkPhoneSelect">\n    </rm-select>\n\n    <!-- 手动输入手机联系人 -->\n    <rm-input type="selectPhone" v-show="!auth" v-ref:input="" :maxlength="11" :value.sync="value" :fieldname="fieldnameInput" :fieldset="fieldsetInput" :rules="rulesInput" :base64="base64" :top-error="topError" :record-input="false" :async="checkPhoneInput">\n    </rm-input>\n    <input type="hidden" v-model="phoneBook" v-fieldname="uuid">\n</section>';
n&&n.exports&&(n.exports.template=v),t&&t.default&&(t.default.template=v),n.exports=t["default"]});
;define("hiloan:components/fin-rm/select-phone/index",function(e,n,t){"use strict";function o(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var l=e("hiloan:components/fin-rm/select-phone/select-phone.vue"),u=o(l);
n.default=u.default,t.exports=n["default"]});
;define("hiloan:components/fin-rm/contact-info/contact-info.vue",function(e,n,t){"use strict";function o(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var a=e("hiloan:node_modules/underscore/underscore"),i=(o(a),e("hiloan:components/fin-fg/util/base64/index")),l=o(i),r=e("hiloan:components/fin-rm/rm-select/index"),c=o(r),u=e("hiloan:components/fin-rm/rm-input/index"),p=o(u),s=e("hiloan:components/fin-rm/select-phone/index"),m=o(s);
n.default={props:{type:{type:String,"default":"relative"},title:{type:String,"default":""},maxNum:{type:Number,"default":350},recordInput:{type:Boolean,"default":!0},fieldname:{type:Object,"default":function(){return{relation:"",name:"",phone:""}
}},contact:{type:Object,"default":function(){return{relation:"",name:"",phone:""}}},collect:{type:Object,"default":function(){return{relation:!0,name:!0,phone:!0}}},base64:{type:Object,"default":function(){return{relation:!1,name:!1,phone:!1}
}},phoneBook:{type:String,"default":""},showTitle:{type:Boolean,"default":!0},border:{type:String,"default":""},nameChecking:{type:Boolean,"default":!1},phoneChecking:{type:Boolean,"default":!1},useH5:{type:Boolean,"default":!1},manualInput:{type:Boolean,"default":!1}},data:function(){return{is:"contact-info",checkName:{apiName:"checkContact",before:function(e){return this.base64&&(e=l.default.encode(e)),{name:e}
},rule:function(e){return 0===e.data.name},message:{error:"请勿填写申请者本人姓名"}},checkPhone:{apiName:"checkContact",before:function(e){return this.base64&&(e=l.default.encode(e)),{mobile:e}},rule:function(e){return 0===e.data.mobile
},message:{error:"请勿填写申请者本人手机号"}}}},computed:{isH5:function(){return!Agent.OS.wallet},iTitle:function(){if(this.title)return this.title;var e="亲属";return"friend"===this.type&&(e="朋友"),e+="关系联系人"},relationType:function(){var e="kinship";
return"friend"===this.type&&(e="relationship"),e},iFieldname:function(){var e={relation:"",name:"relativeName",phone:"relativePhone"};return"friend"===this.type&&(e={relation:"",name:"friendName",phone:"friendPhone"}),e.relation=this.fieldname.relation||e.relation,e.name=this.fieldname.name||e.name,e.phone=this.fieldname.phone||e.phone,e
},asyncName:function(){return this.nameChecking?this.checkName:{}},asyncPhone:function(){return this.phoneChecking?this.checkPhone:{}}},components:{rmSelect:c.default,rmInput:p.default,selectPhone:m.default}};
var d='<section class="fin-{{is}}">\n    <p class="title" v-if="showTitle">{{ iTitle }}</p>\n    <!-- 选择关系 -->\n    <div class="main {{ border }}">\n        <rm-select :type="relationType" :value.sync="contact.relation" :fieldname="fieldname.relation" :fieldset="fieldname.relation" :collect="collect.relation" :base64="base64.relation" :record-input="recordInput" border="border-bottom">\n        </rm-select>\n\n        <!-- 姓名 -->\n        <rm-input type="name" v-ref:name="" :value.sync="contact.name" :fieldname="iFieldname.name" :fieldset="iFieldname.name" :collect="collect.name" :base64="base64.name" :record-input="recordInput" :async="asyncName" :border="true">\n        </rm-input>\n\n        <!-- 电话 -->\n        <rm-input v-if="useH5 || isH5" type="selectPhoneH5" :value.sync="contact.phone" :fieldname="iFieldname.phone" :fieldset="iFieldname.phone" :record-input="recordInput" :collect="collect.phone" :maxlength="11" :base64="base64.phone" :async="asyncPhone">\n        </rm-input>\n        <select-phone v-else="" v-ref:phone="" :phone-book="phoneBook" :value.sync="contact.phone" :fieldname="iFieldname.phone" :record-input="recordInput" :max-num="maxNum" :collect="collect.phone" :base64="base64.phone" :checking="phoneChecking" :manual-input="manualInput">\n        </select-phone>\n    </div>\n</section>';
t&&t.exports&&(t.exports.template=d),n&&n.default&&(n.default.template=d),t.exports=n["default"]});
;define("hiloan:components/fin-rm/contact-info/index",function(e,n,t){"use strict";function o(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var f=e("hiloan:components/fin-rm/contact-info/contact-info.vue"),i=o(f);
n.default=i.default,t.exports=n["default"]});
;define("hiloan:components/fin-rm/credit-card/credit-card.vue",function(t,e,n){"use strict";function i(t){return t&&t.__esModule?t:{"default":t}}Object.defineProperty(e,"__esModule",{value:!0});var a=t("hiloan:components/fin-rm/rm-input/index"),r=i(a),s=t("hiloan:app/static/config/api"),o=i(s),u=6;
e.default={props:{value:{type:String,"default":""}},data:function(){return{is:"credit-card",errorClass:"",showValue:"",bankcardInfo:{name:"",type:"",url:""},lock:!0,iFormat:function(t){return t.split("").filter(function(t){return!!t.trim()
}).join("")}}},computed:{showCredit:function(){return!this.$refs.creditCard.$children[0].errorClass&&this.bankcardInfo.name&&this.value.length}},methods:{sendRequest:function(){var t=this;this.value.length>=u&&this.lock&&(o.default.remoteValidate(this.value,{"x-silent":!0}).then(function(e){t.bankcardInfo=e.data
}),this.lock=!1)},format:function(t){t=t.split("").filter(function(t){return!!t.trim()}).join(""),this.value=t,this.showValue=t.replace(/(\d{4})/g,"$1 ").trim()}},components:{rmInput:r.default},watch:{value:function(){this.lock&&this.sendRequest(),this.value.length<u&&(this.lock=!0,this.bankcardInfo={name:"",type:"",url:""})
}},events:{input:function(){this.format(this.showValue)}},ready:function(){this.showValue=this.value,this.format(this.value),this.sendRequest()}};var l='<section class="fin-{{is}}">\n    <rm-input v-ref:credit-card="" :value.sync="showValue" :format="iFormat" type="creditCard">\n    </rm-input>\n    <div class="tip" v-show="showCredit">\n        <img :src="bankcardInfo.url">\n        <p>{{bankcardInfo.name}} {{bankcardInfo.type}}</p>\n    </div>\n</section>';
n&&n.exports&&(n.exports.template=l),e&&e.default&&(e.default.template=l),n.exports=e["default"]});
;define("hiloan:components/fin-rm/credit-card/index",function(e,t,n){"use strict";function d(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var r=e("hiloan:components/fin-rm/credit-card/credit-card.vue"),i=d(r);
t.default=i.default,n.exports=t["default"]});
;define("hiloan:components/fin-rm/credit-contract/credit-contract.vue",function(e,t,o){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});
var a=e("hiloan:node_modules/vue/dist/vue.common"),i=n(a),s=e("hiloan:node_modules/vue-touch/vue-touch"),l=n(s),c=e("hiloan:node_modules/vue-validator/dist/vue-validator.common"),d=n(c),u=e("hiloan:node_modules/vue-validator-manage/dist/vue-validator-manage.common"),p=n(u),r=e("hiloan:node_modules/iscroll/build/iscroll"),f=n(r),h=e("hiloan:app/static/config/api"),v=n(h),m=e("hiloan:components/fin-fg/util/index"),y=e("hiloan:components/fin-fg/mixins/index"),g=e("hiloan:components/fin-ui/ui-mask/index"),S=n(g),T=e("hiloan:components/fin-cs/credit-report/index"),w=n(T);
i.default.use(l.default),i.default.use(d.default),i.default.use(p.default),t.default={mixins:[y.inputValidNotice],props:{title:{type:String,"default":"个人征信授权书"},checked:{type:Boolean,"default":!1},border:{type:String,"default":""},showTitle:{type:Boolean,"default":!1},showType:{type:String,"default":"dialog"},show:{type:Boolean,"default":!1},name:{type:String,"default":""},type:{type:String,"default":""},idcard:{type:String,"default":"身份证"},time:{type:String,"default":""},place:{type:String,"default":"北京市海淀区"},spouseName:{type:String,"default":""},spousePrcid:{type:String,"default":""},disabled:{type:Boolean,"default":!0},descText:{type:String,"default":"点击接受个人征信授权书"},protocol:{type:Boolean,"default":!1},ptype:{type:String,"default":""},tplinfo:{type:String,"default":""},pNo:{type:String,"default":""},pCode:{type:String,"default":""},spouse:{type:Boolean,"default":!1},spouseProcess:{type:String,"default":"applySupply"},rules:{type:Object,"default":function(){return{required:{rule:!0,message:""}}
}},value:{type:String,"default":""},fieldname:{type:String,"default":"creditAuthorization"}},data:function(){return{is:"credit-contract",iScroll:null,iHander:-1}},methods:{getEnabledTpl:function(){var e=this,t={pList:[]};
return t.pList[0]={pType:e.ptype},t.pList=JSON.stringify(t.pList),t.getVersion="fbu",v.default.getEnabledTpl(t).then(function(e){return e}).catch(function(e){return console.log(e),!1})},replaceStr:function(e){if(e){var t=this;
t.tplinfo=e.data[0].pInfo,v.default.showContract({},{"x-silent":!0}).then(function(e){var o=e.data;t.name=e.data.authName,t.type=e.data.authType,t.idcard=e.data.authIdNo,t.time=e.data.authDate,t.place=e.data.authAddress,t.spouse?v.default.getSpouseInfo({stage:t.spouseProcess,data:["spouseName","spousePrcid"]},{"x-silent":!0}).then(function(e){o.spouseName=e.data.spouseName,o.spousePrcid=e.data.spousePrcid,t.spouseName=e.data.spouseName,t.spousePrcid=e.data.spousePrcid,Object.keys(o).forEach(function(e){var n=new RegExp("{{"+e+"}}","g"),a=o[e]||"";
t.tplinfo=t.tplinfo.replace(n,a)})}):Object.keys(o).forEach(function(e){var n=new RegExp("{{"+e+"}}","g"),a=o[e]||"";t.tplinfo=t.tplinfo.replace(n,a)})}).catch(function(e){return console.log(e),!1})}},touchmovePrevent:function(e){e.preventDefault()
},close:function(){document.removeEventListener("touchmove",this.touchmovePrevent),this.show=!1},confirm:function(){var e=this;e.disabled||(e.protocol?e.getEnabledTpl().then(e.signProtocol).then(function(t){t||(e.close(),e.checked=!0,e.value="1",e.descText="我已同意接受个人征信授权书",e.showTitle||e.$dispatch("confirm",e),e.focusToBlur=!0)
}).catch(function(){}):(e.close(),e.checked=!0,e.value="1",e.descText="我已同意接受个人征信授权书",e.showTitle||e.$dispatch("confirm",e),e.focusToBlur=!0))},showUi:function(){document.addEventListener("touchmove",this.touchmovePrevent),this.show=!0
},getContract:function b(){var e=this,b=v.default.showContract({},{"x-silent":!0}).then(function(t){return e.name=t.data.authName,e.type=t.data.authType,e.idcard=t.data.authIdNo,e.time=t.data.authDate,e.place=t.data.authAddress,t
});"page"===this.showType&&b.then(function(){return v.default.getSpouseInfo({stage:"applyBasic",data:["spouseName","spousePrcid"]},{"x-silent":!0})}).then(function(t){e.spouseName=t.data.spouseName,e.spousePrcid=t.data.spousePrcid
})},bindScroll:function(){var e=this;e.iScroll.on("scrollEnd",function(){var t=Math.abs(e.iScroll.maxScrollY-e.iScroll.y);10>=t&&(e.disabled=!1)})},preventTouchMove:function(e,t){return e?!1:t.preventDefault()
},signProtocol:function(e){var t=this,o={};if(e){if("page"===t.showType){var n={authName:t.name,authType:t.type,authIdNo:t.idcard,authDate:t.time,authAddress:t.place};t.spouse&&(n.spouseName=t.spouseName,n.spousePrcid=t.spousePrcid),o={pList:[{pCode:e.data[0].pCode,pNo:e.data[0].pNo,pIndex:m.query.parse().search.transactionId||m.query.parse().search.tid,pReplace:JSON.stringify(n)}]},o.pList=JSON.stringify(o.pList)
}else o={pCode:e.data[0].pCode,pNo:e.data[0].pNo,pType:t.ptype};return v.default.signProtocol(o,{"x-silent":!0}).then(function(e){return 0===e.errno?!1:!0}).catch(function(){return!0})}return!0}},watch:{show:function(){var e=this,t=this.$els.content;
this.iScroll?this.iScroll.scrollTo(0,0):this.$nextTick(function(){e.iScroll=new f.default(t),e.bindScroll()}),clearTimeout(this.iHander),this.iHander=setTimeout(function(){e.disabled=!1},12e3)}},ready:function(){this.ptype||this.getContract()
},created:function(){this.protocol&&this.getEnabledTpl().then(this.replaceStr)},components:{uiMask:S.default,creditReport:w.default}};var N='<section class="fin-{{is}}">\n    <validator name="validation">\n        <div v-if="showType==\'page\'">\n            <credit-report :title="title" :name="name" :type="type" :idcard="idcard" :time="time" :place="place" :spouse-name="spouseName" :spouse-prcid="spousePrcid" :protocol="protocol" :ptype="ptype" :tplinfo.sync="tplinfo"></credit-report>\n        </div>\n        <div v-else="">\n            <div class="credit-auth-title {{border}}" v-show.sync="showTitle">\n                <p class="title" v-if="title">{{title}}</p>\n                <div class="cell" id="cell" v-touch:tap="showUi">\n                    <span class="icon" :class="[checked ? \'icon-radio-on on\' : \'icon-radio-off\']"></span>\n                    <span class="desc" :class="[checked ? \'highlight\' : \'\']">\n                        {{descText}}\n                    </span>\n                    <p class="info">授权重庆百度小额贷款有限公司向中国人民银行金融信用信息基础数据库报送、查询本人的信用信息。\n                    </p>\n                </div>\n            </div>\n            <section v-show.sync="show">\n                <ui-mask v-touch:tap="close" :show.sync="show">\n                </ui-mask>\n                <div class="pop" :class="{\'show-select\':show}">\n                    <div class="close">\n                        <i class="icon icon-close" v-touch:tap="close">\n                        </i>\n                    </div>\n                    <h2 class="title" @touchmove="preventTouchMove(false, $event)">\n                        阅读完全部内容，才能下一步操作\n                    </h2>\n                    <div class="content" id="content" v-el:content="">\n                        <slot>\n                            <credit-report :name="name" :type="type" :idcard="idcard" :time="time" :place="place" :protocol="protocol" :ptype="ptype" :tplinfo.sync="tplinfo"></credit-report>\n                        </slot>\n                    </div>\n                    <div class="button" @touchmove="preventTouchMove(false, $event)">\n                        <input class="fin-ui-button full" type="button" :disabled.sync="disabled" value="我已阅读并同意本协议" v-touch:tap="confirm">\n                    </div>\n                </div>\n            </section>\n        </div>\n        <input type="hidden" v-model="value" v-validate:value="rules" v-fieldname="fieldname" @valid="invalidToValid">\n    </validator>\n</section>';
o&&o.exports&&(o.exports.template=N),t&&t.default&&(t.default.template=N),o.exports=t["default"]});
;define("hiloan:components/fin-rm/credit-contract/index",function(e,t,n){"use strict";function c(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var o=e("hiloan:components/fin-rm/credit-contract/credit-contract.vue"),r=c(o);
t.default=r.default,n.exports=t["default"]});
;define("hiloan:components/fin-ui/ui-picker/draggable",function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var n=function(e){e.preventDefault(),t.drag&&t.drag(u?e.changedTouches[0]||e.touches[0]:e)
},d=function c(e){u||(document.removeEventListener("mousemove",n),document.removeEventListener("mouseup",c)),document.onselectstart=null,document.ondragstart=null,o=!1,t.end&&t.end(u?e.changedTouches[0]||e.touches[0]:e)
};e.addEventListener(u?"touchstart":"mousedown",function(e){o||(document.onselectstart=function(){return!1},document.ondragstart=function(){return!1},u||(document.addEventListener("mousemove",n),document.addEventListener("mouseup",d)),o=!0,t.start&&t.start(u?e.changedTouches[0]||e.touches[0]:e))
}),u&&(e.addEventListener("touchmove",n),e.addEventListener("touchend",d),e.addEventListener("touchcancel",d))};var o=!1,u="ontouchstart"in window;n.exports=t["default"]});
;define("hiloan:components/fin-ui/ui-picker/translate",function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=document.documentElement.style,l=void 0,a=!1;window.opera&&"[object Opera]"===Object.prototype.toString.call(opera)?l="presto":"MozAppearance"in r?l="gecko":"WebkitAppearance"in r?l="webkit":"string"==typeof navigator.cpuClass&&(l="trident");
var o={trident:"-ms-",gecko:"-moz-",webkit:"-webkit-",presto:"-o-"}[l],s={trident:"ms",gecko:"Moz",webkit:"Webkit",presto:"O"}[l],i=document.createElement("div"),p=s+"Perspective",u=s+"Transform",c=o+"transform",d=s+"Transition",f=o+"transition",m=s.toLowerCase()+"TransitionEnd";
void 0!==i.style[p]&&(a=!0);var y=function(t){var e={left:0,top:0};if(null===t||null===t.style)return e;var n=t.style[u],r=/translate\(\s*(-?\d+(\.?\d+?)?)px,\s*(-?\d+(\.\d+)?)px\)\s*translateZ\(0px\)/g.exec(n);
return r&&(e.left=+r[1],e.top=+r[3]),e},x=function(t){if(null!==t&&null!==t.style){var e=t.style[u];e&&(e=e.replace(/translate\(\s*(-?\d+(\.?\d+?)?)px,\s*(-?\d+(\.\d+)?)px\)\s*translateZ\(0px\)/g,""),t.style[u]=e)
}},v=function(t,e,n){if((null!==e||null!==n)&&null!==t&&null!==t.style&&(t.style[u]||0!==e||0!==n)){if(null===e||null===n){var r=y(t);null===e&&(e=r.left),null===n&&(n=r.top)}x(t),t.style[u]+=a?" translate("+(e?e+"px":"0px")+","+(n?n+"px":"0px")+") translateZ(0px)":" translate("+(e?e+"px":"0px")+","+(n?n+"px":"0px")+")"
}};e.default={transformProperty:u,transformStyleName:c,transitionProperty:d,transitionStyleName:f,transitionEndProperty:m,getElementTranslate:y,translateElement:v,cancelTranslateElement:x},n.exports=e["default"]
});
;define("hiloan:components/fin-ui/ui-picker/picker-slot.vue",function(e,t,a){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var i=e("hiloan:node_modules/vue/dist/vue.common"),l=n(i),r=e("hiloan:components/fin-ui/ui-picker/draggable"),s=n(r),u=e("hiloan:components/fin-ui/ui-picker/translate"),o=n(u),d=e("hiloan:components/fin-fg/util/index"),c=(n(d),function(e,t){if(e){var a=o.default.transformProperty;
e.style[a]=e.style[a].replace(/rotateX\(.+?deg\)/gi,"")+(" rotateX("+t+"deg)")}}),v=1.333,f={3:-45,5:-20,7:-15};t.default={props:{values:{type:Array,"default":function(){return[]}},value:{},visibleItemCount:{type:Number,"default":5},divider:{type:Boolean,"default":!1},textAlign:{type:String,"default":"center"},flex:{},className:{},content:{}},data:function(){return{dragging:!1,animationFrameId:null}
},computed:{classNames:function(){var e="picker-slot-",t=[],a=this.textAlign||"center";return t.push(e+a),this.divider&&t.push(e+"divider"),this.className&&t.push(this.className),t.join(" ")},contentHeight:function(){return v*this.visibleItemCount
},valueIndex:function(){return this.values.indexOf(this.value)},dragRange:function(){var e=this.values,t=this.visibleItemCount;return[-ITEMHE_IGHT_PX*(e.length-Math.ceil(t/2)),ITEMHE_IGHT_PX*Math.floor(t/2)]
}},methods:{value2Translate:function(e){var t=this.values,a=t.indexOf(e),n=Math.floor(this.visibleItemCount/2);return-1!==a?(a-n)*-ITEMHE_IGHT_PX:void 0},translate2Value:function(e){e=Math.round(e/ITEMHE_IGHT_PX)*ITEMHE_IGHT_PX;
var t=Math.round(-(e-Math.floor(this.visibleItemCount/2)*ITEMHE_IGHT_PX)/ITEMHE_IGHT_PX);return this.values[t]},updateRotate:function(e,t){if(!this.divider){var a=this.dragRange,n=this.$els.wrapper;t||(t=n.querySelectorAll(".picker-item")),void 0===e&&(e=o.default.getElementTranslate(n).top);
var i=(Math.ceil(this.visibleItemCount/2),f[this.visibleItemCount]||-20);[].forEach.call(t,function(t,n){var l=n*ITEMHE_IGHT_PX,r=a[1]-e,s=l-r,u=s/ITEMHE_IGHT_PX,o=i*u;o>180&&(o=180),-180>o&&(o=-180),c(t,o)
})}},planUpdateRotate:function(){{var e=this;this.$els.wrapper}this.animationFrameId=requestAnimationFrame(function(){e.updateRotate()})},initEvents:function(){var e,t,a,n=this,i=this.$els.wrapper,r={};
s.default(i,{start:function(e){n.animationFrameId=null;var t=e.target;t.style.backgroundColor="#ECF0F5",r={range:n.dragRange,start:new Date,startLeft:e.pageX,startTop:e.pageY,startTranslateTop:o.default.getElementTranslate(i).top},a=i.querySelectorAll(".picker-item")
},drag:function(a){n.dragging=!0,r.left=a.pageX,r.top=a.pageY;var l=r.top-r.startTop,s=r.startTranslateTop+l;o.default.translateElement(i,null,s),e=l>0?1:0>l?-1:0,t=s},end:function(t){var a=t.target;a.style.backgroundColor="transparent",n.dragging=!1,r.left=t.pageX,r.top=t.pageY;
var s=r.top-r.startTop;if(0===s){var u=t.target,d=u.innerText,c=n.values.indexOf(d),v=n.values.indexOf(n.value),f=Math.abs(c-v),p=void 0;if(3>=f){var h=o.default.getElementTranslate(n.$els.wrapper).top;
p=h+(v-c)*ITEMHE_IGHT_PX,p=Math.max(Math.min(p,n.dragRange[1]),n.dragRange[0]),o.default.translateElement(n.$els.wrapper,null,p),n.value=n.translate2Value(p)}return void(r={})}var g,m=6,E=o.default.getElementTranslate(i).top,T=new Date-r.start;
300>T&&(g=E+e*m);var I=r.range;l.default.nextTick(function(){var e;e=g?Math.round(g/ITEMHE_IGHT_PX)*ITEMHE_IGHT_PX:Math.round(E/ITEMHE_IGHT_PX)*ITEMHE_IGHT_PX,e=Math.max(Math.min(e,I[1]),I[0]),o.default.translateElement(i,null,e),n.value=n.translate2Value(e)
}),r={}}})},doOnValueChange:function(){var e=this.value,t=this.$els.wrapper;o.default.translateElement(t,null,this.value2Translate(e))},doOnValuesChange:function(){var e=this.$el,t=e.querySelectorAll(".picker-item");
[].forEach.call(t,function(e,t){o.default.translateElement(e,null,ITEMHE_IGHT_PX*t)})}},ready:function(){this.ready=!0,window.ITEMHE_IGHT_PX=document.getElementsByClassName("picker-item")[0].clientHeight,this.divider||(this.initEvents(),this.doOnValueChange())
},watch:{values:function(e){-1===this.valueIndex&&(this.value=(e||[])[0])},value:function(){this.doOnValueChange(),this.$dispatch("slotValueChange",this)}}};var p='<div class="picker-slot {{classNames}}" :style="{ flex: flex }">\n  <div class="picker-slot-wrapper" :class="{ dragging: dragging }" :style="{ height: contentHeight + \'rem\' }" v-if="!divider" v-el:wrapper="">\n      <div class="picker-item" :class="{ \'picker-selected\': itemValue === value }" v-el:="" pickeritem="" v-for="itemValue in values">\n      {{ itemValue }}\n      </div>\n  </div>\n  <div v-if="divider">{{ content }}</div>\n</div>';
a&&a.exports&&(a.exports.template=p),t&&t.default&&(t.default.template=p),a.exports=t["default"]});
;define("hiloan:components/fin-ui/ui-picker/ui-picker.vue",function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={props:{slots:{type:Array},visibleItemCount:{type:Number,"default":5}},beforeCompile:function(){var e=this.slots||[];
this.values=[];var t=this.values,n=0;e.forEach(function(e){e.divider||(e.valueIndex=n++,t[e.valueIndex]=(e.values||[])[e.defaultIndex||0])})},data:function(){return{is:"ui-picker"}},methods:{getSlot:function(e){var t=this.slots||[],n=0,i=void 0,s=this.$children;
return t.forEach(function(t,l){t.divider||(e===n&&(i=s[l]),n++)}),i},getSlotValue:function(e){var t=this.getSlot(e);return t?t.value:null},setSlotValue:function(e,t){var n=this.getSlot(e);n&&(n.value=t)
},getSlotValues:function(e){var t=this.getSlot(e);return t?t.values:null},setSlotValues:function(e,t){var n=this.getSlot(e);n&&(n.values=t)}},events:{slotValueChange:function(){this.$emit("change",this,this.values)
}},computed:{values:function s(){var e=this.slots||[],s=[];return e.forEach(function(e){e.divider||s.push(e.value)}),s},slotCount:function(){var e=this.slots||[],t=0;return e.forEach(function(e){e.divider||t++
}),t}},components:{PickerSlot:e("hiloan:components/fin-ui/ui-picker/picker-slot.vue")}};var i='<div class="fin-ui-picker">\n    <div class="picker-items">\n    <div class="options-bg">\n        <ul>\n            <li class="underline" v-for="n in visibleItemCount-1">\n                <span></span>\n                <span></span>\n                <span></span>\n            </li>\n        </ul>\n    </div>\n        <picker-slot v-for="slot in slots" :values="slot.values || []" :text-align="slot.textAlign || \'center\'" :visible-item-count="visibleItemCount" :class-name="slot.className" :flex="slot.flex" :value.sync="values[slot.valueIndex]" :divider="slot.divider" :content="slot.content">\n        </picker-slot>\n    </div>\n</div>';
n&&n.exports&&(n.exports.template=i),t&&t.default&&(t.default.template=i),n.exports=t["default"]});
;define("hiloan:components/fin-ui/ui-picker/index",function(e,i,u){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(i,"__esModule",{value:!0});var t=e("hiloan:components/fin-ui/ui-picker/ui-picker.vue"),o=n(t);
i.default=o.default,u.exports=i["default"]});
;define("hiloan:components/fin-ui/ui-datetime-picker/ui-datetime-picker.vue",function(t,e,i){"use strict";function s(t){return t&&t.__esModule?t:{"default":t}}Object.defineProperty(e,"__esModule",{value:!0});
var a=t("hiloan:components/fin-ui/ui-picker/index"),l=s(a),r=t("hiloan:components/fin-ui/ui-mask/index"),n=s(r),h={Y:"year",M:"month",D:"date",H:"hour",m:"minute"};e.default={props:{visible:{type:Boolean,"default":!1},cancelText:{type:String,"default":"取消"},confirmText:{type:String,"default":"确定"},title:{type:String,required:!1,"default":"选择日期"},type:{type:String,"default":"date"},startDate:{type:Date},endDate:{type:Date},startHour:{type:Number,"default":0},endHour:{type:Number,"default":23},yearFormat:{type:String,"default":"{value} 年"},monthFormat:{type:String,"default":"{value} 月"},dateFormat:{type:String,"default":"{value} 日"},hourFormat:{type:String,"default":"{value} 时"},minuteFormat:{type:String,"default":"{value} 分"},value:null},data:function(){return{is:"ui-datetime-picker",originVal:this.value,showItem:!1,startYear:null,endYear:null,startMonth:1,endMonth:12,startDay:1,endDay:31,selfTriggered:!1,isSlotChange:!1,dateSlots:[],shortMonthDates:[],longMonthDates:[],febDates:[],leapFebDates:[]}
},components:{uiPicker:l.default,uiMask:n.default},methods:{show:function(){this.showItem=!0},isLeapYear:function(t){return t%400===0||t%100!==0&&t%4===0},isShortMonth:function(t){return[4,6,9,11].indexOf(t)>-1
},getTrueValue:function(t){for(;isNaN(parseInt(t,10));)t=t.slice(1);return parseInt(t,10)},getValue:function(t){var e=this,i=void 0;if("time"===this.type)i=t.map(function(t){return("0"+e.getTrueValue(t)).slice(-2)
}).join(":");else{var s=this.getTrueValue(t[0]),a=this.getTrueValue(t[1]),l=this.getTrueValue(t[2]),r=this.typeStr.indexOf("H")>-1?this.getTrueValue(t[this.typeStr.indexOf("H")]):0,n=this.typeStr.indexOf("m")>-1?this.getTrueValue(t[this.typeStr.indexOf("m")]):0;
i=new Date(s,a-1,l,r,n)}return i},onChange:function(t,e){if(this.selfTriggered)return void(this.selfTriggered=!1);this.isSlotChange=!0;var i=this.getValue(e);if(this.type.indexOf("date")>-1)if(i.getTime()<this.startDate.getTime()&&(this.value=this.startDate,i=this.startDate,this.selfTriggered=!0,this.setSlots()),i.getTime()>this.endDate.getTime()&&(this.value=this.endDate,i=this.endDate,this.selfTriggered=!0,this.setSlots()),this.isShortMonth(this.getTrueValue(e[1]))){if(-1===this.shortMonthDates.indexOf(e[2]))return void t.setSlotValue(2,this.dateSlots[2].values[0]);
this.dateSlots[2].values=this.shortMonthDates.map(function(t){return t})}else if(2===this.getTrueValue(e[1]))if(this.isLeapYear(this.getTrueValue(e[0]))){if(-1===this.leapFebDates.indexOf(e[2]))return void t.setSlotValue(2,this.dateSlots[2].values[0]);
this.dateSlots[2].values=this.leapFebDates.map(function(t){return t})}else{if(-1===this.febDates.indexOf(e[2]))return void t.setSlotValue(2,this.dateSlots[2].values[0]);this.dateSlots[2].values=this.febDates.map(function(t){return t
})}else this.dateSlots[2].values=this.longMonthDates.map(function(t){return t});this.value=i,this.type.indexOf("date")>-1&&this.rimDetect(this.dateSlots[2].values),this.$emit("change",this.value)},rimDetect:function(t){this.dateSlots[1].values=this.fillValues("M",1,12),this.value.getFullYear()===this.startDate.getFullYear()&&(this.trimSlots("start",this.startDate,1),this.value.getMonth()===this.startDate.getMonth()?this.trimSlots("start",this.startDate,2):this.dateSlots[2].values=t.map(function(t){return t
})),this.value.getFullYear()===this.endDate.getFullYear()&&(this.trimSlots("end",this.endDate,1),this.value.getMonth()===this.endDate.getMonth()?this.trimSlots("end",this.endDate,2):this.dateSlots[2].values=t.map(function(t){return t
}))},trimSlots:function(t,e,i){var s=[e.getFullYear(),e.getMonth()+1,e.getDate(),e.getHours(),e.getMinutes()];if("start"===t)for(;this.getTrueValue(this.dateSlots[i].values[0])<s[i];)this.dateSlots[i].values.shift();
if("end"===t)for(var a=this.dateSlots[i].values.length-1;this.getTrueValue(this.dateSlots[i].values[a])>s[i];)this.dateSlots[i].values.pop(),a--},fillValues:function(t,e,i){for(var s=[],a=e;i>=a;a++)s.push(10>a?this[h[t]+"Format"].replace("{value}",("0"+a).slice(-2)):this[h[t]+"Format"].replace("{value}",a));
return s},pushSlots:function(t,e,i){this.dateSlots.push({flex:1,values:this.fillValues(t,e,i)})},generateSlots:function(){var t=this,e={Y:[this.startYear,this.endYear],M:[this.startMonth,this.endMonth],D:[this.startDay,this.endDay],H:[this.startHour,this.endHour],m:[0,59]},i=this.typeStr.split("");
i.forEach(function(i){e[i]&&t.pushSlots.apply(null,[i].concat(e[i]))}),"Hm"===this.typeStr&&this.dateSlots.splice(1,0,{divider:!0,content:":"})},setSlots:function(){var t=this.$refs.uiPicker.setSlotValue;
if("time"===this.type&&"string"==typeof this.value){var e=this.value.split(":");t(0,this.hourFormat.replace("{value}",e[0])),t(1,this.minuteFormat.replace("{value}",e[1]))}if("time"!==this.type&&"[object Date]"==={}.toString.call(this.value)){var i=this.value.getFullYear(),s=this.value.getMonth()+1,a=this.value.getDate();
if(t(0,this.yearFormat.replace("{value}",i)),t(1,this.monthFormat.replace("{value}",("0"+s).slice(-2))),t(2,this.dateFormat.replace("{value}",("0"+a).slice(-2))),"datetime"===this.type){var l=this.value.getHours(),r=this.value.getMinutes();
t(3,this.hourFormat.replace("{value}",("0"+l).slice(-2))),t(4,this.minuteFormat.replace("{value}",("0"+r).slice(-2)))}}},confirm:function(){this.visible=!1,this.showItem=!1,this.originVal=this.value;var t=this.$refs.uiPicker.values.join("/ ").replace(/年|月|日/g,""),e=this.value.getTime(),i={text:t,value:e};
this.$dispatch("selected",this,i)},cancel:function(){this.showItem=!1,this.value=this.originVal}},computed:{typeStr:function(){return"time"===this.type?"Hm":"date"===this.type?"YMD":"YMDHm"}},watch:{value:function(){var t=this;
this.$nextTick(function(){t.$refs.uiPicker.$children.forEach(function(t){t.doOnValueChange()})}),this.isSlotChange?this.isSlotChange=!1:this.setSlots()}},created:function(){var t=new Date;this.startDate=this.startDate||new Date(t.getFullYear()-10,0,1),this.endDate=this.endDate||new Date(t.getFullYear()+10,11,31),this.startYear=this.startDate.getFullYear(),this.endYear=this.endDate.getFullYear(),this.startYear===this.endYear&&(this.startMonth=this.startDate.getMonth()+1,this.endMonth=this.endDate.getMonth()+1,this.startMonth===this.endMonth&&(this.startDay=this.startDate.getDate(),this.endDay=this.endDate.getDate()));
for(var e=1;28>=e;e++)this.febDates.push(this.dateFormat.replace("{value}",("0"+e).slice(-2)));this.leapFebDates=this.febDates.concat(this.dateFormat.replace("{value}","29")),this.shortMonthDates=this.leapFebDates.concat(this.dateFormat.replace("{value}","30")),this.longMonthDates=this.shortMonthDates.concat(this.dateFormat.replace("{value}","31")),this.generateSlots()
},ready:function(){this.setSlots(),this.type.indexOf("date")>-1&&!this.value&&(this.value=this.startDate,this.trimSlots("start",this.value,1),this.trimSlots("start",this.value,2))}};var u='<div class="ui-datetime-picker">\n    <ui-mask :show="showItem" :opacity="0.5" @click="cancel">\n    </ui-mask>\n    <div class="w-select j-w-select" :class="{\'show-select\':showItem}">\n        <div class="title-box">\n            <span class="ui-datetime-action ui-datetime-cancel" @click="cancel">{{cancelText}}</span>\n            {{title}}\n            <span class="ui-datetime-action ui-datetime-confirm" @click="confirm">{{confirmText}}</span>\n        </div>\n        <ui-picker :slots="dateSlots" @change="onChange" :visible-item-count="7" class="ui-datetime-picker" v-ref:ui-picker="">\n        </ui-picker>\n    </div>\n</div>';
i&&i.exports&&(i.exports.template=u),e&&e.default&&(e.default.template=u),i.exports=e["default"]});
;define("hiloan:components/fin-ui/ui-datetime-picker/index",function(e,i,t){"use strict";function u(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(i,"__esModule",{value:!0});var n=e("hiloan:components/fin-ui/ui-datetime-picker/ui-datetime-picker.vue"),d=u(n);
i.default=d.default,t.exports=i["default"]});
;define("hiloan:components/fin-ui/ui-date-input/ui-date-input.vue",function(e,t,i){"use strict";function a(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var n=e("hiloan:node_modules/vue/dist/vue.common"),l=a(n),u=e("hiloan:node_modules/vue-validator/dist/vue-validator.common"),d=a(u),s=e("hiloan:components/fin-ui/ui-select-input/index"),r=a(s),o=e("hiloan:components/fin-ui/ui-datetime-picker/index"),c=a(o);
l.default.use(d.default),t.default={props:{showPicker:{type:Boolean,"default":!1},size:{type:String,"default":""},title:{type:String,"default":""},placeholder:{type:String,"default":""},value:{type:String,"default":""},text:{type:String,"default":""},rules:{type:Object,"default":function(){return{}
}},fieldname:{type:String,"default":""},fieldset:{type:String,"default":""},border:{type:String,"default":""},tip:{type:String,"default":""},enable:{type:Boolean,"default":!0},dateType:{type:String,"default":"date"},startDate:Date,endDate:Date,dateValue:Date,defaultDate:{type:Date,"default":null}},data:function(){return{is:"ui-date-input"}
},components:{uiSelectInput:r.default,uiDatetimePicker:c.default},methods:{selectClick:function(){this.enable&&(this.showPicker=!0,this.$nextTick(function(){var e=this;setTimeout(function(){e.$refs.uiDatetimePicker.show()
},0)}))},selected:function(e,t){var i=this;return this.text=t.text,this.value=t.value+"",setTimeout(function(){i.showPicker=!1},300),!0},formatDate:function(e){if(e){e=parseInt(e,10);var t=new Date(e),i=t.getFullYear(),a=t.getMonth()+1+"",n=t.getDate()+"",l=a.length>1?a:"0"+a,u=n.length>1?n:"0"+n,d=i+" / "+l+" / "+u;
return d}return""}},ready:function(){this.text=this.formatDate(this.value),this.dateValue=this.value?new Date(parseInt(this.value,10)):this.defaultDate}};var f='<div class="fin-ui-date-input">\n    <ui-select-input :fieldname="fieldname" v-fieldset="fieldset" :border="border" :icon="icon" :title="title" :placeholder="placeholder" :value.sync="value" :rules="rules" :text.sync="text" :tip="tip" :size="size" :enable="enable" :class="{disable:!enable}" @select-click="selectClick">\n    </ui-select-input>\n    <ui-datetime-picker v-ref:ui-datetime-picker="" :type="dateType" :start-date="startDate" :end-date="endDate" :value.sync="dateValue" v-if="showPicker" @selected="selected">\n    </ui-datetime-picker>\n</div>';
i&&i.exports&&(i.exports.template=f),t&&t.default&&(t.default.template=f),i.exports=t["default"]});
;define("hiloan:components/fin-ui/ui-date-input/index",function(e,t,u){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var i=e("hiloan:components/fin-ui/ui-date-input/ui-date-input.vue"),d=n(i);
t.default=d.default,u.exports=t["default"]});
;define("hiloan:components/fin-rm/date-input-group/date-input-group.vue",function(e,t,a){"use strict";function d(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});
var n=e("hiloan:node_modules/vue/dist/vue.common"),l=d(n),u=e("hiloan:node_modules/vue-validator/dist/vue-validator.common"),i=d(u),r=e("hiloan:components/fin-ui/ui-date-input/index"),s=d(r);l.default.use(i.default),t.default={props:{grouptitle:{type:String,"default":""},grouptip:{type:String,"default":""},size:{type:String,"default":""},titles:{type:Object,"default":function(){return{title1:"起始时间",title2:"结束时间"}
}},placeholders:{type:Object,"default":function(){return{placeholder1:"选择日期",placeholder2:"选择日期"}}},value1:{type:String,"default":""},value2:{type:String,"default":""},fields:{type:Object,"default":function(){return{startdate:"startDate",enddate:"endDate"}
}},rules1:{type:Object,"default":function(){return{required:{rule:!0,message:"请选择起始时间"}}}},rules2:{type:Object,"default":function(){return{required:{rule:!0,message:"请选择结束时间"}}}},dateType:{type:String,"default":"date"},startDate1:Date,startDate2:{type:Date},endDate1:Date,endDate2:Date,dateValue1:{type:Date,"default":function(){return new Date
}},dateValue2:{type:Date,"default":function(){return new Date}}},data:function(){return{is:"date-input-group",defaultDate:new Date}},components:{uiDateInput:s.default},methods:{firstSelected:function(e,t){var a=this.$refs.secondInput;
if(a.enable=!0,this.getSecondDate(t.value),this.dateValue2){var d=this.dateValue1.getTime(),n=this.dateValue2.getTime();d>=n&&(this.dateValue2=null,a.$children[0].value="",a.$children[0].text="")}},getSecondDate:function(e){if(e){var t=parseInt(e,10),a=new Date(t).getFullYear(),d=a+2,n=new Date(t).getMonth()+1;
n>12&&(n-=12,d=d++);var l=new Date(d,n,1);this.endDate2=new Date(l.getTime()-864e5),this.startDate2=new Date(parseInt(t,10)+864e5)}}},ready:function(){var e=this.$refs.firstInput,t=this.$refs.secondInput,a=this.$refs.firstInput.$children[0];
if(e.text||a.text){var d=e.value||a.value;t.enable=!0,this.getSecondDate(d),this.dateValue1=new Date(parseInt(d,10))}var n=t.$children[0];if(n.text||t.text){var l=t.value||n.value;this.dateValue2=new Date(parseInt(l,10))
}}};var o='<div class="fin-{{is}}">\n    <div class="title">{{grouptitle}}</div>\n    <ui-date-input :size="size" :title="titles.title1" :placeholder="placeholders.placeholder1" :value.sync="value1" :date-type="dateType" :default-date="defaultDate" :date-value.sync="dateValue1" :start-date.sync="startDate1" :end-date.sync="endDate1" border="border-bottom" :fieldname="fields.startdate" :fieldset="fields.startdate" :rules="rules1" @selected="firstSelected" v-ref:first-input=""></ui-date-input>\n    <ui-date-input :size="size" :title="titles.title2" :placeholder="placeholders.placeholder2" :value.sync="value2" :date-type="dateType" :default-date="defaultDate" :date-value.sync="dateValue2" :start-date.sync="startDate2" :end-date.sync="endDate2" border="border-bottom" :fieldname="fields.enddate" :fieldset="fields.enddate" :rules="rules2" :enable="false" v-ref:second-input=""></ui-date-input>\n    <div class="group-tip" v-if="grouptip">{{grouptip}}</div>\n</div>';
a&&a.exports&&(a.exports.template=o),t&&t.default&&(t.default.template=o),a.exports=t["default"]});
;define("hiloan:components/fin-rm/date-input-group/index",function(e,t,n){"use strict";function u(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var o=e("hiloan:components/fin-rm/date-input-group/date-input-group.vue"),i=u(o);
t.default=i.default,n.exports=t["default"]});
;define("hiloan:components/fin-rm/group-both/index",function(e,n,u){"use strict";function t(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0}),n.default=function(e,n){var u=[];
e.forEach(function(e){var n=c({},e.rules);delete n.required,r.default.isEmpty(n)&&(n={empty:f}),u.push(n)});var t=function(){return e.some(function(e){return e.value?!0:!1})},o=function(){e.forEach(function(e,u){e.rules=c({},e.rules,{required:{rule:!0,message:n[u]}})
})},i=function(){e.forEach(function(e,n){e.rules=u[n]})},a=function(){t()?o():i()};a(),e.forEach(function(e){e.$watch("value",function(){a()})})};var o=e("hiloan:node_modules/underscore/underscore"),r=t(o),i=e("hiloan:components/fin-fg/util/index"),c=Object.assign?Object.assign:r.default.extend,f=i.validator.custom.empty;
u.exports=n["default"]});
;define("hiloan:components/fin-rm/group-required/show",function(t,r,e){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i="init",o="error",n="",s="true";r.default={props:{topError:{type:String,"default":n}},computed:{tipState:function(){var t=i;
return this.$validation.invalid&&"true"===this.topError?t=o:"false"===this.topError&&(t=i),t},showTip:function(){var t=!1;return(this.tipState===i&&this.tip||this.tipState===o)&&(t=!0),t},tipContent:function(){var t=this.tip;
if(this.tipState===o){var r=this.$validation.value.errors;r&&r.length>0&&(t=r[0].message)}return t}},events:{"show-error-message":function(){this.topError=s}}},e.exports=r["default"]});
;define("hiloan:components/fin-rm/group-required/ui-component.vue",function(e,r,t){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(r,"__esModule",{value:!0});var n=e("hiloan:components/fin-rm/group-required/show"),s=i(n);
r.default={mixins:[s.default],props:{value:{type:String,"default":""},errorMessage:{type:String,"default":"请填写一项"}},data:function(){return{is:"group-required",rules:{required:{rule:!0,message:"请选择一项"}}}
},computed:{hasErrorClass:function(){return"error"===this.tipState?"error":""}},created:function(){this.rules={required:{rule:!0,message:this.errorMessage}}}};var a='<div class="fin-{{is}}">\n    <validator name="validation">\n        <input type="hidden" v-model="value" v-validate:value="rules">\n        <section class="tip {{hasErrorClass}}" v-if="showTip">\n            <i class="icon icon-info"></i>\n            <p v-text="tipContent"></p>\n        </section>\n    </validator>\n</div>';
t&&t.exports&&(t.exports.template=a),r&&r.default&&(r.default.template=a),t.exports=r["default"]});
;define("hiloan:components/fin-rm/group-required/group-required.vue",function(e,t,o){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var u=e("hiloan:node_modules/vue/dist/vue.common"),i=n(u),r=e("hiloan:node_modules/vue-validator/dist/vue-validator.common"),a=n(r),d=e("hiloan:node_modules/vue-validator-manage/dist/vue-validator-manage.common"),l=n(d),s=e("hiloan:components/fin-rm/group-required/ui-component.vue"),f=n(s);
i.default.use(a.default),i.default.use(l.default),t.default={props:{errorMessage:{type:String,"default":"请至少填写一项"},topError:{type:String,"default":"true"},fieldnames:{type:Array,"default":function(){return[]
}}},data:function(){return{is:"group-required",value:"",fieldset:"required"}},components:{uiComponent:f.default},created:function(){this.fieldset=this.fieldnames.join("_")},compiled:function(){function e(){return Object.keys(n).some(function(e){return n[e]
})}var t=this,o=this.$root.fieldsData,n={},u=this;this.fieldnames.forEach(function(i){n[i]=o[i]?!0:!1,t.$root.$watch("fieldsData",function(t){u.fieldnames.forEach(function(e){n[e]=t[e]?!0:!1}),u.value=e()?"valid":""
},{deep:!0})}),u.value=e()?"valid":""}};var m='<div>\n    <ui-component :value="value" v-fieldset="fieldset" :error-message="errorMessage">\n    </ui-component>\n</div>';o&&o.exports&&(o.exports.template=m),t&&t.default&&(t.default.template=m),o.exports=t["default"]
});
;define("hiloan:components/fin-rm/group-required/index",function(e,r,u){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(r,"__esModule",{value:!0});var o=e("hiloan:components/fin-rm/group-required/group-required.vue"),t=n(o);
r.default=t.default,u.exports=r["default"]});
;define("hiloan:components/fin-rm/guarantor/guarantor.vue",function(e,t,a){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}function n(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e
}Object.defineProperty(t,"__esModule",{value:!0});var r=e("hiloan:components/fin-fg/util/index"),d=e("hiloan:components/fin-ui/ui-dialog/index"),o=i(d),l=e("hiloan:components/fin-rm/rm-input/index"),s=i(l),u=r.validator.custom,f=u.phone,c=u.idcard,h=u.name;
t.default={props:{title:{type:String,"default":"您的持股比例未达50%。需要增加担保人，以保证您及担保人的股份占比达50%以上方可继续申请，请您按提示补充，谢谢。"},fieldname:{type:String,"default":"warrant"},value:{type:String,"default":""},itemFieldnames:{type:Array,"default":function(){return["warrantName","warrantPhonenumber","warrantPrcid"]
}},rules:{type:Object,"default":function(){return{name:{required:{rule:!0,message:"请填写姓名"},name:h},phone:{required:{rule:!0,message:"请填写手机号码"},phone:f},idcard:{required:{rule:!0,message:"请填写身份证号"},idcard:c}}
}}},data:function(){return{showDialog:!1,warrant:[],count:0,deleteId:0,nextStep:{}}},methods:{addDom:function(){this.count++,this.warrant.push({id:this.count,vif:!0,val:{}})},removeInfo:function(e){this.showDialog=!0,this.deleteId=e
},remove:function(){this.warrant[this.deleteId-1].vif=!1,this.showDialog=!1},fieldval:function(){for(var e=this.fieldname,t=this.warrant.filter(function(e){return e.vif}),a=t.length,i=[],r=0;a>r;r++){var d,o=t[r].id,l="name"+o,s="phone"+o,u="idcard"+o;
i.push((d={},n(d,this.itemFieldnames[0],this.$root.fieldsData[l]),n(d,this.itemFieldnames[1],this.$root.fieldsData[s]),n(d,this.itemFieldnames[2],this.$root.fieldsData[u]),d)),delete this.$root._fieldsData[l],delete this.$root._fieldsData[s],delete this.$root._fieldsData[u]
}i.length>0?(this.nextStep.lightUp="",this.$root._fieldsData[e]=JSON.stringify(i)):this.nextStep.lightUp="false"},checkRepeat:function(){function e(e){var i=[];a.forEach(function(a){-1!==a.indexOf(e)&&i.push(t[a])
});var n=Array.from(new Set(i));return i.length!==n.length}var t=this.$root.fieldsData,a=Object.keys(t);return{idcard:e("idcard"),name:e("name"),phone:e("phone")}}},components:{rmInput:s.default,uiDialog:o.default},created:function(){var e=this,t=this.value;
if(t){var a=JSON.parse(t);a.forEach(function(t,i){e.count++,e.warrant.push({id:e.count,vif:!0,val:a[i]})})}},ready:function(){var e=this;setTimeout(function(){e.$root.$children.forEach(function(t){"next-step"===t.is&&(e.nextStep=t)
}),0===e.warrant.length&&(e.nextStep.lightUp="false")}),this.fieldval(),this.$root.$watch("fieldsData",function(){e.fieldval()},{deep:!0})}};var p='<section class="fin-machain-guarantor">\n        <div class="instruction">\n            {{title}}\n        </div>\n        <div class="guarantorInfo" v-for="data in warrant">\n            <div class="top" v-if="data.vif">\n                <span class="title">担保人基本信息</span>\n                <span class="operation" @click="removeInfo(data.id)">删除</span>\n            </div>\n            <div v-if="data.vif">\n                <rm-input :record-input="false" :repair-validation="true" :fieldname="\'name\'+data.id" :fieldset="\'name\'+data.id" :rules="rules.name" :value="data.val[itemFieldnames[0]]" title="真实姓名" placeholder="请输入姓名" border="offset">\n                 </rm-input>\n                <rm-input :record-input="false" :repair-validation="true" :fieldname="\'phone\'+data.id" :fieldset="\'phone\'+data.id" :rules="rules.phone" :value="data.val[itemFieldnames[1]]" title="手机号码" placeholder="请输入手机号码" border="offset">\n                </rm-input>\n                <rm-input :record-input="false" :repair-validation="true" :fieldname="\'idcard\'+data.id" :fieldset="\'idcard\'+data.id" :rules="rules.idcard" :value="data.val[itemFieldnames[2]]" title="身份证号" placeholder="请输入身份证号码">\n                </rm-input>\n            </div>\n        </div>\n\n        <div class="add" @click="addDom">\n            <div>添加</div>\n        </div>\n        <div class="dialog">\n            <ui-dialog :show.sync="showDialog" :head="false" ok="删除" cancel="取消" type="confirm" :content="\'确认删除该担保人信息\'" @ok="remove">\n            </ui-dialog>\n        </div>\n</section>';
a&&a.exports&&(a.exports.template=p),t&&t.default&&(t.default.template=p),a.exports=t["default"]});
;define("hiloan:components/fin-rm/guarantor/index",function(e,n,t){"use strict";function o(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var r=e("hiloan:components/fin-rm/guarantor/guarantor.vue"),u=o(r);
n.default=u.default,t.exports=n["default"]});
;define("hiloan:components/fin-rm/take-photo/take-photo.vue",function(e,t,a){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var o=e("hiloan:node_modules/underscore/underscore"),s=i(o),n=e("hiloan:node_modules/vue/dist/vue.common"),c=i(n),h=e("hiloan:node_modules/vue-validator/dist/vue-validator.common"),r=i(h),d=e("hiloan:node_modules/vue-touch/vue-touch"),l=i(d),u=e("hiloan:components/fin-fg/util/index"),m=e("hiloan:node_modules/query-string/index"),p=i(m),f=e("hiloan:components/fin-ui/ui-mask/index"),g=i(f),k=e("hiloan:app/static/config/api"),v=i(k);
c.default.use(l.default),c.default.use(r.default);var b=u.query.getQuery,w=u.validator.custom.boskey;t.default={props:{title:{type:String,"default":""},subtitle:{type:String,"default":""},titleShow:{type:Boolean,"default":!0},subtitleShow:{type:Boolean,"default":!0},type:{type:String,"default":""},fieldname:{type:String,"default":""},stage:{type:String,"default":""},uuid:{type:String,"default":""},recordPhoto:{type:Boolean,"default":!1},desc:{type:String,"default":""},tip:{type:String,"default":""},tipShow:{type:Boolean,"default":!1},demo:{type:Boolean,"default":!0},demoShow:{type:Boolean,"default":!1},demoInfo:{type:Object,"default":function(){return{}
}}},data:function(){return{is:"rm-take-photo",file:"",h5Mode:!Agent.OS.wallet,viewMode:!1,viewHash:location.hash,rules:{required:{rule:!0,message:""},boskey:w},demoStatus:!1}},computed:{iFieldname:function(){if(this.fieldname){var e=["idcardPhoto","idcardPhotoBack","idcardPhotowith","bankcard","drivingLicensePhoto1","drivingLicensePhoto2","estateLicensePhoto1","estateLicensePhoto2","estateLicensePhoto3","houseContractPhoto1","houseContractPhoto2","houseContractPhoto3"];
return this.type&&-1===e.indexOf(this.fieldname)?this.fieldname:(this.type=this.fieldname,this.fieldname)}var t="";switch(this.type){case"idcardFront":t="idcardPhoto";break;case"idcardBack":t="idcardPhotoBack";
break;case"handheldIdcard":t="idcardPhotowith";break;case"bankcard":t="bankcard";break;case"licenseFront":t="drivingLicensePhoto1";break;case"licenseBack":t="drivingLicensePhoto2";break;case"estateLicensePhoto1":t="estateLicensePhoto1";
break;case"estateLicensePhoto2":t="estateLicensePhoto2";break;case"estateLicensePhoto3":t="estateLicensePhoto3";break;case"houseContractPhoto1":t="houseContractPhoto1";break;case"houseContractPhoto2":t="houseContractPhoto2";
break;case"houseContractPhoto3":t="houseContractPhoto3"}return t},iTitle:function(){if(this.title)return this.title;var e="";switch(this.type){case"idcardFront":e="上传身份证正面照";break;case"idcardBack":e="上传身份证反面照";
break;case"handheldIdcard":e="上传手持身份证正面照";break;case"bankcard":e="上传银行卡正面照";break;case"licenseFront":e="上传行驶证正面照";break;case"licenseBack":e="上传行驶证反面照";break;case"estateLicensePhoto1":e="上传产权人信息页";break;
case"estateLicensePhoto2":e="上传房屋测绘页";break;case"estateLicensePhoto3":e="上传其他页";break;case"houseContractPhoto1":e="上传购房合同首页";break;case"houseContractPhoto2":e="上传购房合同签字页";break;case"houseContractPhoto3":e="上传其他页"
}return e},iDemoInfo:function(){var e={};switch(this.type){case"idcardFront":e={title:"身份证正面照",subtitle:"请拍摄完整页面并确保清晰",image:"https://umoney.bdstatic.com/webroot/static/hiloan/pkg/image/_bc8da4f.jpg"};
break;case"idcardBack":e={title:"身份证反面照",subtitle:"请拍摄完整页面并确保清晰",image:"https://umoney.bdstatic.com/webroot/static/hiloan/pkg/image/_1f5c26a.jpg"};break;case"handheldIdcard":e={title:"手持身份证正面照",subtitle:"人脸清晰完整，身份证正面完整显示",image:"https://umoney.bdstatic.com/webroot/static/hiloan/pkg/image/_d6f09f6.jpg"};
break;case"bankcard":e={title:"银行卡正面照",subtitle:"银行卡正面信息清晰完整可见",image:"https://umoney.bdstatic.com/webroot/static/hiloan/pkg/image/_003d8b1.jpg"};break;case"marriage":e={title:"结婚证照片(整页)",subtitle:"请拍摄完整页面并确保清晰",image:"https://umoney.bdstatic.com/webroot/static/hiloan/pkg/image/_7debd47.jpg"};
break;case"licenseFront":e={title:"行驶证正面照",subtitle:"",image:"https://umoney.bdstatic.com/webroot/static/hiloan/pkg/image/_9cb6882.jpg"};break;case"licenseBack":e={title:"行驶证反面照",subtitle:"",image:"https://umoney.bdstatic.com/webroot/static/hiloan/pkg/image/_81288b3.jpg"};
break;case"estateLicensePhoto1":e={title:"产权人信息页",subtitle:"若为复印件，必须加盖开具机构<i>红章</i>",image:"https://umoney.bdstatic.com/webroot/static/hiloan/pkg/image/_a6ec6ff.jpg"};break;case"estateLicensePhoto2":e={title:"房屋测绘页",subtitle:"照片需包含<i>房屋整体尺寸结构</i>和<i>测绘单位名称</i>",image:"https://umoney.bdstatic.com/webroot/static/hiloan/pkg/image/_2b1e9d1.jpg"};
break;case"estateLicensePhoto3":e={title:"其他页",subtitle:"可拍摄购房合同封面或任意一页",image:"https://umoney.bdstatic.com/webroot/static/hiloan/pkg/image/_860e385.jpg"};break;case"houseContractPhoto1":e={title:"购房合同首页",subtitle:"照片必须包含<i>买卖双方信息</i>",image:"https://umoney.bdstatic.com/webroot/static/hiloan/pkg/image/_2868dcf.jpg"};
break;case"houseContractPhoto2":e={title:"购房合同签字页",subtitle:"照片必须包含<i>签字盖章信息</i>",image:"https://umoney.bdstatic.com/webroot/static/hiloan/pkg/image/_315097e.jpg"};break;case"houseContractPhoto3":e={title:"其他页",subtitle:"可拍摄购房合同封面或任意一页",image:"https://umoney.bdstatic.com/webroot/static/hiloan/pkg/image/_974f5a1.jpg"}
}return s.default.isEmpty(this.demoInfo)?e:s.default.extend(e,this.demoInfo)},iDesc:function(){if(this.desc)return this.desc;var e="";switch(this.type){case"idcardFront":e="点击拍摄身份证<i>正面</i>";break;case"idcardBack":e="点击拍摄身份证<i>反面</i>";
break;case"handheldIdcard":e="点击拍摄<i>手持</i>身份证正面照";break;case"bankcard":e="点击拍摄<i>银行卡</i>正面照"}return e},image:function(){var e=v.default.getPhotoFile&&v.default.getPhotoFile.getUrl(),t=b().search;t.token&&delete t.token,t=p.default.stringify(t);
var a="",i=G.constants.token;return e&&(a=-1!==e.indexOf("?")?""+location.origin+e+"&uuid="+this.uuid+"&"+t+"&token="+i:""+location.origin+e+"?uuid="+this.uuid+"&"+t+"&token="+i),this.file?this.uuid?this.file:a:this.uuid?a:""
},api:function P(){var P="camera";return("idcardFront"===this.type||"idcardBack"===this.type||"handheldIdcard"===this.type)&&(P="IDPhoto"),P},apiParam:function I(){var I={type:1};switch(this.type){case"idcardFront":I.type=2;
break;case"idcardBack":I.type=3;break;case"handheldIdcard":I.type=4}return I}},methods:{showDemo:function(){this.demo?this.demoShow=!0:this.takePhoto()},hideDemo:function(){this.demo&&(this.demoShow=!1)
},takePhoto:function(){if(this.hideDemo(),!this.h5Mode){var e=null,t="";s.default.delay(function(){var a=this;u.native.get(this.api,this.apiParam).then(function(i){return e=i,t=e.front||e.back||e.image,t?v.default.putFile({file:t,photoKey:a.iFieldname,stage:a.stage}):new Promise(function(e,t){t(!0)
})}).then(function(e){a.uuid=e.data.uuid,a.imageBase64=t,a.file="data:image/jpeg;base64,"+t,a.$emit("after-take-photo",a.file),a.$dispatch("after-take-photo",{type:a.type,file:a.imageBase64})})}.bind(this),100)
}},takeH5Photo:function(e){var t=this,a=e.target;if(a.files&&a.files.length>0){var i=a.files[0],o=i.type,s=i.name,n=i.size,c=/(image\/)|\.(png|jpg|jpeg|gif)/i,h=c.test(o)||c.test(s);n/=1048576;if(h){var r=window.webkitURL||window.URL,d=new Image;
d.src=r.createObjectURL(i),d.onload=function(){var e=t.compressImage(d);e&&v.default.putFile({file:e,photoKey:t.iFieldname,stage:t.stage}).then(function(a){t.uuid=a.data.uuid,t.imageBase64=e,t.file="data:image/jpeg;base64,"+e,t.$emit("after-take-photo"),t.$dispatch("after-take-photo",{type:t.type,file:t.imageBase64})
})}}else u.ui.toast({content:"图片仅支持png、gif、jpg格式"})}},compressImage:function(e,t,a){t=t||5;var i="image/jpeg";"undefined"!=typeof a&&"png"==a&&(i="image/png");var o=document.createElement("canvas");o.width=e.naturalWidth,o.height=e.naturalHeight;
var s=(o.getContext("2d").drawImage(e,0,0),o.toDataURL(i,t/100));return s.replace("data:image/jpeg;base64,","")},viewPhoto:function(){this.$root.whichPhoto=this.type,location.hash=this.viewHash+"&"+(+new Date).toString(32),this.viewMode=!0,this.$dispatch("viewPhoto",this.type)
},retakePhoto:function(){this.showDemo(),location.hash!==this.viewHash&&(this.$dispatch("retakePhoto",this.type),history.go(-1))},confirmPhoto:function(){history.go(-1)}},components:{uiMask:g.default},watch:{uuid:function(e){this.recordPhoto&&u.store.local.setItem(this.iFieldname,e)
}},created:function(){if(this.recordPhoto){var e=u.store.local.getItem(this.iFieldname);e&&(this.uuid=e)}},attached:function(){var e=this,t=function(){e.viewMode=location.hash!==e.viewHash&&e.type===e.$root.whichPhoto?!0:!1
};window.removeEventListener("hashchange",t),window.addEventListener("hashchange",t)}};var y='<section class="fin-rm-take-photo">\n    <p class="title" v-if="titleShow &amp;&amp; iTitle" v-html="iTitle"></p>\n    <p class="subtitle" v-if="subtitleShow &amp;&amp; subtitle" v-html="subtitle"></p>\n\n    <!-- 预览图片 && 重拍 -->\n    <div v-if="image" :class="{show: true,  rshow: !(tipShow &amp;&amp; tip)}" :style="{backgroundImage: \'url(\' + image + \')\'}">\n        <div class="take-wrapper" @click="viewPhoto">\n            <div class="image">\n                <i class="icon icon-camera" @click.stop="showDemo"></i>\n                <!-- 触发热区: H5拍摄 -->\n                <input v-if="h5Mode &amp;&amp; !demo" @click.stop="" @change="takeH5Photo" class="take-h5-camera" type="file" accept="image/*; capture=camera" capture="camera">\n            </div>\n            <p class="tip" v-show="tipShow &amp;&amp; tip" v-html="tip"></p>\n        </div>\n    </div>\n    <!-- Native拍摄 / H5拍摄 -->\n    <div v-else="" class="take">\n        <div class="take-wrapper" @click="showDemo">\n            <div class="image">\n                <i class="icon icon-camera"></i>\n            </div>\n            <p class="desc" v-html="iDesc"></p>\n            <!-- 触发热区: H5拍摄 -->\n            <input v-if="h5Mode &amp;&amp; !demo" @change="takeH5Photo" class="take-h5-camera" type="file" accept="image/*; capture=camera" capture="camera">\n        </div>\n    </div>\n\n    <div class="demo-wrap" v-show="demoShow" @touchmove.prevent="">\n        <ui-mask :show="true" @click="hideDemo"></ui-mask>\n        <div class="demo">\n            <p class="title" v-if="iDemoInfo.title" v-html="iDemoInfo.title"></p>\n            <i class="icon icon-close" @click="hideDemo"></i>\n\n            <div class="shoot-box">\n                <p class="subtitle" v-if="iDemoInfo.subtitle" v-html="iDemoInfo.subtitle"></p>\n                <p class="info" v-if="iDemoInfo.info || iDemoInfo.tip" v-html="iDemoInfo.info || iDemoInfo.tip"></p>\n                <div class="img-wrap">\n                    <div :style="{ backgroundImage: \'url(\' + iDemoInfo.image + \')\' }" class="img-carrier"></div>\n                </div>\n                <div class="fin-ui-button full" @click.stop="takePhoto">\n                    <!-- H5拍摄 -->\n                    <input v-if="h5Mode" @change="takeH5Photo" class="take-h5-camera" type="file" accept="image/*; capture=camera" capture="camera">\n                    开始拍摄\n                </div>\n            </div>\n        </div>\n    </div>\n\n    <div :class="[\'view\', viewMode ? \'visible\': \'\']" @touchmove.prevent="">\n        <ui-mask :show="true"></ui-mask>\n        <div class="image" :style="{backgroundImage: \'url(\' + image + \')\'}">\n        </div>\n        <div class="bottom">\n            <span @click="retakePhoto">\n                重拍\n                <!-- 触发热区: H5拍摄 -->\n                <input v-if="h5Mode &amp;&amp; !demo" @change="takeH5Photo" class="take-h5-camera" type="file" accept="image/*; capture=camera" capture="camera">\n            </span>\n            <span @click="confirmPhoto">确认</span>\n        </div>\n    </div>\n\n    <validator name="validation">\n        <input type="hidden" v-model="uuid" v-fieldname="iFieldname" v-validate:uuid="rules">\n    </validator>\n</section>';
a&&a.exports&&(a.exports.template=y),t&&t.default&&(t.default.template=y),a.exports=t["default"]});
;define("hiloan:components/fin-rm/take-photo/index",function(e,t,o){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var u=e("hiloan:components/fin-rm/take-photo/take-photo.vue"),a=n(u);
t.default=a.default,o.exports=t["default"]});
;define("hiloan:components/fin-rm/handheld-idcard/handheld-idcard.vue",function(e,t,d){"use strict";function a(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});
var n=e("hiloan:node_modules/vue/dist/vue.common"),i=(a(n),e("hiloan:components/fin-rm/take-photo/index")),l=a(i);t.default={props:{uuid:{type:String,"default":""},header:{type:Boolean,"default":!0},demo:{type:Boolean,"default":!0},desc:{type:String,"default":""},title:{type:String,"default":"拍照示例"},image:{type:String,"default":""}},data:function(){return{is:"rm-handheld-idcard",type:"handheldIdcard"}
},components:{takePhoto:l.default}};var o='<section class="fin-rm-handheld-idcard">\n    <take-photo :type="type" v-fieldset="type" :uuid="uuid" :header="header" :demo="demo" :desc="desc"></take-photo>\n\n    <div class="example">\n        <p class="title">{{ title }}</p>\n        <div v-if="image" class="image" :style="{backgroundImage: \'url(\' + image + \')\'}"></div>\n        <div v-else="" class="image default"></div>\n    </div>\n</section>';
d&&d.exports&&(d.exports.template=o),t&&t.default&&(t.default.template=o),d.exports=t["default"]});
;define("hiloan:components/fin-rm/handheld-idcard/index",function(e,d,n){"use strict";function a(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(d,"__esModule",{value:!0});var t=e("hiloan:components/fin-rm/handheld-idcard/handheld-idcard.vue"),i=a(t);
d.default=i.default,n.exports=d["default"]});
;define("hiloan:components/fin-rm/rm-date-input/rm-date-input.vue",function(e,t,a){"use strict";function l(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var d=e("hiloan:components/fin-ui/ui-date-input/index"),n=l(d);
t.default={props:{showPicker:{type:Boolean,"default":!1},size:{type:String,"default":""},title:{type:String,"default":""},placeholder:{type:String,"default":""},value:{type:String,"default":""},text:{type:String,"default":""},rules:{type:Object,"default":function(){return{}
}},fieldname:{type:String,"default":""},fieldset:{type:String,"default":""},border:{type:String,"default":""},tip:{type:String,"default":""},enable:{type:Boolean,"default":!0},dateType:{type:String,"default":"date"},startDate:Date,endDate:Date,dateValue:Date,defaultDate:{type:Date,"default":null}},data:function(){return{is:"rm-date-input"}
},methods:{},components:{uiDateInput:n.default}};var u='<section class="fin-{{ is }}">\n    <ui-date-input :show-picker="showPicker" :size="size" :title="title" :placeholder="placeholder" :value.sync="value" :text.sync="text" :rules="rules" :fieldname="fieldname" :fieldset="fieldset" :border="border" :tip="tip" :enable="enable" :date-type="dateType" :start-date="startDate" :end-date="endDate" :date-value="dateValue" :default-date="defaultDate"></ui-date-input>\n</section>';
a&&a.exports&&(a.exports.template=u),t&&t.default&&(t.default.template=u),a.exports=t["default"]});
;define("hiloan:components/fin-rm/rm-date-input/index",function(e,t,n){"use strict";function u(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var i=e("hiloan:components/fin-rm/rm-date-input/rm-date-input.vue"),d=u(i);
t.default=d.default,n.exports=t["default"]});
;define("hiloan:components/fin-rm/take-photo-group/take-photo-group.vue",function(t,e,i){"use strict";function o(t){return t&&t.__esModule?t:{"default":t}}Object.defineProperty(e,"__esModule",{value:!0});
var n=t("hiloan:node_modules/vue/dist/vue.common"),s=o(n),u=t("hiloan:components/fin-rm/take-photo/index"),l=o(u);s.default.component("take-photo-c",{template:'<take-photo\n                    :class="class"\n                    :title="title"\n                    :subtitle="subtitle"\n                    :title-show="titleShow"\n                    :subtitle-show="subtitleShow"\n                    :type="type"\n                    :fieldname="fieldname"\n                    :stage="stage"\n                    :uuid="uuid"\n                    :record-photo="recordPhoto"\n                    :desc="desc"\n                    :tip="tip"\n                    :tip-show="tipShow"\n                    :demo="demo"\n                    v-fieldset="fieldset"\n                    :demo-show="demoShow"\n                    :demo-info="demoInfo"\n                    :title="title">\n            </take-photo>',props:["class","title","subtitle","titleShow","subtitleShow","type","fieldname","stage","uuid","recordPhoto","desc","tip","tipShow","demo","fieldset","demoShow","demoInfo","title"],components:{takePhoto:l.default}}),e.default={props:{groupTitle:{type:String,"default":""},groupSubtitle:{type:String,"default":""},items:{type:Array,"default":[]},uuids:{type:Object,"default":function(){return{}
}}},data:function(){return{is:"rm-take-photo-group"}},methods:{isGroupLast:function(t){var e=!1;return this.itemsLen<=3?e=!0:0===this.remainNum&&t>this.itemsLen-3?e=!0:0!==this.remainNum&&t>this.itemsLen-this.remainNum&&(e=!0),e
},isGroupFill:function(t){return this.itemsLen>3&&1===this.remainNum&&t===this.itemsLen}},computed:{itemsLen:function(){return this.items.length},remainNum:function(){return this.itemsLen%3}},components:{takePhoto:l.default}};
var m='<section class="fin-rm-take-photo-group">\n    <div class="group-header" v-if="groupTitle || groupSubtitle">\n        <p v-text="groupTitle"></p>\n        <p v-text="groupSubtitle"></p>\n    </div>\n    <div class="group-wrap clearfix photo-num-{{ items.length }}">\n        <take-photo-c v-for="(index, item) in items" :class="{\'group-last\': isGroupLast(index + 1), \'group-fill\': isGroupFill(index + 1)}" :title="item.title" :subtitle="item.subtitle" :title-show="item.titleShow" :subtitle-show="item.subtitleShow" :type="item.type" :fieldname="item.fieldname" :stage="item.stage" :uuid="uuids[item.uuid]" :record-photo="item.recordPhoto" :desc="item.desc" :tip="item.tip" :tip-show="item.tipShow" :demo="item.demo" :fieldset="item.fieldset" :demo-show="item.demoShow" :demo-info="item.demoInfo">\n        </take-photo-c>\n    </div>\n</section>';
i&&i.exports&&(i.exports.template=m),e&&e.default&&(e.default.template=m),i.exports=e["default"]});
;define("hiloan:components/fin-rm/take-photo-group/index",function(e,o,t){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(o,"__esModule",{value:!0});var u=e("hiloan:components/fin-rm/take-photo-group/take-photo-group.vue"),r=n(u);
o.default=r.default,t.exports=o["default"]});
;define("hiloan:components/fin-rm/house-property/house-property.vue",function(e,t,n){"use strict";function o(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var r=e("hiloan:node_modules/vue/dist/vue.common"),i=(o(r),e("hiloan:app/static/config/api")),s=(o(i),e("hiloan:components/fin-ui/ui-tab/index")),a=o(s),d=e("hiloan:components/fin-rm/rm-input/index"),u=o(d),p=e("hiloan:components/fin-rm/rm-select/index"),l=o(p),c=e("hiloan:components/fin-rm/rm-date-input/index"),h=o(c),m=e("hiloan:components/fin-rm/take-photo-group/index"),f=o(m);
t.default={props:{currentTabIndex:{type:Number,"default":1,required:!1}},data:function(){return{is:"house-property",ocrRequestDone:!0,dateRules:{required:{rule:"true",message:"请选择正确的时间"}},items:[{name:"不动产权证/房产证",index:1},{name:"购房合同",index:2}]}
},methods:{},components:{uiTab:a.default,rmInput:u.default,rmSelect:l.default,rmDateInput:h.default,takePhotoGroup:f.default}};var b='<section class="fin-{{is}}">\n    <div class="title">\n        <h2>房产信息</h2>\n        <p><i class="icon icon-info-revert"></i>请完善房产证等相关信息，有助于获得更高的现金额度</p>\n    </div>\n    <div class="second-title">\n        请上传证件类型照片（二选一）\n    </div>\n    <ui-tab :index.sync="currentTabIndex" :items="items" size="full" :tab-change="fn">\n    </ui-tab>\n    <rm-input v-show="false" :value.sync="currentTabIndex" fieldname="hpType" fieldset="hpType" border="border-bottom">\n    </rm-input>\n    <take-photo-group v-if="currentTabIndex==1" :items="[\n            {\n                titleShow: false,\n                desc:\'个人所有权页\',\n                type: \'estateLicensePhoto1\',\n                fieldset: \'estateLicensePhoto1\'\n            },\n            {\n                titleShow: false,\n                desc:\'房屋测绘页\',\n                type: \'estateLicensePhoto2\',\n                fieldset: \'estateLicensePhoto2\'\n            },\n            {\n                titleShow: false,\n                desc:\'其他页\',\n                type: \'estateLicensePhoto3\',\n                fieldset: \'estateLicensePhoto3\'\n            }\n        ]">\n    </take-photo-group>\n    <take-photo-group v-if="currentTabIndex==2" :items="[\n            {\n                titleShow: false,\n                desc:\'合同正文首页\',\n                type: \'houseContractPhoto1\',\n                fieldset: \'houseContractPhoto1\'\n            },\n            {\n                titleShow: false,\n                desc:\'签字页\',\n                type: \'houseContractPhoto2\',\n                fieldset: \'houseContractPhoto2\'\n            },\n            {\n                titleShow: false,\n                desc:\'其他页\',\n                type: \'houseContractPhoto3\',\n                fieldset: \'houseContractPhoto3\'\n            }\n        ]">\n    </take-photo-group>\n    <div class="second-title">\n        请核实或补充以下信息\n    </div>\n    <div class="info-content">\n        <rm-input type="hpHolderName" :value="hpHolderName" border="border-bottom">\n        </rm-input>\n\n        <rm-select type="hpHouseNature" :value="hpHouseNature" border="border-bottom">\n        </rm-select>\n\n        <rm-select type="hpOwnCondition" :value="hpOwnCondition" border="border-bottom">\n        </rm-select>\n        \n        <rm-date-input title="登记时间" placeholder="请输入证件上登记时间" :start-date="new Date(1975, 0, 1)" :default-date="new Date(2000, 5, 15)" :end-date="new Date()" border="border-bottom" fieldname="hpRegisterTime" fieldset="hpRegisterTime" :rules="dateRules">\n        </rm-date-input>\n\n        <rm-input type="hpEstateLicense" :value="hpEstateLicense" :border-bottom="true">\n        </rm-input>\n\n        <rm-input type="hpHouseSize" :value="hpHouseSize" :border-bottom="true">\n        </rm-input>\n\n        <rm-select type="currentCity" :value="hpHouseNature" fieldname="hpHouseAddressCity" fieldset="hpHouseAddressCity" border="border-bottom">\n        </rm-select>\n\n        <rm-input type="hpHouseAddress" category="only-header" header="详细地址" :border-bottom="true"></rm-input>\n\n    </div>\n</section>';
n&&n.exports&&(n.exports.template=b),t&&t.default&&(t.default.template=b),n.exports=t["default"]});
;define("hiloan:components/fin-rm/house-property/index",function(e,o,t){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(o,"__esModule",{value:!0});var r=e("hiloan:components/fin-rm/house-property/house-property.vue"),u=n(r);
o.default=u.default,t.exports=o["default"]});
;define("hiloan:components/fin-ui/ui-count-down/ui-count-down.vue",function(t,e,i){"use strict";function n(t){return t&&t.__esModule?t:{"default":t}}Object.defineProperty(e,"__esModule",{value:!0});var o=t("hiloan:node_modules/vue/dist/vue.common"),a=(n(o),t("hiloan:components/fin-ui/ui-mask/index")),s=n(a);
e.default={props:{show:{type:Boolean,"default":!1},text:{type:String,"default":"正在审批..."},time:{type:Number,"default":30}},data:function(){return{is:"ui-count-down"}},components:{uiMask:s.default},ready:function(){var t=this.time;
this.$watch("show",function(){var e=this;this.show?(this.time=t,this.timerId=setInterval(function(){e.time--,e.time<=0&&(e.show=!1,clearInterval(e.timerId))},1e3)):clearInterval(this.timerId)})}};var d='<!-- 倒计时 loading -->\n<ui-mask :show="show" :opacity="0">\n</ui-mask>\n<div v-show="show" transition="fade" class="fin-ui-count-down">\n    <div class="loading-mask"></div>\n    <div class="loading-loader"></div>\n    <div class="loading-time">{{time}}</div>\n    <div class="loading-content">{{text}}</div>\n</div>';
i&&i.exports&&(i.exports.template=d),e&&e.default&&(e.default.template=d),i.exports=e["default"]});
;define("hiloan:components/fin-ui/ui-count-down/index",function(e,n,u){"use strict";function o(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var t=e("hiloan:components/fin-ui/ui-count-down/ui-count-down.vue"),i=o(t);
n.default=i.default,u.exports=n["default"]});
;define("hiloan:components/fin-rm/next-step/next-step.vue",function(t,e,i){"use strict";function a(t){return t&&t.__esModule?t:{"default":t}}Object.defineProperty(e,"__esModule",{value:!0});var n=t("hiloan:node_modules/vue/dist/vue.common"),o=a(n),s=t("hiloan:node_modules/es6-promise/dist/es6-promise"),l=t("hiloan:node_modules/query-string/index"),u=a(l),r=t("hiloan:app/static/config/api"),d=a(r),c=t("hiloan:components/fin-fg/util/native/index"),f=a(c),p=t("hiloan:components/fin-fg/util/request/redirect"),h=a(p),m=t("hiloan:components/fin-fg/util/store/index"),g=a(m),v=t("hiloan:components/fin-ui/ui-loading/index"),y=a(v),b=t("hiloan:components/fin-ui/ui-toast/index"),x=a(b),A=t("hiloan:components/fin-ui/ui-count-down/index"),$=a(A),w=t("hiloan:node_modules/vue-validator-manage/dist/vue-validator-manage.common"),q=a(w),R=t("hiloan:node_modules/object-assign/index"),P=a(R),I=t("hiloan:node_modules/vue-touch/vue-touch"),S=a(I),B=(t("hiloan:components/fin-fg/util/request/query"),t("hiloan:components/fin-fg/util/native/getNativeFields")),k=a(B),C=t("hiloan:components/fin-fg/util/index"),D=t("hiloan:components/fin-fg/track/log"),_=a(D),N=t("hiloan:components/fin-fg/directive/log/index"),j=(a(N),g.default.local);
o.default.use(q.default),o.default.use(S.default),S.default.registerCustomEvent("doubletap",{type:"tap",taps:2}),e.default={data:function(){return{is:"next-step",_time:0,loading:!1,countDown:!1,toast:!1,toastContent:"",map:{apply:{applyBasic:1,applySupply:1},active:{activateBasic:1,activateVerification:1,supplySubmit:1}}}
},computed:{disabled:function(){if("false"===this.lightUp)return!0;if("true"===this.lightUp)return!1;if(!this.$root.validation)return!1;var t=void 0,e=this.$root.validation.valid;if(e)switch(this.$root.validation.asyncResult){case"true":t=!1;
break;case"false":t=!0;break;case"loading":case"init":t=!0;break;default:t=!0}else t=!0;return t}},props:{lightUp:{type:String,"default":""},beforeSubmit:{type:Function},beforeRedirect:{type:Function},afterCheckFail:{type:Function},hasLoading:{type:Boolean,"default":!0},hasCountDown:{type:Boolean,"default":!1},hasMargin:{type:Boolean,"default":!0},save:{type:Boolean,"default":!0},position:{type:Boolean,"default":!0},pageId:{type:String,required:!0},stage:{type:String},moduleId:{type:String},title:{type:String,"default":""},"class":{type:String,"default":"default"},type:{type:String},poll:{type:Boolean,"default":!1},pollTimelimit:{type:Number},pollInterval:{type:Number,"default":1e3},param:{type:Object},auto:{type:Boolean,required:!1},message:{type:Boolean,"default":!0},hasToast:{type:Boolean,"default":!1},extraNa:{type:Object,"default":null},logConfig:{type:String,"default":"nextstep"},ignorePosAccess:{type:Boolean,"default":!1}},components:{uiLoading:y.default,uiToast:x.default,uiCountDown:$.default},ready:function(){this.title||(this.title=this.type?"提交":"下一步"),this.auto&&this.ajax()
},methods:{msg:function(t){this.loading=!1,this.countDown=!1,this.hasToast&&t&&t.errmsg&&t.errno>0&&t.errno<=100?(this.toast=!0,this.toastContent=t.errmsg):this.$dispatch("error",t,this)},error:function(){this.disabled&&this.$root.$broadcast("show-error-message")
},clearLocal:function(){var t=this.$root.fieldsData;t&&Object.keys(t).forEach(function(t){j.removeItem(t)})},checkApplyAction:function(t){var e=this;if(t){this.$dispatch("beforeCheckApply",this);var i={"x-silent":!0,"x-message":this.message};
return d.default.checkApply({},i).then(function(t){return 0!==t.errno?e.afterCheckFail(t):(e.$dispatch("afterCheckApply",t,e),t)}).catch(function(t){e.msg(t),e.$dispatch("afterCheckApply",t,e)})}},saveAction:function(t){var e=this;
if(t){if(!this.save)return new s.Promise(function(t){t(!0)});this.$dispatch("beforeSaveRequest",this);var i={};if(!this.$root.getFieldsData)return new s.Promise(function(t){t(!0)});i=this.$root.getFieldsData();
var a=P.default({pageId:this.pageId,stage:this.stage,moduleId:this.moduleId,data:P.default(i,this.param)}),n={"x-silent":!0,"x-message":this.message};return d.default.setPageData(a,n).then(function(t){return e.$dispatch("afterSaveRequest",t,e),t
}).catch(function(t){e.msg(t),e.$dispatch("afterSaveRequest",t,e)})}},positionAction:function(t){var e=this;return t?this.position?(this.$dispatch("beforePosition",this),f.default.get("position").then(function(t){return e.$dispatch("afterPosition",t,e),t
}).catch(function(t){if(e.ignorePosAccess){var i=+sessionStorage.getItem("ignorePosAccess");if(1===i)return s.Promise.resolve(t);sessionStorage.setItem("ignorePosAccess",1)}e.msg(t),e.$dispatch("afterPosition",t,e)
})):new s.Promise(function(t){t(!0)}):void 0},bizAction:function(t){var e=this;if(t){this.$dispatch("beforeBizRequest",this);var i={"x-silent":!0,"x-message":this.message},a=this.poll?this.pollAction:this.navAction;
return k.default(this.extraNa).then(function(n){var o=P.default(t.data||{},e.param,n);return d.default[e.type](o,i).then(function(t){return e.$dispatch("afterBizRequest",t,a,e),t}).catch(function(t){return e.msg(t),e.$dispatch("afterBizRequest",t,a,e),t&&30001===t.errno?!0:void 0
})})}},pollAction:function(t){var e=this;if(t){this.$dispatch("beforePollRequest",this);var i=void 0,a=void 0;for(var n in this.map)if(this.map[n][this.type]){i=n;break}if("apply"==i)a="applyResult";else{if("active"!=i)return;
a="activateResult"}var o={"x-silent":!0,"x-message":this.message};return d.default[a](this.param,o).then(function(t){4==t.data.status||7==t.data.status?(e.hasCountDown&&!e.countDown&&(e.navAction(t),e.$dispatch("afterPollRequest",t,e)),!e.pollTimelimit||e._time<e.pollTimelimit?(setTimeout(e.pollAction,e.pollInterval,t),e._time+=e.pollInterval):(e.msg("请求超时"),e.$dispatch("afterPollRequest",e))):(e.navAction(t),e.$dispatch("afterPollRequest",t,e))
}).catch(function(t){e.msg(t),e.$dispatch("afterPollRequest",t,e)})}},navAction:function(t){var e=this;if(t){this.$dispatch("beforeNavRequest",this);var i=function(){var t=P.default({pageId:e.pageId,moduleId:e.moduleId,stage:e.stage,originalUrl:location.href},e.param),i={"x-silent":!0,"x-message":e.message};
return d.default.getNextPage(t,i).then(function(t){return e.$dispatch("afterNavRequest",t,e),t}).then(function(t){if(e.loading=!1,e.countDown=!1,e.$dispatch("afterNavRequest",t,e),!t||!t.data)return t;
var i={};t.data.stage&&(i.stage=t.data.stage),t.data.pageId&&(i.pageId=t.data.pageId);var a=t.data.url,n={};-1!==a.indexOf("?")&&(n=u.default.parse(a.slice(a.indexOf("?"))));var o=e.param;o&&o.url&&delete o.url,h.default(t.data.url,P.default({},i,o,n))
}).catch(function(t){e.msg(t),e.$dispatch("afterNavRequest",t,e)})};return this.beforeRedirect&&this.beforeRedirect(t,i)?(this.loading=!1,void(this.countDown=!1)):i()}},ajax:function(){var t=this;if(!t.disabled)if(t.beforeSubmit){var e=t.beforeSubmit();
if(e instanceof s.Promise)e.then(function(e){e||t.submit()});else{if(e)return;t.submit()}}else t.submit()},submit:function(){var t=this;t.hasCountDown?t.countDown=!0:t.hasLoading&&(t.loading=!0),t._time=0,t.type?"confirmApply"==t.type?t.checkApplyAction(!0).then(t.saveAction).then(t.navAction):t.saveAction(!0).then(t.positionAction).then(t.bizAction).then(t.poll?t.pollAction:t.navAction):t.saveAction(!0).then(t.navAction)
}},created:function(){window.G&&window.G.constants&&window.G.constants.autoSendBfbLog&&C.eventBus.$on("invalidToValid",function(t){_.default.sendBfbAction(t.fieldname||t.is)})}};var F='<div class="fin-next-step {{hasMargin?\'space\':\'\'}}">\n    <div class="fin-ui-button full" :class="[class]" v-touch:tap="ajax" :disabled="disabled" v-touch:doubletap="error" v-log="logConfig" :extra-na="extraNa">\n        <slot name="title">{{{ title }}}</slot>\n    </div>\n    <ui-loading :show.sync="loading"></ui-loading>\n    <ui-count-down :show.sync="countDown"></ui-count-down>\n    <ui-toast :show.sync="toast" :content="toastContent" :mask="false"></ui-toast>\n</div>';
i&&i.exports&&(i.exports.template=F),e&&e.default&&(e.default.template=F),i.exports=e["default"]});
;define("hiloan:components/fin-rm/next-step/index",function(n,e,t){"use strict";t.exports=n("hiloan:components/fin-rm/next-step/next-step.vue")});
;define("hiloan:components/fin-ui/ui-radio/show",function(t,i,r){"use strict";Object.defineProperty(i,"__esModule",{value:!0});var e="init",o="error",n="",s="true";i.default={props:{topError:{type:String,"default":n}},computed:{tipState:function(){var t=e;
return this.$validation.invalid&&"true"===this.topError?t=o:"false"===this.topError&&(t=e),t},showTip:function(){var t=!1;return(this.tipState===e&&this.tip||this.tipState===o)&&(t=!0),t},tipContent:function(){var t=this.tip;
if(this.tipState===o){var i=this.$validation.value.errors;i.length>0&&(t=i[0].message)}return t}},events:{"show-error-message":function(){this.topError=s}}},r.exports=i["default"]});
;define("hiloan:components/fin-ui/ui-radio/ui-radio.vue",function(i,e,t){"use strict";function n(i){return i&&i.__esModule?i:{"default":i}}Object.defineProperty(e,"__esModule",{value:!0});var a=i("hiloan:node_modules/vue/dist/vue.common"),s=n(a),l=i("hiloan:node_modules/vue-validator/dist/vue-validator.common"),d=n(l),o=i("hiloan:components/fin-ui/ui-radio/show"),r=n(o),c=i("hiloan:components/fin-fg/util/index"),v=c.store.local;
s.default.use(d.default),e.default={mixins:[r.default],props:{title:String,type:{type:String,"default":"vertical"},list:{type:Array,required:!0,"default":function(){return[]}},tip:{type:String,"default":""},rules:{type:Object,"default":function(){return{}
}},fieldname:{type:String,"default":""},value:{type:String,"default":""},description:{type:String,"default":""},recordRadio:{type:Boolean,"default":!0}},computed:{hasErrorClass:function(){return"error"===this.tipState?"error":""
}},data:function(){return{is:"ui-radio"}},methods:{check:function(i){this.value=i.value,this.$dispatch("radioSelected",this.value)}},watch:{value:function(i){this.recordRadio&&v.setItem(this.fieldname,i)
}},created:function(){if(this.recordRadio){var i=v.getItem(this.fieldname);i&&i.trim()&&(this.value=i)}}};var u='<section class="fin-ui-radio" :class="type">\n    <section :class="type">\n        <validator name="validation">\n            <div class="radio-title" v-if="title">{{ title }}</div>\n            <div class="radio-group clearfix">\n                <div v-if="type == \'button\'">\n                    <div class="radio-list-wrap clearfix">\n                        <div v-for="(index, item) in list" class="radio-list-box">\n                            <div class="radio-list" :class="{\'active\': item.value === value}" @click="check(item)">\n                                <input type="button" class="fin-ui-button large primary" value="{{ item.desc }}">\n                            </div>\n                        </div>\n                    </div>\n                    <div v-if="description" class="description">{{ description }}</div>\n                </div>\n                <div v-else="">\n                    <div v-for="(index, item) in list" class="radio-list-box">\n                        <div class="radio-list" :class="{\'active\': item.value === value}" @click="check(item)">\n                            <div class="left">\n                                <i class="icon" v-if="type == \'transverse\'" :class="[item.value === value ? \'icon-radio\' : \'icon-radio-off\']">\n                                </i>\n                                <i class="icon" v-else="" :class="[item.value === value ? \'icon-radio-on\' : \'icon-radio-off\']">\n                                </i>\n                            </div>\n                            <div class="right">\n                                <div class="type">\n                                    <span class="type-letter">{{ item.type }}</span>\n                                </div>\n                                <div class="desc">{{ item.desc }}</div>\n                            </div>\n                        </div>\n                        <div v-if="type == \'vertical\'">\n                            <div v-if="index < list.length - 1" class="line"></div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n            <input type="hidden" v-model="value" v-validate:value="rules" v-fieldname="fieldname">\n            <section class="tip {{hasErrorClass}}" v-if="showTip">\n                <i class="icon icon-info"></i>\n                <p v-text="tipContent"></p>\n            </section>\n        </validator>\n    </section>\n    <div v-if="type == \'transverse\'" class="line"></div>\n</section>';
t&&t.exports&&(t.exports.template=u),e&&e.default&&(e.default.template=u),t.exports=e["default"]});
;define("hiloan:components/fin-ui/ui-radio/index",function(e,i,u){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(i,"__esModule",{value:!0});var o=e("hiloan:components/fin-ui/ui-radio/ui-radio.vue"),t=n(o);
i.default=t.default,u.exports=i["default"]});
;define("hiloan:components/fin-rm/position-type/position-type.vue",function(e,t,i){"use strict";function a(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var l=e("hiloan:node_modules/vue/dist/vue.common"),u=a(l),o=e("hiloan:node_modules/vue-validator/dist/vue-validator.common"),n=a(o),d=e("hiloan:node_modules/vue-validator-manage/dist/vue-validator-manage.common"),r=a(d),p=e("hiloan:components/fin-ui/ui-radio/index"),s=a(p);
u.default.use(n.default),u.default.use(r.default),t.default={props:{fieldname:{type:String,"default":"jobType"},fieldset:{type:String,"default":"jobType"},value:{type:String,"default":""},tip:{type:String,"default":""},topError:{type:String,"default":""},rules:{type:Object,"default":function(){return{required:{rule:!0,message:"请选择职业类型"}}
}},title:{type:String,"default":"您当前的真实职业是"},list:{type:Array,"default":function(){return[{value:"1",type:"在职人员",desc:"具有稳定收入来源 如:公司职员、公务员、教师等"},{value:"2",type:"私营业主",desc:"个人独资、合作企业投资者等"},{value:"3",type:"自由职业",desc:"个体户、网店卖家等"}]
}},type:{type:String,"default":"vertical"}},data:function(){return{is:"rm-position-type"}},components:{uiRadio:s.default}};var f='<div class="fin-{{is}}">\n    <ui-radio :title="title" :type="type" :list="list" :tip="tip" :value="value" :top-error="topError" :fieldname="fieldname" v-fieldset="fieldset" :rules="rules">\n    </ui-radio>\n</div>';
i&&i.exports&&(i.exports.template=f),t&&t.default&&(t.default.template=f),i.exports=t["default"]});
;define("hiloan:components/fin-rm/position-type/index",function(e,t,n){"use strict";function o(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var i=e("hiloan:components/fin-rm/position-type/position-type.vue"),u=o(i);
t.default=u.default,n.exports=t["default"]});
;define("hiloan:components/fin-ui/ui-table/ui-table.vue",function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default={is:"ui-table",props:{items:{type:Array,"default":function(){return[]
}},hasTitle:{type:Boolean,"default":!1},align:{type:String,"default":""},fontSize:{type:String,"default":"14"},color:{type:String,"default":""},darkColor:{type:String,"default":""},dark:{type:Array,"default":function(){return[]
}},zebra:{type:String,"default":""}},methods:{getAlign:function(t){return this.align?this.align:2===t.length?"justify":"center"},getTitle:function(t){return this.hasTitle&&0===t?" tit":""},getFontSize:function(){return[" f",this.fontSize].join("")
},getColor:function(t){var e=void 0;return e=-1!==this.dark.indexOf(t+1)?this.darkColor:this.color,e?{color:e}:{}},getZebra:function(t){return this.hasTitle&&0===t?"":" "+this.zebra}}};var i='<table class="fin-ui-table">\n    <tbody>\n        <tr v-for="(n, item) in items" class="item {{ getZebra(n) + getTitle(n) }}">\n            <td v-for="(index, cell) in item" class="w{{ item.length + \' \' +getAlign(item) + getFontSize() }}" :style="[getColor(index)]">\n                {{{ cell }}}\n            </td>\n        </tr>\n    </tbody>\n</table>';
n&&n.exports&&(n.exports.template=i),e&&e.default&&(e.default.template=i),n.exports=e["default"]});
;define("hiloan:components/fin-ui/ui-table/index",function(e,t,u){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var i=e("hiloan:components/fin-ui/ui-table/ui-table.vue"),l=n(i);
t.default=l.default,u.exports=t["default"]});
;define("hiloan:components/fin-rm/select-installment/select-installment.vue",function(t,e,n){"use strict";function i(t){return t&&t.__esModule?t:{"default":t}}Object.defineProperty(e,"__esModule",{value:!0});
var a=t("hiloan:node_modules/vue/dist/vue.common"),s=i(a),l=t("hiloan:node_modules/iscroll/build/iscroll"),o=i(l),u=(t("hiloan:node_modules/es6-promise/dist/es6-promise"),t("hiloan:components/fin-fg/util/index"),t("hiloan:components/fin-ui/ui-toast/index")),r=i(u),d=t("hiloan:components/fin-rm/rm-select/index"),c=i(d),h=t("hiloan:components/fin-ui/ui-table/index"),f=i(h),m=t("hiloan:node_modules/vue-touch/vue-touch"),v=i(m),p=t("hiloan:app/static/config/api"),y=i(p),x=t("hiloan:components/fin-fg/util/format/index"),b=i(x),g=t("hiloan:node_modules/vue-validator/dist/vue-validator.common"),S=i(g),T=t("hiloan:node_modules/vue-validator-manage/dist/vue-validator-manage.common"),w=i(T);
s.default.use(S.default),s.default.use(w.default),s.default.use(v.default),e.default={data:function(){return{isShow:!1,transitionType:"",is:"select-installment",hash:"installment-detail",reg:/&?installment-detail/g,toast:!1,toastText:"",amountTable:null,infoTable:null,iScroll:null,empty:!0,current:this.list&&this.list.length&&this.list[0].value}
},props:{value:{type:String,"default":"",coerce:function(t){return t+""}},beforeShow:{type:Function},projectid:{type:String,"default":"",coerce:function(t){return t+""}},list:{type:Array,required:!0},totalfeetext:{type:String,"default":"总手续费"},feetext:{type:String,"default":"手续费"},trxnamount:{type:Number,required:!0},recordInput:{type:Boolean,"default":!0},hot:{type:Boolean,"default":!1}},computed:{text:function(){var t=this.value+"期";
return this.value||(t=""),t}},components:{uiToast:r.default,rmSelect:c.default,uiTable:f.default},ready:function(){var t=this;this.hashMarked()&&this.show(),window.addEventListener("hashchange",function(){t.isShow=t.hashMarked()
},!1),this.transitionType="fade"},methods:{hashMarked:function(){return this.reg.test(location.hash)},select:function(t){var e=+t;this.current!==e&&(this.current=e,this.getServerData())},getServerData:function(){var t=this,e={periods:this.current,trxnamount:this.trxnamount};
y.default.getLoanTrail(e).then(function(e){var n=e.data;if(n&&n.total&&n.bills){t.amountTable=[["总还款",b.default.money(n.total.moneyTotal,"en",2)],["本金",b.default.money(n.total.prinamtTotal,"en",2)],[t.totalfeetext,b.default.money(n.total.feeTotal,"en",2)]],t.infoTable=[["分期","预估还款时间","本金",t.feetext]];
for(var i,a=0;i=n.bills[a++];)t.infoTable.push(["第"+i.period+"期",b.default.datetime(i.dueDate),b.default.money(i.prinamt,"",2),b.default.money(i.fee,"",2)])}t.empty=!1,t.$nextTick(function(){t.iScroll?t.iScroll.refresh():t.iScroll=new o.default(".installment-mask")
})}).catch(function(){t.empty=!0})},hide:function(){this.value=this.current+"",this.isShow=!1,location.hash=location.hash.replace(this.reg,""),this.$dispatch("hide",this)},show:function(){var t=this;this.value&&(this.current=+this.value),this.beforeShow&&this.beforeShow()||(this.isShow=!0,this.hashMarked()||(location.hash=location.hash?location.hash+"&"+this.hash:this.hash),this.$dispatch("show",this),this.getServerData(),this.$nextTick(function(){t.iScroll&&t.iScroll.scrollTo(0,0)
}))}}};var k='<div class="fin-select-installment">\n    <rm-select type="installment" @select-click="show" :record-input="recordInput" :text.sync="text" :value.sync="value">\n    </rm-select>\n\n    <ui-toast :show.sync="toast" :content="toastText"></ui-toast>\n    <div class="installment-mask" v-show="isShow" @touchstart.prevent="" :transition="transitionType">\n        <div class="mask-wrap">\n            <div class="mask-header">\n                <div class="header-list">\n                    <div class="list-item box-center" v-for="(index, item) in list" v-touch:tap="select(item.value)" :class="{ \'cur\':item.value==current}">\n                        <div class="item-text box-center" :class="{\'hot\': hot &amp;&amp; (index === list.length - 1)}">\n                            <div class="text-high">{{item.value}}</div>\n                            <div class="text-low">期</div>\n                        </div>\n                    </div>\n                </div>\n                <div class="header-arrow"></div>\n            </div>\n            <div class="mask-content">\n                <div v-if="!empty">\n                    <div class="content-amount">\n                        <ui-table :items="amountTable" font-size="12" align="justify">\n                        </ui-table>\n                    </div>\n                    <div class="content-info">\n                        <ui-table :items="infoTable" align="justify" :has-title="true" zebra="even">\n                        </ui-table>\n                    </div>\n                </div>\n                <div class="mask-empty box-center" v-else="">\n                    暂无数据\n                </div>\n            </div>\n        </div>\n\n        <div class="mask-footer">\n            <div class=" fin-ui-button full" v-touch:tap="hide">提交</div>\n        </div>\n    </div>\n</div>';
n&&n.exports&&(n.exports.template=k),e&&e.default&&(e.default.template=k),n.exports=e["default"]});
;define("hiloan:components/fin-rm/select-installment/index",function(n,e,t){"use strict";t.exports=n("hiloan:components/fin-rm/select-installment/select-installment.vue")});
;define("hiloan:components/fin-rm/social-info/social-info.vue",function(e,i,t){"use strict";function s(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(i,"__esModule",{value:!0});var l=e("hiloan:node_modules/vue/dist/vue.common"),a=s(l),n=e("hiloan:node_modules/vue-touch/vue-touch"),u=s(n),o=e("hiloan:components/fin-rm/rm-input/index"),d=s(o),c=e("hiloan:components/fin-fg/util/store/local"),r=s(c),m=e("hiloan:components/fin-ui/ui-radio/index"),f=s(m),p=e("hiloan:components/fin-fg/util/validator/index"),h=s(p),g=e("hiloan:node_modules/vue-validator-manage/dist/vue-validator-manage.common"),y=s(g);
a.default.use(u.default),a.default.use(y.default);var v=h.default.custom.email,q=h.default.custom.qq,R=h.default.custom.weixin;i.default={props:{email:{type:String,"default":""},socialType:{type:String,"default":"weixin"},socialNo:{type:String,"default":""},emailPlaceholder:{type:String,"default":"个人电子邮箱地址"},noRules:{type:Object,defult:function(){return{required:{rule:!0,message:"请填写帐号号码"}}
}},fieldnames:{type:Array,"default":function(){return["email","socialType","socialNo"]}},fieldsets:{type:Array,"default":function(){return["email","socialType","socialNo"]}},emailEdit:{type:Boolean,"default":!1},emailRules:{type:Object,defult:function(){return{required:{rule:!0,message:"请填写邮箱"},email:v}
}}},data:function(){return{is:"rm-social-info",canEditClass:"",socialRadio:"",suggestList:[],socialInputType:"text",socialTypeList:[{value:"weixin",type:"微信"},{value:"qq",type:"QQ号"}]}},components:{rmInput:d.default,uiRadio:f.default},created:function(){""!=this.email?(this.emailEdit=!0,this.emailPlaceholder=this.email,this.email="",this.emailRules={email:v},this.canEditClass="can-edit-class"):(this.canEditClass="",this.emailRules={required:{rule:!0,message:"请填写邮箱"},email:v})
},ready:function(){this.socialRadio=this.socialType,this.noRules="qq"===this.socialType?{required:{rule:!0,message:"请填写帐号号码"},qq:q}:{required:{rule:!0,message:"请填写帐号号码"},weixin:R}},init:function(){},methods:{edit:function(){this.emailRules={required:{rule:!0,message:"请填写邮箱"},email:v},this.emailPlaceholder="个人电子邮箱地址",this.canEditClass=""
},updateSuggest:function(e){if(""===e||e.match(/@\w*/))return void this.clearSuggest();var i=["qq.com","163.com","126.com"];this.suggestList=i.map(function(i){return e+"@"+i})},clearSuggest:function(){this.suggestList=[]
},selectEmail:function(e){this.email=this.suggestList[e],this.suggestList=[]},setSocialType:function(e){this.socialTypeList.forEach(function(e){e.selected=!1}),this.socialTypeList[e].selected=!0}},events:{edit:function(){this.emailRules={required:{rule:!0,message:"请填写邮箱"},email:v}
},radioSelected:function(e){this.socialRadio!==e&&(r.default.setItem(this.socialRadio,this.socialNo),this.socialRadio=e,this.socialNo=r.default.getItem(e),"qq"===e?(this.socialInputType="tel",this.noRules={required:{rule:!0,message:"请填写帐号号码"},qq:q}):(this.socialInputType="text",this.noRules={required:{rule:!0,message:"请填写帐号号码"},weixin:R}))
},input:function(e){"email"===e.fieldname}}};var T='<div class="fin-{{ is }}">\n    <div class="email-field {{canEditClass}}">\n        <rm-input type="emailList" input-type="email" :value="email" :edit="emailEdit" :fieldname.sync="fieldnames[0]" @edit="edit" :placeholder="emailPlaceholder" :rules="emailRules" :fieldset="fieldsets[0]">\n        </rm-input>\n\n        <ul class="suggest-list">\n            <li class="cut1" v-for="item in suggestList" v-on:click.stop="selectEmail($index)">{{ item }}</li>\n        </ul>\n    </div>\n    <div class="social-line"></div>\n\n    <ui-radio title="社交帐号" :value.sync="socialType" :fieldname="fieldnames[1]" :fieldset="fieldsets[1]" type="transverse" :list="socialTypeList">\n    </ui-radio>\n\n    <div class="social-line"></div>\n\n    <rm-input v-ref:social-no="" type="socialNo" :rules="noRules" :input-type="socialInputType" :value.sync="socialNo" :fieldname="fieldnames[2]" :fieldset="fieldsets[2]">\n    </rm-input>\n\n</div>';
t&&t.exports&&(t.exports.template=T),i&&i.default&&(i.default.template=T),t.exports=i["default"]});
;define("hiloan:components/fin-rm/social-info/index",function(e,n,o){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var t=e("hiloan:components/fin-rm/social-info/social-info.vue"),f=i(t);
n.default=f.default,o.exports=n["default"]});
;define("hiloan:components/fin-rm/student-type/student-type.vue",function(e,t,d){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var u=e("hiloan:node_modules/vue/dist/vue.common"),a=i(u),l=e("hiloan:node_modules/vue-validator/dist/vue-validator.common"),n=i(l),o=e("hiloan:node_modules/vue-validator-manage/dist/vue-validator-manage.common"),r=i(o),s=e("hiloan:components/fin-ui/ui-radio/index"),f=i(s);
a.default.use(n.default),a.default.use(r.default),t.default={props:{fieldname:{type:String,"default":"isStudent"},fieldset:{type:String,"default":"isStudent"},value:{type:String,"default":""},rules:{type:Object,"default":function(){return{required:{rule:!0,message:"请选择完际上课学生"}}
}},title:{type:String,"default":"实际上课学生是"},description:{type:String,"default":"目前仅支持以上两类用户"},list:{type:Array,required:!0,"default":[{value:"1",desc:"我自己"},{value:"2",desc:"我的子女"}]},type:{type:String,"default":"button"},recordRadio:{type:Boolean,"default":!0}},data:function(){return{is:"rm-student-type"}
},components:{uiRadio:f.default}};var p='<div class="fin-{{is}}">\n    <ui-radio :title="title" :type="type" :list="list" :value.sync="value" :description="description" :fieldname="fieldname" v-fieldset="fieldset" :record-radio="recordRadio" :rules="rules">\n    </ui-radio>\n</div>';
d&&d.exports&&(d.exports.template=p),t&&t.default&&(t.default.template=p),d.exports=t["default"]});
;define("hiloan:components/fin-rm/student-type/index",function(e,t,n){"use strict";function u(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var d=e("hiloan:components/fin-rm/student-type/student-type.vue"),o=u(d);
t.default=o.default,n.exports=t["default"]});
;define("hiloan:components/fin-rm/tel/tel.vue",function(e,t,n){"use strict";function l(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var i=e("hiloan:node_modules/underscore/underscore"),o=l(i),u=e("hiloan:node_modules/vue/dist/vue.common"),a=(l(u),e("hiloan:components/fin-fg/util/index")),s=e("hiloan:components/fin-rm/rm-input/index"),r=l(s),d=a.store.local,f=["010","020","021","022","023","025","026","027","028","029","852","853"];
t.default={props:{value:{type:String,"default":"",coerce:function(e){return e+""}},rules:{type:Object,"default":null},fieldname:{type:String,"default":"officePhone"},hasLine:{type:Boolean,"default":!0},recordInput:{type:Boolean,"default":!0},border:{type:Boolean,"default":!1}},data:function(){return{is:"tel",showValue:""}
},components:{rmInput:r.default},methods:{format:function(e){e=e.trim();var t=e;return-1===e.indexOf("-")&&e.length>=3&&(-1!==o.default.indexOf(f,e.slice(0,3))?t=e.slice(0,3)+"-"+e.slice(3):e.length>=4&&(t=e.slice(0,4)+"-"+e.slice(4))),this.value=t,this.hasLine||(this.value=t.split("-").join("")),t
}},watch:{showValue:function(e){this.recordInput&&d.setItem(this.fieldname,e)}},events:{input:function(e){return this.showValue=this.format(e.value),!0}},created:function(){if(this.recordInput){var e=d.getItem(this.fieldname)||this.value;
this.showValue=this.format(e)}else this.showValue=this.format(this.value)}};var c='<div class="fin-{{is}}">\n    <div class="tel">\n        <rm-input v-ref:tel="" type="tel" :value="showValue" :rules="rules" :record-input="false" :maxlength="13" :collect="false" :border="border">\n        </rm-input>\n\n        <input type="hidden" v-fieldname="fieldname" v-model="value">\n    </div>\n</div>';
n&&n.exports&&(n.exports.template=c),t&&t.default&&(t.default.template=c),n.exports=t["default"]});
;define("hiloan:components/fin-rm/tel/index",function(e,t,n){"use strict";function l(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var o=e("hiloan:components/fin-rm/tel/tel.vue"),u=l(o);
t.default=u.default,n.exports=t["default"]});
;define("hiloan:components/fin-rm/info-card/info-card.vue",function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={props:{title:{type:String,required:!0,"default":""},contents:{type:Array,"default":[]},border:{type:String,"default":""}},data:function(){return{is:"info-card"}
}};var i='<section class="fin-{{ is }}">\n    <div class="main {{ border }}">\n        <h3 class="title">{{ title }}</h3>\n        <slot name="before"></slot>\n        <section class="content">\n            <p v-for="item in contents">\n                {{ item }}\n            </p>\n        </section>\n        <slot name="after"></slot>\n    </div>\n</section>';
n&&n.exports&&(n.exports.template=i),t&&t.default&&(t.default.template=i),n.exports=t["default"]});
;define("hiloan:components/fin-rm/info-card/index",function(e,n,o){"use strict";function t(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var f=e("hiloan:components/fin-rm/info-card/info-card.vue"),i=t(f);
n.default=i.default,o.exports=n["default"]});
;define("hiloan:components/fin-rm/risk-notice/risk-notice.vue",function(t,e,n){"use strict";function o(t){return t&&t.__esModule?t:{"default":t}}Object.defineProperty(e,"__esModule",{value:!0});var i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t
}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c=t("hiloan:node_modules/vue/dist/vue.common"),l=(o(c),t("hiloan:components/fin-rm/rm-input/index")),s=o(l);
e.default={props:{title:{type:String,"default":"风险告知书"},contents:{type:Array,"default":[]},fieldname:{type:String,"default":""},value:{type:String,"default":"1"}},data:function(){return{is:"risk-notice"}
},created:function(){this.compatibleMode="object"===i(this.contents[0])},methods:{},components:{rmInput:s.default}};var a='<section class="fin-{{ is }}">\n    <section class="header">\n        <h2>{{title}}</h2>\n        <div class="logo"></div>\n    </section>\n    <section class="content compatible clearfix" v-if="compatibleMode">\n        <div v-for="content in contents">\n            <h3>{{ content.title }}</h3>\n            <p v-for="detail in content.details">\n                {{ detail }}\n            </p>\n        </div>\n    </section>\n    <section class="content clearfix" v-else="">\n        <p v-for="content in contents">\n            {{$index+1}}. {{content}}\n        </p>\n    </section>\n    <rm-input input-type="hidden" :value="value" :fieldname="fieldname">\n    </rm-input>\n</section>';
n&&n.exports&&(n.exports.template=a),e&&e.default&&(e.default.template=a),n.exports=e["default"]});
;define("hiloan:components/fin-rm/risk-notice/index",function(e,n,t){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var o=e("hiloan:components/fin-rm/risk-notice/risk-notice.vue"),r=i(o);
n.default=r.default,t.exports=n["default"]});
;define("hiloan:components/fin-rm/ocr/ocr.vue",function(t,e,o){"use strict";function r(t){return t&&t.__esModule?t:{"default":t}}Object.defineProperty(e,"__esModule",{value:!0});var a=t("hiloan:node_modules/vue/dist/vue.common"),i=(r(a),t("hiloan:node_modules/query-string/index")),n=r(i),c=t("hiloan:components/fin-rm/take-photo/index"),s=r(c),d=t("hiloan:components/fin-ui/ui-dialog/index"),f=r(d),u=t("hiloan:app/static/config/api"),l=r(u);
e.default={props:{frontUuid:{type:String,"default":""},backUuid:{type:String,"default":""}},data:function(){return{is:"rm-ocr",errShow:!1,errMsg:""}},watch:{frontUuid:function(t){t&&this.backUuid&&this.ocrAction()
},backUuid:function(t){t&&this.frontUuid&&this.ocrAction()}},methods:{clearPhoto:function(t){var e=this,o=this.$root.validation;t=Array.isArray(t)?t:[],t.forEach(function(t){var r=e.$refs[t];r.file="",r.uuid="",o.valid=!1
})},ocrAction:function(){var t=this;l.default.idcardDiscern({stage:n.default.parse(location.search).stage,idcardPhoto:this.$refs.front.imageBase64,idcardPhotoBack:this.$refs.back.imageBase64}).then(function(e){var o=+e.data.idcardFe;
if(1!==o){switch(o){case 2:t.clearPhoto(["front"]),t.errMsg="身份证正面识别失败，请重新上传";break;case 3:t.clearPhoto(["back"]),t.errMsg="身份证反面识别失败，请重新上传";break;case 4:t.clearPhoto(["front","back"]),t.errMsg="身份证照片识别失败，请重新上传";
break;case 5:t.clearPhoto(["front","back"]),t.errMsg="抱歉，您的身份证已过期，请更换身份证后再办理贷款";break;default:t.clearPhoto(["front","back"]),t.errMsg="网络不给力，请稍候重试",t.errShow=!0}t.errShow=!0}}).catch(function(){t.clearPhoto(["front","back"])
})}},components:{takePhoto:s.default,uiDialog:f.default}};var h='<section class="fin-rm-ocr">\n    <p class="tip">\n        <i class="icon icon-info-revert"></i>\n        请使用真实有效的身份证件。<br>\n        过期，翻拍的证件将不会通过审核。\n    </p>\n    <take-photo type="idcardFront" v-fieldset="idcardFront" :uuid.sync="frontUuid" v-ref:front=""></take-photo>\n    <take-photo type="idcardBack" v-fieldset="idcardBack" :uuid.sync="backUuid" v-ref:back=""></take-photo>\n    <ui-dialog :show.sync="errShow" :head="false" type="alert" ok="知道了" @ok="this.errShow = false">\n        <div slot="content">\n            <p v-text="errMsg"></p>\n        </div>\n    </ui-dialog>\n</section>';
o&&o.exports&&(o.exports.template=h),e&&e.default&&(e.default.template=h),o.exports=e["default"]});
;define("hiloan:components/fin-rm/ocr/index",function(e,n,t){"use strict";function o(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var r=e("hiloan:components/fin-rm/ocr/ocr.vue");
Object.defineProperty(n,"default",{enumerable:!0,get:function(){return o(r).default}}),t.exports=n["default"]});
;define("hiloan:components/fin-rm/ocr-license/ocr-license.vue",function(e,n,t){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var r=e("hiloan:node_modules/vue/dist/vue.common"),o=i(r),a=e("hiloan:app/static/config/api"),s=i(a),d=e("hiloan:components/fin-rm/rm-input/index"),u=i(d),l=e("hiloan:components/fin-rm/rm-date-input/index"),c=i(l),p=e("hiloan:components/fin-rm/take-photo-group/index"),m=i(p);
n.default={props:{},data:function(){return{is:"ocr-license",ocrRequestDone:!0,dateRules:{required:{rule:"true",message:"请选择正确的时间"}}}},methods:{recognizeText:function(e){var n=this;s.default.licenseDiscern({image:e},{"x-silent":!1,"x-message":!1}).then(function(e){n.autoFill(e.data)
})},autoFill:function(e){var n=this,t=function(e){return("0"+e).slice(-2)},i=function(i){var r=i.toLowerCase();if(e.hasOwnProperty(i)&&n.$refs[r]){var a=String(e[i]);a&&(n.$refs[r].value=a),"drivinglicenseopendate"!==r&&"drivinglicenseregdate"!==r||!e[i]||o.default.nextTick(function(){var o=new Date(e[i]);
n.$refs[r].text=[o.getFullYear(),t(o.getMonth()+1),t(o.getDate())].join(" / ")})}};for(var r in e)i(r)}},components:{rmInput:u.default,rmDateInput:c.default,takePhotoGroup:m.default},created:function(){var e=this;
this.$root.$on("after-take-photo",function(n){"licenseFront"===n.type&&n.file&&e.recognizeText(n.file)})}};var f='<section class="fin-{{is}}">\n    <take-photo-group group-title="上传机动车行驶证" :items="[\n            {\n                desc:\'行驶证正面照\',\n                type: \'licenseFront\',\n                fieldset: \'licenseFront\'\n            },\n            {\n                desc:\'行驶证反面照\',\n                type: \'licenseBack\',\n                fieldset: \'licenseBack\'\n            }\n        ]">\n    </take-photo-group>\n    <template v-if="ocrRequestDone">\n        <p class="space-title">请合适或补充以下信息</p>\n        <rm-input v-ref:drivinglicensenumber="" type="drivingLicenseNumber" :border-bottom="true" :value="licenseNo"></rm-input>\n        <rm-input v-ref:drivinglicensename="" type="drivingLicenseName" :border-bottom="true"></rm-input>            \n        <rm-input v-ref:drivinglicensebrand="" type="drivingLicenseBrand" :border-bottom="true"></rm-input>\n        <rm-input v-ref:drivinglicensenumber1="" type="drivingLicenseNumber1" :border-bottom="true"></rm-input>            \n        <rm-input v-ref:drivinglicensenumber2="" type="drivingLicenseNumber2" :border-bottom="true"></rm-input>            \n        <rm-date-input tip="注册日期" title="注册日期" placeholder="注册日期" :start-date="new Date(1975, 0, 1)" :default-date="new Date(2000, 5, 15)" :end-date="new Date()" v-ref:drivinglicenseregdate="" fieldname="drivingLicenseRegDate" fieldset="drivingLicenseRegDate" :rules="dateRules">\n        </rm-date-input>\n        <rm-date-input tip="发证日期" title="发证日期" placeholder="发证日期" :start-date="new Date(1975, 0, 1)" :default-date="new Date(2000, 5, 15)" :end-date="new Date()" v-ref:drivinglicenseopendate="" fieldname="drivingLicenseOpenDate" fieldset="drivingLicenseOpenDate" :rules="dateRules">\n        </rm-date-input>\n        <rm-input v-show="false" v-ref:drivinglicenseaddress="" type="drivingLicenseAddress" :border-bottom="true"></rm-input>\n        <rm-input v-show="false" v-ref:drivinglicensefunction="" type="drivingLicenseFunction" :border-bottom="true"></rm-input>\n        <rm-input v-show="false" v-ref:drivinglicensetype="" type="drivingLicenseType" :border-bottom="true"></rm-input>\n    </template>\n</section>';
t&&t.exports&&(t.exports.template=f),n&&n.default&&(n.default.template=f),t.exports=n["default"]});
;define("hiloan:components/fin-rm/ocr-license/index",function(e,n,o){"use strict";function t(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var i=e("hiloan:components/fin-rm/ocr-license/ocr-license.vue"),c=t(i);
n.default=c.default,o.exports=n["default"]});
;define("hiloan:components/fin-rm/submit-skip/submit-skip.vue",function(e,t,i){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var a=e("hiloan:node_modules/vue/dist/vue.common"),u=n(a),o=e("hiloan:node_modules/vue-touch/vue-touch"),s=n(o),d=e("hiloan:node_modules/query-string/index"),l=n(d),r=e("hiloan:app/static/config/api"),p=n(r),f=e("hiloan:components/fin-fg/util/request/redirect"),c=n(f);
u.default.use(s.default),t.default={props:{text:{type:String,"default":"跳过"},pageId:{type:String,required:!0},stage:{type:String},moduleId:{type:String}},data:function(){return{is:"submit-skip"}},methods:{skip:function(){p.default.getNextPage({pageId:this.pageId,moduleId:this.moduleId,stage:this.stage,originalUrl:location.href}).then(function(e){if(e&&e.data&&e.data.url){var t=e.data.url,i={};
-1!==t.indexOf("?")&&(i=l.default.parse(t.slice(t.indexOf("?")))),c.default(t,i)}})}}};var m='<section class="fin-{{ is }}" v-touch:tap.stop="skip">\n    {{ text }}\n</section>';i&&i.exports&&(i.exports.template=m),t&&t.default&&(t.default.template=m),i.exports=t["default"]
});
;define("hiloan:components/fin-rm/submit-skip/index",function(e,t,i){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var u=e("hiloan:components/fin-rm/submit-skip/submit-skip.vue"),s=n(u);
t.default=s.default,i.exports=t["default"]});
;define("hiloan:components/fin-rm/index",function(n,e,o){"use strict";function i(n){return n&&n.__esModule?n:{"default":n}}Object.defineProperty(e,"__esModule",{value:!0});var t=n("hiloan:components/fin-rm/area/index"),a=i(t),d=n("hiloan:components/fin-rm/city-picker/index"),r=i(d),l=n("hiloan:components/fin-rm/contact-info/index"),f=i(l),m=n("hiloan:components/fin-rm/credit-card/index"),c=i(m),p=n("hiloan:components/fin-rm/credit-contract/index"),u=i(p),s=n("hiloan:components/fin-rm/date-input-group/index"),h=i(s),x=n("hiloan:components/fin-rm/group-both/index"),k=i(x),y=n("hiloan:components/fin-rm/group-required/index"),I=i(y),g=n("hiloan:components/fin-rm/handheld-idcard/index"),b=i(g),P=n("hiloan:components/fin-rm/input-base/index"),_=i(P),C=n("hiloan:components/fin-rm/next-step/index"),S=i(C),q=n("hiloan:components/fin-rm/position-type/index"),v=i(q),B=n("hiloan:components/fin-rm/risk-inform/index"),G=i(B),M=n("hiloan:components/fin-rm/rm-input/index"),T=i(M),j=n("hiloan:components/fin-rm/rm-select/index"),D=i(j),L=n("hiloan:components/fin-rm/select-installment/index"),N=i(L),O=n("hiloan:components/fin-rm/select-phone/index"),R=i(O),w=n("hiloan:components/fin-rm/social-info/index"),z=i(w),A=n("hiloan:components/fin-rm/student-type/index"),E=i(A),F=n("hiloan:components/fin-rm/take-photo/index"),H=i(F),J=n("hiloan:components/fin-rm/tel/index"),K=i(J),Q=n("hiloan:components/fin-rm/info-card/index"),U=i(Q),V=n("hiloan:components/fin-rm/risk-notice/index"),W=i(V),X=n("hiloan:components/fin-rm/ocr/index"),Y=i(X),Z=n("hiloan:components/fin-rm/take-photo-group/index"),$=i(Z),ne=n("hiloan:components/fin-rm/bankcard-info/index"),ee=i(ne),oe=n("hiloan:components/fin-rm/rm-date-input/index"),ie=i(oe),te=n("hiloan:components/fin-rm/guarantor/index"),ae=i(te),de=n("hiloan:components/fin-rm/ocr-license/index"),re=i(de),le=n("hiloan:components/fin-rm/house-property/index"),fe=i(le),me=n("hiloan:components/fin-rm/submit-skip/index"),ce=i(me);
e.default={area:a.default,cityPicker:r.default,contactInfo:f.default,creditCard:c.default,creditContract:u.default,dateInputGroup:h.default,groupBoth:k.default,groupRequired:I.default,handheldIdcard:b.default,inputBase:_.default,nextStep:S.default,positionType:v.default,riskInform:G.default,rmInput:T.default,rmSelect:D.default,selectInstallment:N.default,selectPhone:R.default,socialInfo:z.default,studentType:E.default,takePhoto:H.default,tel:K.default,infoCard:U.default,riskNotice:W.default,ocr:Y.default,takePhotoGroup:$.default,bankcardInfo:ee.default,rmDateInput:ie.default,guarantor:ae.default,ocrLicense:re.default,submitSkip:ce.default,houseProperty:fe.default},o.exports=e["default"]
});
;define("hiloan:components/fin-rs/liveness/liveness.vue",function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var a=(e("hiloan:node_modules/es6-promise/dist/es6-promise"),e("hiloan:node_modules/query-string/index")),o=i(a),s=e("hiloan:components/fin-fg/util/native/index"),r=i(s),l=(e("hiloan:components/fin-fg/util/index"),e("hiloan:components/fin-fg/util/ui/index")),c=(e("hiloan:app/static/config/constant"),e("hiloan:app/static/config/api")),d=i(c);
t.default={props:{name:{type:String,"default":"xx",required:!0}},data:function(){return this.livenessParam=JSON.parse(decodeURIComponent(o.default.parse(location.search).liveness)),{is:"liveness",conf:{method_name:"callNativeVoice",startIdentify:{productId:this.livenessParam.productId,serviceType:this.livenessParam.serviceType,actionType:"2",barStyle:"1"}}}
},methods:{startRecord:function(){var e=this,t=o.default.parse(location.search).returnUrl;t=t&&decodeURIComponent(t),r.default.get("getDeviceInfo",{}).then(function(n){var i=n&&n.data&&n.data.appversionname;
if(e.isAvailable(i)){try{d.default.log({type:"liveness_request",req:JSON.stringify(e.conf)},{"x-silent":!0,"x-message":!1})}catch(a){}Agent.invoke("callNativeVoice",e.conf,function(e,n){try{d.default.log({type:"liveness_response",res:JSON.stringify(n)},{"x-silent":!0,"x-message":!1})
}catch(i){}var a=parseInt(n.cnt.errCode,10);0===a?d.default.getNextPage({returnUrl:t,originalUrl:location.href}).then(function(e){location.href=e.data.url}):-204!==a&&l.dialog({type:"alert",title:"提示",content:n.cnt.des}).then(function(e){e.hideUi()
})})}})},isAvailable:function(e){var t=this.getAppConf(),n=t.minVersion,i=t.dialogConf,a=t.okHandler,o=void 0===a?function(e){return e.hideUi()}:a;return-1===this.compareVersion(e,n)?(l.dialog(i).then(o),!1):!0
},getAppConf:function(){var e={BaiduWallet:{minVersion:"3.0.1",dialogConf:{type:"confirm",title:"请升级百度钱包",content:"当前百度钱包为旧版本，无法使用本功能，请下载最新版本!",ok:"下载"},okHandler:function(){location.href="http://dwz.cn/3Bczfc"
}},BaiduApp:{minVersion:"8.3.1",dialogConf:{type:"confirm",title:"请升级手机百度",content:"当前手机百度为旧版本，无法使用本功能，请下载最新版本",ok:"知道了"}},BdMap:{minVersion:"10.1.8",dialogConf:{type:"confirm",title:"请升级百度地图",content:"当前百度地图为旧版本, 无法使用本功能, 请下载最新版本"}},BdCloud:{minVersion:"8.0.0",dialogConf:{type:"confirm",title:"请升级百度网盘",content:"当前百度网盘为旧版本, 无法使用本功能, 请下载最新版本"}},Tieba:{minVersion:"8.8.8.14",dialogConf:{type:"confirm",title:"请升级百度贴吧",content:"当前百度贴吧为旧版本, 无法使用本功能, 请下载最新版本"}},YouQianHua:{minVersion:"2.1.0",dialogConf:{type:"confirm",title:"请升级百度有钱花",content:"当前百度有钱花为旧版本, 无法使用本功能, 请下载最新版本"}},IQiYi:{minVersion:"8.12.0",dialogConf:{type:"confirm",title:"请升级爱奇艺",content:"当前爱奇艺为旧版本, 无法使用本功能, 请下载最新版本"}}},t="",n=navigator.userAgent;
switch(!0){case/BdMap/i.test(n):t="BdMap";break;case/baiduapp|baiduboxapp/i.test(n):t="BaiduApp";break;case/bdcloud/i.test(n):t="BdCloud";break;case/tieba/i.test(n):t="Tieba";break;case/bdyouqianhua/i.test(n):t="YouQianHua";
break;case/iqiyi/i.test(n):t="IQiYi";break;default:t="BaiduWallet"}return e[t]},compareVersion:function(e,t){for(var n=e.split("."),i=t.split("."),a=Math.min(n.length,i.length),o=0;a>o;o++){if(parseInt(n[o],10)>parseInt(i[o],10))return 1;
if(parseInt(n[o],10)<parseInt(i[o],10))return-1}return n.length>i.length?1:n.length<i.length?-1:0}},components:{}};var p='<section class="fin-{{is}}">\n    <div class="txt-box">\n        <h1>人脸录入</h1>\n        <h2>请确保 <font class="highlight">{{name}}</font> 本人操作</h2>\n    </div>\n    <div class="info-img"></div>\n    <p class="tip">平视手机 正对光源</p>\n    <button class="start-record fin-ui-button large" v-el:start-record="" @click="startRecord">\n        开始拍摄\n    </button>\n    <p class="tip-small">录入后，将自动进入后台审核</p>\n</section>';
n&&n.exports&&(n.exports.template=p),t&&t.default&&(t.default.template=p),n.exports=t["default"]});
;define("hiloan:components/fin-rs/liveness/index",function(e,n,s){"use strict";function t(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var i=e("hiloan:components/fin-rs/liveness/liveness.vue"),l=t(i);
n.default=l.default,s.exports=n["default"]});
;define("hiloan:components/fin-rs/refund-bar/refund-bar.vue",function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var o=e("hiloan:node_modules/underscore/underscore"),r=i(o),u=e("hiloan:node_modules/vue/dist/vue.common"),a=i(u),d=e("hiloan:node_modules/vue-touch/vue-touch"),s=i(d),f=e("hiloan:app/static/config/api"),l=i(f);
e("hiloan:components/fin-fg/filter/index");var c=e("hiloan:components/fin-fg/util/index");a.default.use(s.default),t.default={props:{projectStatusList:{type:Array,required:!0,"default":function(){return[]
}},ignoreProjectStatusList:{type:Boolean,"default":!1},operatorName:{type:String,"default":"培训学校"},backUrl:{type:String,required:!0,"default":""}},data:function(){return{is:"refund-bar",refundList:[]}},watch:{projectStatusList:{handler:function(e){if(!this.ignoreProjectStatusList&&e){var t=!1;
[102,104].forEach(function(n){-1!==e.indexOf(n)&&(t=!0)}),t&&this.getRefundList()}},immediate:!0},ignoreProjectStatusList:{handler:function(e){e&&this.getRefundList()},immediate:!0}},methods:{gonnaRefund:function(e){c.redirect(this.refundUrl,r.default.extend(e,{backUrl:encodeURIComponent(this.backUrl)}))
},getRefundList:function(){var e=this;l.default.getRefundList(null,{"x-silent":!0,"x-message":!1}).then(function(t){var n=t.data;n.refundList&&(e.refundUrl=n.refundUrl,e.refundList=n.refundList)})}},components:{}};
var p='<section class="fin-{{is}}">\n    <ul>\n        <li v-for="item in refundList" v-touch:tap.stop="gonnaRefund(item)" transition="fade">\n            <p>{{ operatorName }}正在发起{{ item.paidAmount | formatMoney }}元退款</p>\n            <div>去确认<i class="icon icon-arrow-right"></i></div>\n        </li>\n    </ul>\n</section>';
n&&n.exports&&(n.exports.template=p),t&&t.default&&(t.default.template=p),n.exports=t["default"]});
;define("hiloan:components/fin-rs/refund-bar/index",function(e,n,r){"use strict";function u(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var t=e("hiloan:components/fin-rs/refund-bar/refund-bar.vue"),f=u(t);
n.default=f.default,r.exports=n["default"]});
;define("hiloan:components/fin-ui/ui-step/ui-step.vue",function(t,i,e){"use strict";Object.defineProperty(i,"__esModule",{value:!0}),i.default={is:"ui-step",props:{step:{type:Number,"default":2},state:{type:String,required:!1,"default":" "},label:{type:Array,"default":[]}},data:function(){return{stateFirst:"",textFirst:"",iconFirst:"",stateLast:"",textLast:"",iconLast:"",stateList:"",lineList:""}
},methods:{reload:function(){for(var t={ing:"icon-circle",fail:"icon-radio-off",done:"icon-ok"},i=this.step,e=this.state,s=(this.width,this.label),n=this.label.length,a=[],l=1;n>=l;l++){var d=100/(n-1),o=s[l-1];
a.push(i>=l?{state:l===i?e||"done":"done",line:"done",icon:l===i?t[e]||t.done:t.done,width:2*d,left:d*(l-2),text:o}:{state:"",line:"",icon:t.fail,width:2*d,left:d*(l-2),text:o})}this.stateFirst=a[0].state,this.textFirst=a[0].text,this.iconFirst=a[0].icon,this.stateLast=a[n-1].state,this.textLast=a[n-1].text,this.iconLast=a[n-1].icon,this.stateList=a.slice(1,n-1),this.lineList=a.slice(1)
}},created:function(){this.reload()},watch:{state:function(){this.reload()},step:function(){this.reload()},label:function(){this.reload()}}};var s='<div class="fin-ui-step">\n    <div class="first">\n        <div class="state {{ stateFirst }}">\n            <i class="icon {{ iconFirst }}"></i>\n        </div>\n        <div class="label {{ stateFirst }}">{{ textFirst }}</div>\n    </div>\n    <div class="last">\n        <div class="state {{ stateLast }}">\n            <i class="icon {{ iconLast }}"></i>\n        </div>\n        <div class="label {{ stateLast }}">{{ textLast }}</div>\n    </div>\n    <ul class="line-list flex">\n        <li v-for="(index, item) in lineList">\n            <div class="line {{ item.line }}"></div>\n        </li>\n    </ul>\n    <ul class="state-list">\n        <li v-for="(index, item) in stateList" v-bind:style="{left: item.left + \'%\', width: item.width + \'%\'}">\n            <div class="state {{ item.state }}">\n                <i class="icon {{ item.icon }}"></i>\n            </div>\n            <div class="label {{ item.state }}">{{ item.text }}</div>\n        </li>\n    </ul>\n</div>';
e&&e.exports&&(e.exports.template=s),i&&i.default&&(i.default.template=s),e.exports=i["default"]});
;define("hiloan:components/fin-ui/ui-step/index",function(e,t,u){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var i=e("hiloan:components/fin-ui/ui-step/ui-step.vue"),o=n(i);
t.default=o.default,u.exports=t["default"]});
;define("hiloan:components/fin-rs/result/result.vue",function(t,e,i){"use strict";function n(t){return t&&t.__esModule?t:{"default":t}}Object.defineProperty(e,"__esModule",{value:!0});var a=t("hiloan:node_modules/underscore/underscore"),s=n(a),o=t("hiloan:app/static/config/api"),l=n(o),r=t("hiloan:components/fin-fg/util/index"),u=t("hiloan:components/fin-fg/util/native/getNativeFields"),c=n(u),d=t("hiloan:components/fin-ui/ui-dialog/index"),p=n(d),f=t("hiloan:components/fin-ui/ui-loading/index"),h=n(f),g=t("hiloan:components/fin-ui/ui-step/index"),v=n(g),k=t("hiloan:components/fin-ui/ui-table/index"),m=n(k);
e.default={data:function(){return{is:"result",typeClass:{success:"success",fail:"fail",process:"waiting",warn:"supply"},dialogResult:{okFn:function(){}}}},props:{type:{type:String,required:!0,"default":"process"},title:{type:String,"default":""},info:{type:String,"default":""},supplyTitle:{type:String,"default":"补充信息"},supplies:{type:Array,"default":function(){return[]
}},progressList:{type:Array,"default":function(){return[]}},progressStep:{type:Number,"default":1},progressState:{type:String,"default":""},amount:{type:Array,"default":function(){return[]}},items:{type:Array,"default":function(){return[]
}},actions:{type:Array,"default":function(){return[]}}},components:{uiStep:v.default,uiTable:m.default,uiDialog:p.default,uiLoading:h.default},methods:{getActions:function(t,e){e.fn&&e.fn(),this.$dispatch("action"+(t+1));
var i=e.type,n=e.url,a=e.data,s=void 0===a?{}:a;switch(i){case"reload":location.reload();break;case"jump":n&&r.redirect(n,s);break;case"revoke":this.showDialogRevoke(e);break;case"next":this.next(e);break;
case"pay":this.pay(e);break;case"reapply":this.revoke(e);break;case"qr":Agent.OS.wallet?this.qr(e):this.showDialogNotInSdk();break;case"check":this.check(e);break;case"custom":this.custom(e)}},custom:function(t){var e=this,i={screenWidth:"",screenHeight:"",walletUserAgent:"",imei:"",imsi:"",isBreak:"",simSerialNum:"",localIp:"",clientIP:"",safeSdk:"",wifi:"",explorerUserAgent:"",sourceFlag:""};
c.default(i).then(function(e){var i=s.default.extend({},e,t.param);return l.default[t.api](i)}).then(function(i){0===i.errno&&t.url&&(window.Agent.OS.wallet?1==t.poll&&t.pollApi?e.pollAction(t):r.redirect(t.url,r.query.parse(t.url).search):e.showDialogInvalidCustom())
})},pollAction:function(t){var e=this;if(t.pollApi){var i={"x-silent":!0,"x-message":this.message};return l.default[t.pollApi]({},i).then(function(i){4==i.data.status||7==i.data.status?setTimeout(e.pollAction,1e3,t):r.redirect(t.url,r.query.parse(t.url).search)
})}},next:function(t){var e={pageId:t.pageId,stage:t.stage,originalUrl:location.href};l.default.getNextPage(e).then(function(t){var e=t.data;0===t.errno&&e&&e.url&&r.redirect(e.url,{pageId:e.pageId,stage:e.stage})
})},revoke:function(t){var e=this;l.default.applyCancel(null,{"x-message":!1}).then(function(i){var n=i.data;n&&0!==+n.status?(e.showDialogNetworkAbort(n.msg),e.$dispatch("revokeFailed",n.errorCode)):"function"==typeof t.success?t.success(i):e.next(t)
}).catch(function(i){30001===+i.errno?e.showDialogInvalidOperation(t):e.showDialogNetworkAbort(),e.$dispatch("revokeFailed",i.errno)})},pay:function(t){var e=this,i=t.params,n=void 0===i?{}:i;l.default.paymentEncash(n).then(function(){t.backUrl?r.redirect(t.backUrl):"function"==typeof t.success?t.success():e.next(t)
})},qr:function(t){var e=this;r.native.get("qr",{needScanResult:"1"}).then(function(i){for(var n=r.base64.decode(i.scanResult),a=r.query.parse(n).hostname,s=t.whiteList,o=void 0===s?["baidu.com"]:s,l=!1,u=0;u<o.length;u++)if(-1!==a.indexOf(o[u])){l=!0;
break}l?"function"==typeof t.success?t.success(i):location.href=n:e.showDialogInvalidDomain()})},check:function(t){var e=this;l.default.getProjectExpireInfo().then(function(i){var n=i.data.expireStatus;
1===n?e.showDialogExpired(t):r.redirect(t.checkedUrl,t.data)})},showDialogExpired:function(t){t.success=function(){location.reload()};var e="您未在有效期内使用贷款，<br>请撤销后重新申请";this.dialogResult={type:"alert",show:!0,head:!1,content:e,okText:"撤销申请",okFn:function(){this.dialogResult.show=!1,this.revoke(t)
}}},showDialogRevoke:function(t){var e=this,i=t.title,n=void 0===i?"撤销将终止本次申请":i,a=t.content,s=void 0===a?'您可重新申请该笔贷款，但一天内撤销超过<span class="t-orange">5</span>次当日将不可申请(撤销不收取任何费用)':a;this.dialogResult={type:"prompt",show:!0,head:!0,title:n,content:s,okText:"确认撤销",okFn:function(){e.dialogResult.show=!1,e.revoke(t)
}}},showDialogNetworkAbort:function(t){var e=this,i=t||"网络不畅, 请稍候重试";this.dialogResult={type:"alert",show:!0,head:!0,title:"撤销失败",content:i,okText:"我知道了",okFn:function(){e.dialogResult.show=!1}}},showDialogInvalidOperation:function(t){var e=this;
this.dialogResult={type:"alert",show:!0,head:!0,title:"撤销失败",content:"操作无效",okText:t.backUrl?"回到首页":"我知道了",okFn:function(){t.backUrl?r.redirect(t.backUrl):e.dialogResult.show=!1}}},showDialogNotInSdk:function(){var t=this;
this.dialogResult={type:"alert",show:!0,head:!1,content:"请在百度钱包环境中, 进行扫码操作",okText:"我知道了",okFn:function(){t.dialogResult.show=!1}}},showDialogInvalidDomain:function(){var t=this;this.dialogResult={type:"alert",show:!0,head:!1,content:"请扫描正确的项目二维码",okText:"我知道了",okFn:function(){t.dialogResult.show=!1
}}},showDialogInvalidCustom:function(){var t=this;this.dialogResult={type:"alert",show:!0,head:!1,content:"请下载百度钱包",okText:"我知道了",okFn:function(){t.dialogResult.show=!1}}}},created:function(){document.querySelector("body").style.backgroundColor="#f5f5f5"
}};var y='<article class="fin-result {{ type }}">\n    <div class="result-wrap">\n        <div class="result-head">\n            <!-- 类型 -->\n            <div class="result-type"><i class="icon icon-{{ typeClass[type] }}"></i></div>\n            <div class="title-wrap">\n                <!-- 标题 -->\n                <div v-if="title" class="result-title" :class="{\'middle\': !info}">{{ title }}</div>\n                <!-- 副标题 -->\n                <div v-if="info" class="result-info">{{{ info }}}</div>\n            </div>\n            <!-- 标题后插槽 -->\n            <slot name="after-subtitle"></slot>\n        </div>\n        <!-- 补件信息 -->\n        <div v-if="supplies.length" class="result-supplies">\n            <div class="supplies-box">\n                <p class="title">{{ supplyTitle }}</p>\n                <p v-for="item in supplies">{{ item }}</p>\n            </div>\n        </div>\n        <!-- 进度信息 -->\n        <div class="result-progress" v-if="progressList.length">\n            <ui-step :label="progressList" :step="progressStep" :state="progressState"></ui-step>\n        </div>\n    </div>\n    <div class="result-table-wrap">\n        <!-- table前插槽 -->\n        <slot name="pre-table"></slot>\n        <!-- table 金额展示 -->\n        <div v-if="amount.length" class="result-table-amount">\n            <ui-table dark-color="#222" :dark="[2]" :items="amount">\n            </ui-table>\n            <!-- 金额后插槽 -->\n            <slot name="after-amount"></slot>\n        </div>\n        <!-- table 列表 -->\n        <div v-if="items.length" class="result-table">\n            <ui-table dark-color="#222" :dark="[2]" :items="items">\n            </ui-table>\n        </div>\n        <!-- table后插槽 -->\n        <slot name="after-table"></slot>\n    </div>\n    <!-- 操作 -->\n    <div v-if="actions.length" class="result-actions">\n        <!-- 操作前插槽 -->\n        <slot name="pre-actions"></slot>\n        <!--操作按钮列表-->\n        <div v-for="(index, action) in actions" @click="getActions(index, action)" class="fin-ui-button full {{ action.class }}">\n            {{ action.name }}\n        </div>\n    </div>\n    <!-- 弹窗 -->\n    <ui-dialog v-ref:result-dialog="" :type="dialogResult.type" :show.sync="dialogResult.show" :head="dialogResult.head" :title="dialogResult.title" :content="dialogResult.content" :ok="dialogResult.okText" @ok="dialogResult.okFn">\n    </ui-dialog>\n</article>';
i&&i.exports&&(i.exports.template=y),e&&e.default&&(e.default.template=y),i.exports=e["default"]});
;define("hiloan:components/fin-rs/result/index",function(e,t,n){"use strict";function u(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var l=e("hiloan:components/fin-rs/result/result.vue"),o=u(l);
t.default=o.default,n.exports=t["default"]});
;define("hiloan:components/fin-rs/sms-verify/sms-verify.vue",function(e,n,t){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var o=e("hiloan:node_modules/vue/dist/vue.common"),s=(i(o),e("hiloan:node_modules/underscore/underscore")),r=i(s),a=e("hiloan:node_modules/query-string/index"),d=i(a),u=e("hiloan:app/static/config/api"),l=i(u),c=(e("hiloan:components/fin-fg/util/index"),e("hiloan:app/static/config/constant"),e("hiloan:components/fin-ui/ui-toast/index")),f=i(c),p=e("hiloan:components/fin-rm/input-base/input.vue"),m=i(p);
n.default={props:{phone:{type:String,"default":"123****456",required:!0}},data:function(){return r.default.extend({},{is:"sms-verify",timerText:"发送验证码",verifyCodeError:!1,verifyCodeErrorCss:!1,value:"",verifyCodeDisabled:!1})
},computed:{disabled:function(){return!/\d{6}/g.test(this.value.trim())}},methods:{initVerifyCode:function(){this.verifyCodeErrorCss=!1},sendVerifyCode:function(){l.default.sendCode(),this.timer()},postVerifyCode:function(){var e=this.value.trim(),n=d.default.parse(location.search).returnUrl;
n=n&&decodeURIComponent(n),l.default.verifySMSCode({code:e}).then(function(e){return 0===e.errno?l.default.getNextPage({returnUrl:n,originalUrl:location.href}):void 0}).then(function(e){0===e.errno&&(location.href=e.data.url)
}).catch(function(e){console.log(e.errno)})},timer:function(){var e=this,n=this.$els.sendVerifyCode;n.disabled=!0;var t=60,i=function o(){n.innerHTML="重新获取("+t+"s)",setTimeout(function(){t>0?(t--,o()):(n.innerHTML=e.timerText,n.disabled=!1,clearTimeout(o))
},1e3)};i()}},components:{rmInput:m.default,uiToast:f.default}};var v='<section class="fin-{{is}}">\n    <h3>发送验证码到手机</h3>\n    <p class="phone-number">\n        <span class="number">{{ this.phone.slice(0, 3) }}</span>\n        <span class="secret">····</span>\n        <span class="number">{{ this.phone.slice(-4) }}</span>\n    </p>\n    <div class="input-group">\n        <span :class="{\'error-code\': verifyCodeErrorCss }" class="sms-verify">\n            <rm-input type="verifyCode" :record-input="false" :value.sync="value" @input="initVerifyCode" :maxlength="6">\n            </rm-input>\n        </span>\n        <button class="send-code fin-ui-button large primary" :disabled="verifyCodeDisabled" v-el:send-verify-code="" @click="sendVerifyCode">\n            {{ timerText }}\n        </button>\n    </div>\n    <button class="fin-ui-button full" @click="postVerifyCode" :disabled="disabled"> 确定 </button>\n    <ui-toast content="验证码错误" :show.sync="verifyCodeError">\n    </ui-toast>\n</section>';
t&&t.exports&&(t.exports.template=v),n&&n.default&&(n.default.template=v),t.exports=n["default"]});
;define("hiloan:components/fin-rs/sms-verify/index",function(e,n,s){"use strict";function t(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var f=e("hiloan:components/fin-rs/sms-verify/sms-verify.vue"),i=t(f);
n.default=i.default,s.exports=n["default"]});
;define("hiloan:components/fin-rs/status-bar/status-bar.vue",function(t,e,n){"use strict";function s(t){return t&&t.__esModule?t:{"default":t}}Object.defineProperty(e,"__esModule",{value:!0});{var i=t("hiloan:node_modules/underscore/underscore"),o=s(i),r=t("hiloan:node_modules/vue/dist/vue.common"),c=s(r),a=t("hiloan:node_modules/vue-touch/vue-touch"),l=s(a),p=t("hiloan:app/static/config/api");
s(p)}t("hiloan:components/fin-fg/filter/index");var u=t("hiloan:components/fin-fg/util/format/index"),m=t("hiloan:components/fin-fg/util/index"),d=t("hiloan:components/fin-ui/ui-step/index"),b=s(d);c.default.use(l.default),e.default={props:{projectId:{type:[String,Number],required:!0,"default":""},projectObj:{type:Object,"default":function(){return{}
}},stepLabel:{type:Array,"default":function(){return["额度申请","额度激活","付款成功"]}},jumpUrls:{type:Object,"default":function(){return{continueApplyUrl:"",applyResultUrl:"",approveResultUrl:"",paymentDetailUrl:"",repayDetailUrl:""}
}},cusInfo:{type:Object,"default":function(){return{}}}},data:function(){return{is:"status-bar",step:1,state:"ing",statusButton:""}},computed:{projectStatus:function(){return this.projectObj.projectStatus
},iProjectObj:function(){var t=this.projectObj,e={300:{btnDesc:"继续申请",btnUrl:this.jumpUrls.continueApplyUrl,moneyDesc:"申请总额",moneyAmount:t.projectPrice},301:{btnDesc:"申请中",btnUrl:this.jumpUrls.applyResultUrl,moneyDesc:"申请总额",moneyAmount:t.creditLimit},302:{btnDesc:"激活额度",btnUrl:this.jumpUrls.applyResultUrl,moneyDesc:"申请总额",moneyAmount:t.creditLimit},303:{btnDesc:"申请失败",btnUrl:this.jumpUrls.applyResultUrl,btnClass:"fail",moneyDesc:"申请总额",moneyAmount:t.creditLimit},304:{btnDesc:"已撤销",btnUrl:"",btnClass:"back",moneyDesc:"申请总额",moneyAmount:t.creditLimit,repayHide:!0,btnDisabled:!0},306:{btnDesc:"去补件",btnUrl:this.jumpUrls.applyResultUrl,moneyDesc:"申请总额",moneyAmount:t.creditLimit},307:{btnDesc:"去补件",btnUrl:this.jumpUrls.applyResultUrl,moneyDesc:"申请总额",moneyAmount:t.creditLimit},308:{btnDesc:"审核中...",btnClass:"waiting",btnUrl:this.jumpUrls.applyResultUrl,moneyDesc:"申请总额",moneyAmount:t.creditLimit},201:{btnDesc:"激活中",btnUrl:this.jumpUrls.approveResultUrl,moneyDesc:"申请总额",moneyAmount:t.creditLimit},202:{btnDesc:"去付款",btnUrl:this.jumpUrls.approveResultUrl,moneyDesc:"成功贷款",moneyAmount:t.creditLimit},203:{btnDesc:"激活失败",btnUrl:this.jumpUrls.approveResultUrl,btnClass:"fail",moneyDesc:"申请总额",moneyAmount:t.creditLimit},204:{btnDesc:"去补件",btnUrl:this.jumpUrls.approveResultUrl,moneyDesc:"申请总额",moneyAmount:t.creditLimit},101:{btnDesc:"放款中",btnUrl:this.jumpUrls.repayDetailUrl,moneyDesc:"成功贷款",moneyAmount:t.totalRepay,repayHide:!0},102:{btnDesc:"去还款",btnUrl:this.jumpUrls.repayDetailUrl,moneyDesc:"成功贷款",moneyAmount:t.totalRepay,repayDesc:"当期应还",repayMoney:t.monthRepay,repayDate:"最后还款日 "+u.datetime(t.currentDueDate)},103:{btnDesc:"已还清",btnUrl:this.jumpUrls.repayDetailUrl,btnClass:"over",moneyDesc:"成功贷款",moneyAmount:t.totalRepay,repayHide:!0},104:{btnDesc:"去还款",btnUrl:this.jumpUrls.repayDetailUrl,btnClass:"red",moneyDesc:"成功贷款",moneyAmount:t.totalRepay,repayDesc:"逾期金额",repayMoney:t.overdueTotal,repayDate:"已逾期"},105:{btnDesc:"已撤销",btnUrl:"",btnClass:"back",btnDisabled:!0,moneyDisabled:!0,moneyDesc:"",moneyAmount:"",repayHide:!0},106:{btnDesc:"支付中",btnDisabled:!0,moneyDesc:"申请总额",moneyAmount:t.creditLimit,repayHide:!0}};
return t.monthRepay||(e[102].repayHide=!0),t.cusInfo=o.default.extend({stepInfo:{}},e[this.projectStatus],this.cusInfo[this.projectStatus]),t}},created:function(){this.showStep()},methods:{jumpNext:function(){if(this.projectObj.cusInfo.btnDisabled)return!1;
var t=this.projectObj.cusInfo.jumpFn;if(!o.default.isFunction(t)||t.call(this)){var e={};e.projectId=this.projectId,this.projectObj.tid&&(e.tid=this.projectObj.tid),this.projectObj.ccid&&(e.ccid=this.projectObj.ccid),m.redirect(this.projectObj.cusInfo.btnUrl,e)
}},showStep:function(){var t=this.iProjectObj.cusInfo.stepInfo;if(!o.default.isEmpty(t))return this.step=t.step,void(this.state=t.state);var e=this.iProjectObj.projectStatus,n=void 0,s=void 0;switch(e){case 302:n=2,s="fail";
break;case 201:case 203:case 204:n=2,s="ing";break;case 202:n=3,s="fail";break;default:n=1,s="ing"}this.step=n,this.state=s}},components:{filter:{template:".fin-status-bar",props:["timestamp","money"]},uiStep:b.default}};
var j='<section class="fin-{{is}}">\n    <div class="content status-{{ iProjectObj.projectStatus }}">\n        <div class="project-info">\n            <div class="left">\n                <div class="project-title">\n                    {{ iProjectObj.projectName }}\n                </div>\n                <div class="project-price" v-if="!iProjectObj.cusInfo.moneyDisabled">\n                    <span class="txt">{{ iProjectObj.cusInfo.moneyDesc }}</span>\n                    <span :class="[\'price\', iProjectObj.projectStatus === 104 ? \'red\': \'\']" v-if="iProjectObj.cusInfo.moneyAmount">\n                        {{ iProjectObj.cusInfo.moneyAmount | formatMoney \'cn\' }}\n                    </span>\n                    <slot name="left_slot"></slot>\n                </div>\n            </div>\n            <div class="right"> \n                <div class="status-button" :class="[iProjectObj.cusInfo.btnClass, iProjectObj.cusInfo.btnDisabled ? \'disabled\' : \'\']" v-touch:tap="jumpNext">\n                    <i :class="[\'icon\', \'icon-\' + iProjectObj.cusInfo.btnClass]"></i>\n                    {{ iProjectObj.cusInfo.btnDesc }}\n                    <i class="icon icon-arrow-right"></i>\n                </div>\n            </div>\n        </div>\n        <div class="line" v-if="!iProjectObj.cusInfo.repayHide"></div>\n        <template v-if="!iProjectObj.cusInfo.repayHide">\n            <div class="repayment-info" v-if="iProjectObj.tranStatus > 0">\n                 <div v-if="iProjectObj.cusInfo.btnDisabled">\n                    <span class="left"><i class="icon icon-info"></i>&nbsp;\n                        {{iProjectObj.cusInfo.repayTip || \'还款功能暂未开放,敬请期待\'}}\n                    </span>\n                 </div>\n                 <div v-else="">\n                    <span class="left">\n                        <em>{{ iProjectObj.cusInfo.repayDesc }}</em>&nbsp;\n                        <em>{{ iProjectObj.cusInfo.repayMoney | formatMoney \'cn\' }}</em>\n                    </span>\n                    <span class="right" :class="{red: projectStatus === 104}">\n                        <em>{{ iProjectObj.cusInfo.repayDate }}</em>\n                    </span>\n                 </div>\n            </div>\n            <div class="project-step" v-else="">\n                <ui-step :label="stepLabel" :step="step" :state="state"></ui-step>\n            </div>\n        </template>\n        <div class="credit-expire" v-if="iProjectObj.cusInfo.showCreditExpire">\n            <i class="icon icon-info"></i>\n            <span>\n                {{ iProjectObj.cusInfo.creditExpireDesc\n                    || \'您申请的额度有效期至\' + iProjectObj.creditExpire + \'，请尽快完成放款。\' }}\n            </span>\n        </div>\n    </div>\n</section>';
n&&n.exports&&(n.exports.template=j),e&&e.default&&(e.default.template=j),n.exports=e["default"]});
;define("hiloan:components/fin-rs/status-bar/index",function(e,t,n){"use strict";function s(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var u=e("hiloan:components/fin-rs/status-bar/status-bar.vue"),a=s(u);
t.default=a.default,n.exports=t["default"]});
;define("hiloan:components/fin-ui/ui-img/ui-img.vue",function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default={props:{src:{type:String},smallSrc:{type:String,"default":function(){return"data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVQImWNgYGBgAAAABQABh6FO1AAAAABJRU5ErkJggg=="
}},width:{type:Number,"default":0},height:{type:Number,"default":0},alt:{type:String,"default":""}},data:function(){return{imageSrc:this.smallSrc,is:"ui-img"}},ready:function(){var t=this,e=new Image;e.onerror=e.onabort=function(){t.src=t.smallSrc
},e.onload=function(){t.imageSrc=t.src,t.width=e.width,t.height=e.height,e.onerror=e.onabort=null,t.$dispatch("load",t)},e.src=this.src}};var r='<img class="fin-{{ is }}" alt="{{ alt }}" :src="imageSrc" width="{{ width }}" height="{{ height }}">';
i&&i.exports&&(i.exports.template=r),e&&e.default&&(e.default.template=r),i.exports=e["default"]});
;define("hiloan:components/fin-ui/ui-img/index",function(e,i,u){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(i,"__esModule",{value:!0});var t=e("hiloan:components/fin-ui/ui-img/ui-img.vue"),o=n(t);
i.default=o.default,u.exports=i["default"]});
;define("hiloan:components/fin-ui/ui-popup/ui-popup.vue",function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var o=e("hiloan:node_modules/vue/dist/vue.common"),s=i(o),l=e("hiloan:node_modules/vue-touch/vue-touch"),c=i(l),u=e("hiloan:components/fin-ui/ui-mask/index"),a=i(u);
s.default.use(c.default),t.default={props:{show:{type:Boolean,"default":!1},subtitle:{type:String,"default":""},skin:{type:String,"default":""},needBtn:{type:Boolean,"default":!1},disabled:{type:Boolean,"default":!0},btn:{type:String,"default":"我已阅读并同意本协议"}},data:function(){return{is:"ui-popup",iHander:-1}
},methods:{closeUi:function(){this.show=!1},confirm:function(){this.disabled||(this.closeUi(),this.checked=!0,this.value="1",this.$dispatch("confirm",this))},showUi:function(){this.show=!0},bindScroll:function(){var e=this.$els.content,t=this;
e.onscroll=function(){var n=e.scrollHeight,i=e.scrollTop,o=e.offsetHeight;o+i>=n-10&&(t.disabled=!1)}},preventTouchMove:function(e,t){return e?!1:t.preventDefault()}},watch:{show:function(){var e=this;
clearTimeout(this.iHander),this.iHander=setTimeout(function(){e.disabled=!1},12e3)}},ready:function(){this.bindScroll()},components:{uiMask:a.default}};var d='<section class="fin-{{ is }} {{ skin }}">\n    <section v-show.sync="show">\n        <ui-mask @click="closeUi" :show.sync="show">\n        </ui-mask>\n        <div class="pop" :class="{\'show-select\':show}">\n            <div class="close">\n                <i class="icon icon-close" @click="closeUi">\n                </i>\n            </div>\n            <h2 class="title" @touchmove="preventTouchMove(false, $event)">\n                {{ subtitle }}\n            </h2>\n            <div class="content" :class="{\'with-btn\':needBtn}" id="content" @touchmove="preventTouchMove(true, $event)" v-el:content="">\n                <slot>\n                </slot>\n            </div>\n            <div class="fixed-button" @touchmove="preventTouchMove(false, $event)" v-if="needBtn">\n                <input class="fin-ui-button full" type="button" :disabled.sync="disabled" value="{{btn}}" @click="confirm">\n            </div>\n        </div>\n    </section>\n</section>';
n&&n.exports&&(n.exports.template=d),t&&t.default&&(t.default.template=d),n.exports=t["default"]});
;define("hiloan:components/fin-ui/ui-popup/index",function(e,u,n){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(u,"__esModule",{value:!0});var o=e("hiloan:components/fin-ui/ui-popup/ui-popup.vue"),t=i(o);
u.default=t.default,n.exports=u["default"]});
;define("hiloan:components/fin-ui/ui-progress/ui-progress.vue",function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={props:{current:{type:Number,required:!0,"default":function(){return 0
}},title:{type:Array,required:!0,"default":function(){return[]}}},data:function(){return{is:"ui-progress",progressItems:[],percent:"33.33%"}},created:function(){var e=this;this.percent=1/this.title.length*100+"%",this.title.forEach(function(t,r){var s={};
s.className=r<e.current?"before":r===e.current?"current":"after",s.text=t,e.progressItems.push(s)})}};var s='<div class="fin-ui-progress">\n    <ul class="progress-title">\n        <li v-for="(index, item) in progressItems" :class="item.className" :style="{width: percent}">\n            <span>\n                {{item.text}}\n            </span>\n        </li>\n    </ul>\n    <ul class="progress-line">\n        <li v-for="(index, item) in progressItems" :class="item.className" :style="{width: percent}">\n        </li>\n    </ul>\n</div>';
r&&r.exports&&(r.exports.template=s),t&&t.default&&(t.default.template=s),r.exports=t["default"]});
;define("hiloan:components/fin-ui/ui-progress/index",function(e,u,n){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(u,"__esModule",{value:!0});var o=e("hiloan:components/fin-ui/ui-progress/ui-progress.vue"),t=i(o);
u.default=t.default,n.exports=u["default"]});
;define("hiloan:components/fin-ui/ui-radio-input/radio.vue",function(e,t,i){"use strict";function a(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var l=e("hiloan:node_modules/vue/dist/vue.common"),n=a(l),s=e("hiloan:node_modules/vue-touch/vue-touch"),u=a(s),d=e("hiloan:components/fin-fg/util/index"),o=d.store.local;
n.default.use(u.default),t.default={props:{label:{type:String},list:{type:Array},fieldname:{type:String,"default":""},value:{type:String,"default":""},rules:{type:Object,"default":{require:{rule:!0,message:""}}}},data:function(){return{is:"ui-input-radio-sub"}
},methods:{selected:function(e){this.value=this.list[e].value,this.$dispatch("selected",this.value,this)}},watch:{value:function(e){o.setItem(this.fieldname,e)}},ready:function(){var e=o.getItem(this.fieldname);
e&&e.trim()&&(this.value=e)}};var r='<div class="fin-{{ is }}">\n    <validator name="validation">\n        <div class="radio-type">\n            <label>{{ label }}</label>\n            <span v-for="item in list" v-touch:tap.stop="selected($index)" class="radio-item">\n                <i class="icon" :class="[item.value===value ? \'icon-radio\' : \'icon-radio-off\']"></i>\n                <span>{{ item.showName }}</span>\n            </span>\n        </div>\n        <input type="hidden" v-model="value" v-validate:value="rules" v-fieldname="fieldname">\n    </validator>\n</div>';
i&&i.exports&&(i.exports.template=r),t&&t.default&&(t.default.template=r),i.exports=t["default"]});
;define("hiloan:components/fin-ui/ui-radio-input/ui-radio-input.vue",function(e,t,u){"use strict";function o(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var n=e("hiloan:node_modules/vue/dist/vue.common"),i=o(n),a=e("hiloan:components/fin-rm/input-base/index"),l=o(a),r=e("hiloan:node_modules/vue-validator/dist/vue-validator.common"),d=o(r),s=e("hiloan:node_modules/vue-validator-manage/dist/vue-validator-manage.common"),f=o(s),p=e("hiloan:components/fin-fg/util/index"),m=e("hiloan:components/fin-ui/ui-radio-input/radio.vue"),c=o(m);
i.default.use(d.default),i.default.use(f.default);p.validator.custom.empty;t.default={props:{showInput:{type:Boolean,"default":!0},radioLabel:{type:String,"default":""},title:{type:String,"default":""},placeholder:{type:String,"default":""},border:{type:Boolean,"default":!1},borderTop:{type:Boolean,"default":!1},borderBottom:{type:Boolean,"default":!0},list:{type:Array,"default":function(){return[]
}},values:{type:Array,"default":function(){return["",""]}},fieldnames:{type:Array,"default":function(){return[]}},fieldsets:{type:Array,"default":function(){return["",""]}},radioRules:{type:Object,"default":function(){return{required:{rule:!0,message:""}}
}},inputRules:{type:Object,"default":function(){return{required:{rule:!0,message:"请填写"}}}}},data:function(){return{is:"ui-radio-input"}},computed:{fieldsets0:function(){return this.fieldsets[0]}},components:{rmInput:l.default,radio:c.default},created:function(){var e=this;
this.values[0]||this.list.some(function(t){return t.selected===!0?(e.values[0]=t.value,!0):void 0})}};var v='<div class="fin-{{ is }}">\n    <radio :label="radioLabel" :list="list" :value.sync="values[0]" :fieldname="fieldnames[0]" v-fieldset="fieldsets0" :rules="radioRules">\n    </radio>\n    <rm-input v-show="showInput" :title="title" :border="border" :border-top="borderTop" :border-bottom="borderBottom" :placeholder="placeholder" :value.sync="values[1]" :fieldname="fieldnames[1]" :fieldset="fieldsets[1]" :rules="inputRules">\n    </rm-input>\n</div>';
u&&u.exports&&(u.exports.template=v),t&&t.default&&(t.default.template=v),u.exports=t["default"]});
;define("hiloan:components/fin-ui/ui-radio-input/index",function(e,i,u){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(i,"__esModule",{value:!0});var t=e("hiloan:components/fin-ui/ui-radio-input/ui-radio-input.vue"),o=n(t);
i.default=o.default,u.exports=i["default"]});
;define("hiloan:components/fin-ui/ui-tip/ui-tip.vue",function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default={props:{show:{type:Boolean,"default":!1},content:{type:String,"default":"This is tip!"}},data:function(){return{is:"ui-tip"}
},methods:{showUi:function(){this.show=!0},hideUi:function(){this.show=!1}}};var i='<section class="fin-{{ is }}" v-show="show" transition="fade-in">\n    <slot name="content">{{{ content }}}</slot>\n    <span class="close">\n        <b class="icon icon-close" @click="hideUi"></b>\n    </span>\n</section>';
n&&n.exports&&(n.exports.template=i),e&&e.default&&(e.default.template=i),n.exports=e["default"]});
;define("hiloan:components/fin-ui/ui-tip/index",function(e,i,t){"use strict";function u(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(i,"__esModule",{value:!0});var n=e("hiloan:components/fin-ui/ui-tip/ui-tip.vue"),o=u(n);
i.default=o.default,t.exports=i["default"]});