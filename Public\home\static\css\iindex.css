.clear {
    clear:both;
    
    height: 0;
    line-height: 0;
    font-size: 0;
}

#jk_lb span
{
	color: #a26c00;
}

.jk-box
{
	background-color: #fff;
	padding-top: 20px;
	padding-bottom: 20px;
}

#jkje
{
	font-size: 250%;
	color: rgb(248,128,31);
	text-align: center;
	font-weight: 600;
	line-height: 1;
}

.boxes
{
	text-align: center;
}

.box-1,.box-2
{
	background: url('../image/line.jpg') bottom right no-repeat !important;
}

.boxes h3
{
	font-size: 110%;
	margin: 1rem 0 0 0;
}

.boxes small
{
	color: #777;
}



.title_item
{
	margin-left: 1rem;
	margin-bottom: 1rem;
}

#jksj_group .am-btn-default
{
	
	background: none;
	color: rgba(39, 37, 40, 0.7);
	border-radius: 1000px;
	border:none;

}


#jksj_group .am-btn-default:hover
{
	background:rgba(39, 37, 40, 0.9);
	color: #ffffff;
	border:none;
}


#jksj_group .am-btn-default:focus
{
	background:rgba(39, 37, 40, 1);
	color: #ffffff;
	border:none;
}

.am-btn.am-active
{
	
	box-shadow: none;
}

.jk_time_add
{
	background:rgba(39, 37, 40, 1) !important;
	color: #ffffff !important;
}

.sjkd
{
	display: flex;
	align-items: center;
	border-right: solid 1px #9c9c9c;

}


.sjkd_line:nth-of-type(3n+1)
{
	
	height:17px;
	border-left: solid 1px #9c9c9c;
}
.sjkd_line
{
	
	width: 100%;
	height:10px;
	
	border-left: solid 1px #BBBBBB;
}

.sjkd_v
{
	display: flex;
	align-items: center;
}
.sjkd_value
{
	width: 100%;
	
}
.sjkd_value .sjkd_value_l
{
	margin-left: -14px;
}
.sjkd_value .sjkd_value_r
{
	float: right;
	margin-right: -14px;
}

button
{
	width: 100%;
	outline: none !important;
	border:none !important;
}

button:hover
{
	background: #a26c00;
	color: #ffffff !important;
}











input{
	background-color:#ececec;
	margin: 0 auto;
}

input.ne-range[type=range]{
	width: 100%;
	height: 8px;
	border-radius: 8px;
	margin: .8em auto;
	padding: 0;
	cursor: pointer;
	border: 0;
	background-image: linear-gradient(90deg, #44dcf9 0%, #2ca1fa 100%);
	background-repeat: no-repeat;
	background-size: 0% 100%;
}

.ne-range_thumb,
input.ne-range[type=range]::-webkit-slider-thumb {
  width: 2em;
  height: 2em;
  border-radius: 50%;
  border: 0.5em double #259ef8;
  background-color: #259ef8 !important;
  box-shadow: 1px 2px 4px rgba(0, 0, 0, 0.5);
  -webkit-transition: border-color 0.15s, background-color 0.15s;
  transition: border-color 0.15s, background-color 0.15s;
  cursor: pointer;
  background-clip: padding-box;
  box-sizing: border-box;
}
.ne-range_thumb:active,
input.ne-range[type=range]::-webkit-slider-runnable-track {

  height: 8px;
  border-radius: 8px;
  margin: -1.5em -1em 0 -1em;
  padding: 0;
  cursor: pointer;
  border: 0;
  background-image: linear-gradient(32deg, #1E1E20 0%, #454851 100%);
  background-repeat: no-repeat;
  background-size: 0% 100%;
}


.ne-range_track > span {
  display: block;
  width: 0%;
  height: 100%;
  background-color: #40c35f !important;
}
.ne-range_tips {
  position: absolute;
  margin-top: -2em;
  width: 6em;
  text-align: center;
  margin-left: -3em;
}
.ne-range_thumb > .ne-range_tips {
  margin-left: -2.15em;
}
.ne-range_tips > span {
  position: relative;
  display: inline-block;
  padding: 0 3px;
  min-width: 1.2em;
  color: white;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 3px;
  text-align: center;
}
.ne-range_tips > span::after {
  content: '';
  position: absolute;
  left: 50%;
  bottom: -0.25em;
  margin-left: -0.3em;
  border: 0.3em solid rgba(0, 0, 0, 0.5);
  border-right-color: transparent;
  border-left-color: transparent;
  border-bottom: 0;
}

input.ne-range[type=range] {
  position: relative;
  outline: 0;
  -webkit-appearance: none !important;
}
input.ne-range[type=range]::-webkit-slider-thumb {
  -webkit-appearance: none !important;
}

.ne-range {
  display: inline-block;
  position: relative;
  width: 100%;
  font-size: 1em;
}
.ne-range_thumb {
  position: absolute !important;
  top: 0;
  margin-left: -0.85em;
}
.ne-range_thumb.ondrag > .ne-range_tips {
  visibility: visible;
}





.no_attestation
{
    position: relative;
    padding-bottom: 20px;
    background: url(../image/top_bg_v80.png) right bottom no-repeat, linear-gradient(180deg, rgb(255,225,108) 0%, rgb(252,212,54) 100%);
}

.top
{
    position: relative;
}

.top_img
{
    top: 0;
    right: 0;
    position: absolute;
    width: 100%;
    height: 50%;
    background: url(../image/top_bg_v80.png) right bottom no-repeat, linear-gradient(180deg, rgb(255,225,108) 0%, rgb(252,212,54) 100%);

}

.ts
{
    margin: 10px auto;
    border-radius: 10px;
    width: calc(100% - 30px);
    color: #ffffff;
}

.ts_1
{
    background: url(../image/ts_1.png) no-repeat;
    background-size: 100% 100%;
}

.ts_2
{
    background: url(../image/ts_2.png) no-repeat;
    background-size: 100% 100%;
    vertical-align:bottom;
}

.ts_box
{

    height: 100%;
    padding: 30px 0 0 20px;
    position: relative;
}

.number_style
{
    font-size: 35px;
}

.website
{
    
}

.website_name
{
    letter-spacing: 1px;
}

.website_title
{
    letter-spacing: 5px;
    font-size: 70%;
}

.quota_box
{
    width: 90%;
    margin: 0 auto;
    background: #ffffff;
    border-radius: 5px;
    padding: 10px;
    box-shadow:0px 0px 5px rgba(70, 70, 70, 0.45);
}

.other_txt
{
    color: rgb(131, 118, 61);
    font-size: 15px;
}

.quota
{
    font-size: 400%;
    line-height: 125%;
    color: rgb(39, 37, 40);
}

.quota_title_1
{
    color: #6b6b6b;
}

.quota_title_2
{
    letter-spacing: 5px;
    font-size: 130%;
    font-weight: bold;
}

.explain
{
    width: 90%;
    margin: 30px auto;
}

.no_attestation button
{
    background: rgb(39, 37, 40);
    color: #ffffff;
    font-size: 20px;
    border-radius: 1000px;
    letter-spacing: 1px;
}

#q-button,#index-button,#qm-button
{
    background: #fddf65;
    color: #333333;
    font-size: 20px;
    border-radius: 1000px;
}


.no_attestation button:hover
{
    background: rgb(39, 37, 40);
    color: #ffffff !important;
}

#q-button:hover
{
    background: #fddf65;
    color: #333333;
    font-size: 20px;
    border-radius: 1000px;
}

#index-button:hover
{
    background: #fddf65;
    color: #333333;
    font-size: 20px;
    border-radius: 1000px;
}

#edit
{
    padding: 10px 15px 0 5px;
}

.topline
{
    width: 95%;
    background: #929292;
    height: 10px;
    border-radius: 10000px;
    margin: 20px auto 0;
    box-shadow: inset 0 0 10px black;
}

.sq_box
{
    width: 92%;
    background: #ffffff;
    margin: -5px auto 0;
    padding: 10px 0;
    box-shadow: 0px 6px 7px 0px rgba(0, 0, 0, 0.24);
}

.bottomline
{
    width: 92%;
    height: 10px;
    margin: -1px auto 0px;
    background-image: url("../image/bottom_line.png");
    background-size: auto 100%;
}


.rll_number
{
    color: #f01f1f;
    font-size: 30px;
}

.jksj_number,.jkje_number
{
    font-size: 25px;
}

.rll_symbol
{
    color: #f01f1f;
    font-size: 10px;
}

.jksj_symbol,.jkje_symbol
{
    font-size: 10px;
    color: #c2c2c2;
}

.p_u_info
{
    width: 90%;
    padding: 10px 0;
}

.p_u_info div:nth-of-type(1)
{
    font-weight: bold;
    color: rgba(0, 0, 0, 0.45);
}

.p_u_info div:nth-of-type(2)
{
    text-align: right;
}

.no_box
{
    width: 100%;
    text-align: center;
}

.no_box img
{
    width: 60%;
}

.no_box small
{
    font-size: 75%;
}


.cop_box
{
    background-image: url(../image/coupon.png);
    width: 300px;
    height: 475px;
    background-size: 100%;
    background-repeat: no-repeat;
    margin: 0 auto;
    position: relative;
}

.cop_txt
{
    position: absolute;
    width: 80%;
    height: 150px;
    margin: 10%;
    
    top: 170px;
    color: #ffffff;
    text-shadow: 0 2px 5px #ff7800;
}

.cop_title
{

}

.cop_number
{
    text-align: right;
    font-size: 450%;
    line-height: 100%;
    font-weight: bolder;
}

.cop_number_txt
{
    top: 35px;
    font-weight: bold;
    font-size: 125%;
}

.cop_btn
{
    background: #ffa407;
    position: absolute;
    bottom: 25px;
    width: 80%;
    padding: 10px;
    color: #ffffff;
    font-weight: bold;
    letter-spacing: 2px;
    font-size: 20px;
    margin: 0 auto;
    left: 0;
    right: 0;
    border-radius: 1000px;
    box-shadow: 0 5px 5px #ffa200;
}

.cop_btn:hover
{
    box-shadow: 0 0 5px #ffa200;
}