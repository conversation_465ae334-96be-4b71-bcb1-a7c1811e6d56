@charset "utf-8";
/* CSS Document */

body{ background:#ececec;}

.top{ height:3em; position:fixed; top:0; left:0; width:100%; z-index:10;}
.local{ background:rgba(255,255,255,0.7); display:block; height:2.163em; border-radius:4px; line-height:2.163em; margin-top:0.571em; font-size:0.857em;}
.loc_d{ color:#666; margin:0 0.143em 0 0.571em;}
.local .glyphicon{ color:#888; float:right; margin-right:0.571em; margin-top:0.429em;}
.search{ width:2.163em; height:2.163em;background:rgba(255,255,255,0.7); border-radius:50%; display:block; font-size:0.857em; line-height:2.163em; text-align:center; float:right;margin-top:0.571em;}
.search span{ color:#666;}
.top .col-xs-1{ padding:0 6px;}
.top .col-xs-9{ padding-right:0; padding-left:0;}

.hot{ background:#fff; margin:1.143em 0; width:100%; padding-bottom:1.143em;}
.title{ overflow:hidden; margin:1em auto; width:12.627em;}
.title *{ float:left;}
.title h4{ margin:0 0.571em; color:#1798f2;}
.title hr{ width:2.857em; height:1px; background:#9dbee3; margin:0.714em 0;}
.hot .col-xs-5{ padding-right:0;}
.hot .col-xs-7{ padding-left:0;}
.sp{ display:block; border-right:1px solid #e1e1e1; padding-right:0.714em;}
.sp img{ width:107px; height:107px; margin:0 auto 0.571em; display:block;}
.sp p,.sp_1 p{ font-size:0.857em; color:#333; height:2.857em; overflow:hidden;}
.sp span,.sp_1 span{ color:#fe6e12; font-size:0.857em; display:block; font-family:'微软雅黑';}
.sp span em,.sp_1 span em{ font-style:normal!important; font-size:1.667em;}
.sp_1 img{ width:70px; height:70px; margin:0 0.714em; float:left;}
.sp_1{ overflow:hidden; border-bottom:1px solid #ececec; padding-bottom:1em;}
.sp_1 p,.sp_1 span{ text-align:right;}

.nav_s{ background:#fff; height:2.786em; width:100%; overflow:hidden; position:relative;}
.pos{ position:fixed; left:0; top:3em; border-bottom:1px solid #ececec; z-index:18; width:100%; background:#fff;}
.scroll a{ font-size:1.143em; display:block; color:#555; line-height:2.241em;margin:0 1em; float:left;}
.scroll a.currer{ color:#1798f2; border-bottom:2px solid #1798f2;}

.product{ background:#fff; margin-top:1.423em; margin-bottom:5.571em;}
.sp_pr{ padding:1em; border-bottom:1px solid #ececec; overflow:hidden;}
.sp_pr img{ width:60px; height:60px; margin-right:1em; float:left;border-radius: 50%;}
.text_p{ float:left;}
.text_p p{ font-size:1em; color:#272727; margin-bottom:0;}

.text_p p span{background:#00c451;color:#fff;padding:0 5px;}

.xing{ overflow:hidden;}
.xing span{ font-size:0.857em; color:#fe6e12; margin-right:0.286em;}
.yue{ font-size:0.857em; color:#888; display:block; margin-top:0.286em;}
.yue em{ color:#333; font-style:normal; margin-left:0.366em;}
.jul{ float:right; font-size:1em; color:#1798f2; margin-top:2em;}
.em_s{ margin-right:0.286em;}

.footer{ background:#fff; border-top:1px solid #ccc; height:3.857em; position:fixed; bottom:0; left:0; z-index:24; width:100%;}
.footer .col-xs-3{ margin:0; padding:0;}
.dao{ display:block; color:#7b7b7b; text-align:center; text-decoration:none;}
.dao span{ display:block; font-size:0.857em; text-align:center;}
.dao i{ display:block; font-size:1.571em; color:#7b7b7b; margin-top:2px; text-align:center;}

/*便民服务*/
.top_c{height:3em; position:fixed; top:0; left:0; width:100%; z-index:20; background:#1798f2; border-bottom:1px solid #ccc;}
.ss_c{ overflow:hidden; background:#f1f1f1; border-radius:4px; height:2em; margin-top:0.5em;}
.ss_c input{ float:left; width:89%; border:0; background:none; height:2em; padding-left:0.714em;}
.ss_c a{ float:right; font-size:1.286em; color:#333; line-height:1.5em; margin-right:0.571em;}

.lie{ margin-top:3em; width:100%; overflow:hidden;}
.left_c{ float:left; width:30%; position:relative;}
.left_c ul,.left_c li,.right_c ul,.right_c li{ list-style:none; margin:0; padding:0;}
.left_c ul{ width:100%; height:auto;}
.left_c li{ width:100%; height:2.571em; font-size:1em; line-height:2.571em; text-align:center; color:#484848; border-bottom:1px solid #d7d7d7;}
.left_c li a{ color:#484848;}
.right_c{ background:#fff; width:70%; float:left; position:relative;}
.right_c ul{ padding:0 1em; height:auto; width:100%;}
.right_c li{ border-bottom:1px  solid #ccc; margin-top:1em; padding-bottom:0.857em;}
.right_c li p{ font-size:1.143em; color:#333; font-weight:600; margin-bottom:0.286em;}
.right_c li p span{ font-size:0.825em; color:#666; margin-left:0.714em; font-weight:normal;}
.right_c li p span em{ font-style:normal; margin-left:1em;}
.jian{ font-size:0.857em; color:#999;}

/*钱包*/
.titi{ font-size:1.286em; color:#fff; line-height:2.3em; margin:0; text-align:center;}
.tou{ background:#1798f2; width:100%; margin-top:3em; padding:2.429em 0 1.286em;}
.tou span{ font-size:0.857em; text-align:center; color:#f5f5f5; display:block; margin-bottom:0.286em;}
.tou p{ text-align:center; color:#fff; font-size:2.143em;}

.tou2{height:50px;line-height:50px;background:#1589d9 url(../images/1.png) center center no-repeat;text-align:center;}
.tou2 a{  color:#fff; }


.lie_b{ background:#fff; margin-top:1em;}
.one{ border-bottom:1px solid #ececec;}
.biao{ font-size:1.071em; height:2.414em; line-height:2.414em; color:#333; margin:0;}
.biao span{ color:#000; font-size:1.2em; margin-right:0.625em;}
.biao i{ float:right; color:#888; font-size:0.8em;}
.biao b{float:right; color:#4e4e4e;font-size:1em;font-weight:100;}
.biao input{width: 15px;height: 30px;float: right;}


.remember { margin-top: 15px; }
.remember input[type=checkbox] { vertical-align: middle; margin:0 5px 0 0; }
.remember span { color: #999;font-size:0.8em; }

/*个人中心*/
.zhang{ background:url(../images/bj.jpg) no-repeat; background-size:cover; width:100%; overflow:hidden;}
.xiao{ line-height:2.143em; height:2.143em;}
.xiao span{ float:right; margin-left:0.857em;font-size:1.286em; color:#fff;}
.xiao .icon-xiaoxi{ position:relative;}
.xiao .icon-xiaoxi i{ display:block; position:absolute; top:4px; right:-4px; width:6px; height:6px; background:#fff; border-radius:50%;}
.tou_x{ overflow:hidden; margin-bottom:0.714em;}
.tou_x .icon-touxiang{ font-size:4.286em; color:#ececec; float:left;}
.yong{ float:left; margin-left:1em; margin-top:1.143em;}
.yong p{ font-size:1.256em; color:#fff; font-weight:600; margin-bottom:0.429em;}
.yong span{ display:block; color:#eaeaea; font-size:0.857em;}

.ding{ background:#fff; margin-top:1em;}
.ding ul,.ding li{ list-style:none; margin:0; padding:0;}
.ding ul{ overflow:hidden;}
.ding li{ float:left; width:20%;}
.ding li span{ font-size:1.429em; text-align:center; display:block; color:#444; margin-top:1.086em; position:relative;}
.ding li p{ font-size:1em; color:#444; text-align:center;}
.ding li span i{ display:block; font-style:normal; width:1.429em; height:1.429em; background:#fe6e12; color:#fff; font-size:0.6em; text-align:center; position:absolute; top:-10px; right:6px; border-radius:50%; line-height:1.429em;}

.lie_y{ background:#fff; margin-top:1.2em; margin-bottom:4.857em;}
.lie_y .col-xs-4{ margin:0; padding:0; border:1px solid #ececec; border-top:none; border-left:none;}
.lie_y .iconfont{ font-size:2.143em; display:block; color:#5da9fb; text-align:center; margin:0.614em 0 0.246em; }
.lll{ font-size:1em; text-align:center; color:#444; margin-bottom:1.143em;}


/*商家*/
.top_c .icon-jiantou-copy-copy{ float:left; color:#fff; display:block; margin-left:1em; line-height:2.68em;}
.shou{ float:right; display:block;}
.shou span{ font-size:1.143em;margin-right:1em; line-height:2.68em; color:#666; display:block;}
.shou .icon-shoucang1{ display:none;}
.titll .col-xs-2{width:50%;}
.titll .col-xs-4,.titll .col-xs-2{ padding:0.571em 0; text-align:center; font-size:1em; color:#666;}
.sp_b{ width:60px; height:62px; border:1px solid #ececec; float:left; display:block;}
.text{ float:left; margin-left:0.714em;}
.right_c li .text p{ font-size:1em; margin-bottom:0;}
.money{ margin-top:0.286em;}
.money span{ color:#fe6e12; font-size:1.143em;}
.right_c li .icon-tubiao225{ position:absolute; color:#1798f2; right:0; bottom:0.657em;}

.footer .icon-iconfontgouwuche{ display:block; float:left; font-size:2em; color:#555; line-height:1.9em; margin-left:0.614em; position:relative;}
.footer .icon-iconfontgouwuche i{ position:absolute; right:-9px; top:4px; display:block; width:1.67em; height:1.67em; font-size:0.429em; color:#fff; text-align:center; background:#fe6e12; line-height:1.67em; border-radius:50%; font-style:normal;}
.jia{ float:left; margin-left:1.714em; margin-top:0.143em;}
.jia p{ font-size:1.143em; color:#555; margin-bottom:-2px;}
.jia p em{ color:#fe6e12; font-size:1.25em; font-style:normal;}
.jia span{ color:#888; font-size:0.857em;}
.jie{ display:block; float:right; height:3em; width:5em; font-size:1.286em; color:#fff; background:#1798f2; line-height:3em; text-align:center;}

.ping{ display:none;}
.zong{ background:#fff; width:100%;}
.num{ font-size:1.571em; color:#fe6e12; display:block; text-align:center; margin:0.627em 0 0.2em;}
.he{ display:block; font-size:1em; text-align:center; margin-bottom:0.857em; color:#666;}
.zero{ overflow:hidden; margin-top:0.857em;}
.fu{ font-size:0.857em; color:#666; float:left; display:block; line-height:1.67em;}
.zero .xing{ float:left; margin-left:1.286em;}
.lun{ background:#fff; margin-top:1.143em; margin-bottom:1em;}
.lun ul,.lun li{ list-style:none; margin:0; padding:0;}
.lun li{ border-bottom:1px solid #ececec; margin-top:1em; padding-bottom:1em;}
.touxi{ overflow:hidden;  margin-bottom:8px;}
.touxi img{ display:block; float:left; width:40px; height:40px; border-radius:50%; margin-right:0.857em;}
.time p{ font-size:1em; color:#333; margin-bottom:0;}
.time span{ font-size:0.857em; color:#888;}
.nei{ font-size:1em; color:#666;}

.img_sh{ width:100%; display:block;}
.sh_name{ background:#fff; width:100%; margin-bottom:1em;}
.biti{ font-size:1.286em; color:#222; margin-top:1em;}
.sh_name .zero{ margin-top:0.3em;}
.sh_name .fu,.sh_name .glyphicon-star,.sh_name .xing{ font-size:1em; line-height:1.67em;}
.sh_name .xing{ margin-left:0.714em;}
.shop{ display:none;}

.window{ position:fixed; top:0; left:0; width:100%; height:100%; background:rgba(0,0,0,0.6); z-index:22; display:none;}
.gou{ background:#fff; position:fixed; width:100%; left:0; bottom:3.857em; z-index:23; display:none;}
.wu_t{ font-size:0.857em; color:#666; line-height:2.571em; border-bottom:1px solid #ececec; margin-bottom:4px;}
.wu_t i{ display:block; width:0.286em; height:1.286em; background:#fe6e12; margin:0.571em 0.571em 0 1.143em; float:left;}
.wu_t a{ display:block; float:right; color:#666; margin-right:1.143em;}
.wu_t a span{ margin-right:0.429em;}
.gou table tr td{ border-top:none; border-bottom:1px  solid #ccc;}
.j_ge{ color:#fe6e12;}
#add{ color:#1798f2;}
#min{ color:#666;}
.xuan input{ border:none; text-align:center; height:1.429em; width:2em;}

/*商品*/
.sp_tit{ background:#fff; border-top:1px solid #ececec;}
.sp_tit p{ font-size:1.286em; color:#222; font-weight:600; margin-top:1em;}
.gui{ font-size:1em; color:#888; display:block;}
.gui em{ font-style:normal; float:right;}
.ge{ display:block; color:#fe6e12; font-size:1em; margin:1em 0 0.714em;}
.ge em{ font-size:1.571em; font-style:normal;}
.ge a{ float:right; color:#1798f2; font-size:1.429em;}

.xiang{ background:#fff; margin-top:1.143em; margin-bottom:4.875em;}
.xi_t{ font-size:1.143em; color:#333; line-height:2.571em; border-bottom:1px solid #ececec;}
.xi_t i{ display:block; width:0.1875em; height:1.375em; background:#1798f2; margin:0.625em 0.5em 0 1em; float:left;}
.xiang img{ width:100%; margin-top:1em;}


/*搜索*/

.bi_ao{ background:#fff;}
.li_s{ margin-top:3em;}
.li_s p{ font-size:1.143em; line-height:2.286em; padding-left:1em; color:#333; margin-bottom:0;}
.content{ font-size:1em; line-height:2.143em; border-bottom:1px solid #ececec; color:#666;}
.kong{ display:block; color:#fe6e12; font-size:1em; text-align:center; line-height:2.286em;}


/*登录注册*/
.login img{ width:180px; margin:5.2em auto 2.6em; display:block;}
.ru{ width:100%; border-bottom:1px solid #ccc; line-height:2.714em; margin-top:1.2em;}
.ru input{ width:90%; border:none;}
.ru span{ font-size:1.286em; color:#888; margin-right:0.286em;}
.wang{ font-size:1em; color:#fe6e12; margin-top:0.871em; display:block; text-align:right;}
.deng{ width:100%; display:block; border-radius:6px; background:#1798f2; height:2.286em; font-size:1.143em; line-height:2.286em; text-align:center; color:#fff; margin:1.571em 0 0.714em; clear:both;}
.san hr{ height:1px; width:4.286em; float:left; background:#ccc;}
.san span{ font-size:1em; color:#666; margin:0.714em; float:left;}
.san i{ font-size:1.714em; color:#666; text-align:center; display:block; margin-top:0.429em;}
.san{ width:15em; margin:4em auto 0;}

.zhu input{ width:100%;}
.zhu{ margin-top:4em;}
.yan{position:absolute;bottom:0;right:0;}
.zhu .ru{ position:relative;}
.btn_mfyzm{ background:#fe6e12; color:#fff;}


/*充值提现*/

.cong{ margin-top:4em;}
.cong p{ font-size:1em; color:#666; line-height:2.286em; padding-left:1em;}
.cong p em{ font-size:1.286em; color:#fe6e12; font-style:normal;}
.zhi{ background:#fff; line-height:2.714em; }
.zhi span{ font-size:1em; color:#333; margin-right:0.571em;}
.zhi input{ width:80%; border:0;}
.ming{ display:block; float:right; font-size:1em; color:#888; line-height:3em; margin-right:1em;}

.zhi .icon-zhifubao{ color:#009fe8; font-size:1.714em; margin-top:0.143em; display:block; float:left;}
.zhong{ display:block; float:right;}
.zhong span{ margin-right:0; color:#666; font-size:1.286em;}
.zhong .icon-xuanzhong{ display:none;color:#fe6e12;}

.ka{ background:#fff; overflow:hidden; padding-bottom:0.429em; margin-bottom:1em;}
.hao{ float:left;}
.hao p{ font-size:1.143em; color:#333; padding-left:0; margin-bottom:0; line-height:2em;}
.hao span{ font-size:0.857em; color:#888; margin-bottom:0.286em;}
.ka .icon-jiantou{ font-size:1.286em; color:#ccc; float:right; line-height:3em;}

.xi_m{ margin-top:3em; background:#fff;}
.xi_one{ overflow:hidden; border-bottom:1px solid #ececec; padding:0.571em 0;}
.xi_left{ float:left;}
.xi_left p{ font-size:1em; color:#333; margin-bottom:0;}
.xi_left span,.xi_right span{ font-size:0.857em; color:#999; display:block;}
.xi_right{ float:right;}
.xi_right p{ font-size:1.143em; color:#fe6e12; margin-bottom:0; text-align:right;}
.xi_right span{ text-align:right;}


/*信用额度*/

.tou_e{ margin-top:3em; background:#1798f2; padding:1.429em 0 0.714em;}
.jian_z{ text-align:center;}
.jian_z span{ display:block; text-align:center; color:#c4d4eb; font-size:1em; margin-top:0.286em;}
.jian_z b{ color:#fff; font-size:2em; margin:0.286em auto 0.714em; border-bottom:1px  solid #91b8e3; font-weight:normal; display:inline-block;}
.jian_z p{ font-size:1.571em; text-align:center; color:#dfe8f1;}

.dna{ background:#fff; overflow:hidden;}
.zhangd{ float:left;}
.zhangd p{ font-size:1.143em; color:#333; margin:0.714em 0 0.429em;}
.zhangd span{ display:block; font-size:0.857em; color:#999;}
.zhangd i{ font-size:1.571em; font-style:normal; color:#666; margin-bottom:0.429em; display:block;}
.huan{ float:right; color:#1798f2; width:5.2em; font-size:1.143em; line-height:1.857em; display:block; border:1px solid #1798f2; border-radius:30px; text-align:center; margin-top:2em;}

.tu{ width:6em; height:6em; background:#ddd; border-radius:50%; text-align:center; line-height:6em; margin:6em auto 2em;}
.tu span{ font-size:2.714em; color:#666;}
.nin{ font-size:1.143em; color:#333; text-align:center;}
.tiao{ text-align:center; margin-top:2em;}
.tiao p{ font-size:0.857em; color:#333;}
.tiao span{ font-size:0.857em; color:#777;}

.xi_right .edu{ color:#333; font-size:1.286em; line-height:2.143em;}

/*银行卡*/
.bank{ background:#1798f2; width:100%; border-radius:6px; margin-top:4em;}
.bank span{ display:block; padding:1.143em 1em; font-size:1em; color:#f5f5f5;}
.bank p{ font-size:1.714em; text-align:right; padding-right:1em; padding-bottom:1.143em; color:#fff;}
.jia_t{ position:fixed; bottom:0; left:0; width:100%; background:#fff; height:3em; line-height:3em; display:block; text-align:center; font-size:1.143em; color:#222; border-top:1px solid #ccc;}

/*账户子页*/

.one p em{ float:right; font-size:1.714em; color:#999; margin-right:0.571em;}

.ding_d{ margin-top:6.4em; margin-bottom:5.571em;}
.on_d{ background:#fff; margin-top:1em;}
.bh{ font-size:0.857em; line-height:2.857em; color:#666; padding:0 1em; border-bottom:1px solid #ececec; margin-bottom:0;}
.bh span{ float:right; font-size:1.33em; color:#fe6e12;}
.button{ overflow:hidden; float: right;}
.button a{ display:block; float:right; width:3em; height:2.143em; font-size:1em; text-align:center; line-height:2.1em; border-radius:6px; } 
.button .liji{ background:#fff;border:1px solid #1798f2; color:#1798f2;}
.button .qu{ color:#333; border:1px solid #666;}
.button .lip{ background:#fe6e12; color:#fff;}

.button p span{color:#00c451;font-weight:600;padding:0 5px;}

.deta{ margin-top:3em; margin-bottom:4.4em;}
.zhuang{ line-height:2.571em; font-size:1.143em; color:#fe6e12; padding-left:1em; background:#fff;}
.d_min{ font-size:1em; padding-left:1em; line-height:2.429em; color:#333; margin-bottom:0;}
.leb{ background:#fff;}
.d_ti{ display:block; border-bottom:1px solid #ececec; font-size:0.857em; color:#888; line-height:3em;}
.d_ti img{ width:20px; height:20px; float:left; margin:0.714em 0.714em 0.714em 1em; display:block;}
.d_ti i{ float:right; font-size:1em; margin-right:1em; color:#ccc;}
.leb table tr td{ border:none; color:#444;}
.leb table{ margin:0.714em 0;}
.pei,.hui{ display:block; border-top:1px solid #ececec; font-size:1em; color:#555; line-height:2.714em;}
.pei i,.hui i{ font-style:normal; float:right;}
.hui{ color:#999;}
.hui i{ color:#555;}
.hui i em{ font-style:normal; color:#fe6e12;}
.bu_fi{ position:fixed; bottom:0; left:0; background:#fff; width:100%;}

.ke{ color:#888; margin-left:0.714em;}
.lit{ display:block; width:12.143em; height:8.571em; margin-top:0.714em;}
.shi{ display:block; color:#666; text-align:center; line-height:2.143em;}

.sp_pr a{ display:block;}

.addr{ margin-top:4em;}
.add{ background:#fff; border-radius:6px; padding:0.714em; margin-bottom:1em;}
.add p{ font-size:1.143em; color:#333;}
.add p i{ font-size:0.875em; font-style:normal; color:#666; margin-left:0.714em;}
.add span{ font-size:0.857em; color:#888;}
.mo{ border-top:1px solid #ececec; margin-top:0.714em; padding-top:0.714em;}
.mo a{ font-size:0.857em; color:#666; float:right; margin-right:1em;}
.mo a span{ margin-right:0.286em; color:#888;}

.tui{ margin-top:4em; background:#fff; border-radius:6px; padding:0.714em 0;}
.tui p{ font-size:1.143em; color:#888; text-align:center; margin-top:0.429em;}
.tui p span{ color:#fe6e12; font-size:1.25em;}
.tui img{ width:120px; margin:2.143em auto; display:block;}

.bj{ width:100%; height:180px; background:url(../images/bjjj.jpg) no-repeat; background-size:cover; padding-top:1px; position:relative;}
.bj img{ width:80px; height:80px; display:block; border-radius:6px; margin:2.429em auto 0.714em;}
.bj p{ font-size:1.143em; color:#fff; text-align:center;}
.bj .icon-jiantou-copy-copy{ position:absolute; top:0.714em; left:1em; color:#666; font-size:1em;}


/* toggle switch css start */
.switch-btn {position: relative; display: block; vertical-align: top; width: 52px; height: 20px; border-radius: 18px; cursor: pointer; float:right; margin-top:8px;}
.checked-switch {position: absolute; top: 0; left: 0; opacity: 0;}
.text-switch {background-color:#fe6e12; border: 1px solid #ef6208; border-radius: inherit; color: #fff; display: block; font-size: 0.857em; height: inherit; position: relative; text-transform: uppercase;}
.text-switch:before, .text-switch:after {position: absolute; top: 50%; margin-top: -.5em; line-height: 1; -webkit-transition: inherit; -moz-transition: inherit; -o-transition: inherit; transition: inherit;}
.text-switch:before {content: attr(data-no); right: 11px;}
.text-switch:after {content: attr(data-yes); left: 11px; color: #FFFFFF; opacity: 0;}
.checked-switch:checked ~ .text-switch {background-color: #00af2c; border: 1px solid #01a42a;}
.checked-switch:checked ~ .text-switch:before {opacity: 0;}
.checked-switch:checked ~ .text-switch:after {opacity: 1;}
.toggle-btn {background: linear-gradient(#eee, #fafafa); border-radius: 100%; height: 18px; left: 1px; position: absolute; top: 1px; width: 18px;}
.toggle-btn::before {color: #aaaaaa; display: inline-block; font-size: 12px; letter-spacing: -2px; padding: 4px 0; vertical-align: middle;}
.checked-switch:checked ~ .toggle-btn {left: 32px;}
 .text-switch, .toggle-btn {transition: All 0.3s ease; -webkit-transition: All 0.3s ease; -moz-transition: All 0.3s ease; -o-transition: All 0.3s ease;}

.tis{ position:fixed; top:50%; left:0; z-index:30; width:100%; height:180px; margin-top:-90px; display:none;}
.tib{ background:#fff; border-radius:6px; padding:0.714em;}
.tib p{ font-size:1.143em; color:#333; line-height:2.571em; text-align:center;}

.diz{ background:#fff; margin-top:1em; padding:1em 0;}
.diz .add{ margin-bottom:0; padding:0;}

































@media (min-width: 340px) {
	
.em_s{ margin-right:20px;}
.sp_b{ width:72px;}	
	
	
	
}






















