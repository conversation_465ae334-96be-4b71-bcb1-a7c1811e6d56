<!DOCTYPE html>
<html lang="en" class="no-js">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="description" content="">
  <meta name="keywords" content="">
<!--  <meta name="viewport" content="width=device-width, initial-scale=1">-->
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
  

  <!-- Set render engine for 360 browser -->
  <meta name="renderer" content="webkit">

  <!-- No Baidu Siteapp-->
  <meta http-equiv="Cache-Control" content="no-siteapp"/>

  <link rel="icon" type="image/png" href="https://aiqianjin193.com/Application/Home/Public/assets/i/favicon.png">

  <!-- Add to homescreen for Chrome on Android -->
  <meta name="mobile-web-app-capable" content="yes">
  <link rel="icon" sizes="192x192" href="https://aiqianjin193.com/Application/Home/Public/assets/i/<EMAIL>">

  <!-- Add to homescreen for Safari on iOS -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="Amaze UI"/>
  <link rel="apple-touch-icon-precomposed" href="https://aiqianjin193.com/Application/Home/Public/assets/i/<EMAIL>">

  <!-- Tile icon for Win8 (144x144 + tile color) -->
  <meta name="msapplication-TileImage" content="https://aiqianjin193.com/Application/Home/Public/assets/i/<EMAIL>">
  <meta name="msapplication-TileColor" content="#0e90d2">

  <!-- SEO: If your mobile URL is different from the desktop URL, add a canonical link to the desktop page https://developers.google.com/webmasters/smartphone-sites/feature-phones -->
  <!--
  <link rel="canonical" href="http://www.example.com/">
  -->

  <link rel="stylesheet" href="https://aiqianjin193.com/Application/Home/Public/assets/css/amazeui.min.css">
  <link rel="stylesheet" href="https://aiqianjin193.com/Application/Home/Public/assets/css/app.css">


  <link rel="stylesheet" href="https://aiqianjin193.com/Application/Home/Public/assets/fontawesome-free-5.7.2-web/css/all.css">



  

<title> - 站长图库（zzTuKu.com） </title>
<link rel="stylesheet" href="https://aiqianjin193.com/Application/Home/Public/css/common.css">
<link rel="stylesheet" href="https://aiqianjin193.com/Application/Home/Public/css/iindex.css">

<style>
	html,body
	{
		background: #ffffff;
		height: 100%;
	}
</style>
</head>
<body id="sy">
<!--[if lte IE 9]>
<p class="browsehappy">你正在使用<strong>过时</strong>的浏览器，Amaze UI 暂不支持。 请 <a
  href="http://browsehappy.com/" target="_blank">升级浏览器</a>
  以获得更好的体验！</p>
<![endif]-->


	<div class="no_attestation ">
			<div data-am-sticky class="am-g fixed_top" style="font-size: 20px;line-height:300%;text-align:center;">
				<div class="am-u-sm-2">
					   &nbsp;
				</div>
				<div class="am-u-sm-8">
					<b class="website_name">爱钱进</b>
				</div>
				<div class="am-u-sm-2">
					<i class="fas fa-envelope-open-text"></i>
				</div>

			</div>

			<div class="am-g" style="text-align: center; padding: 20px 0 25px;">
				<samll class="other_txt">预估可借额度(元)</samll><br>
				<span class="f_number quota">55,607</span><br>
				<samll class="other_txt">①填写资料&nbsp;②一键投递&nbsp;③极速放款</samll>

			</div>

			<div class="am-g">
				<div class="am-u-sm-7 am-u-sm-centered">
					<form action="" method="post">
						<button type="submit" class="am-btn" id="">立即去借款</button>

					</form>
				</div>
			</div>




	</div>
	<div class="ts ts_1">
		<div class="ts_box">
			<div>
				聪明的人已加入 <br>
				<span class="number_style estimate_1">443,361</span>&nbsp;人
			</div>
			<div style="height: 10px; width: 1px;"></div>
			<div>
				已借出 <br>
				<span class="number_style estimate_2">31,791,679</span>&nbsp;元
			</div>
		</div>
	</div>
	<div class="ts ts_2">
		<div class="ts_box">
			<h2 style="
				font-size: 200%;
				letter-spacing: 5px;
			">爱钱进</h2>
			<div style="
				/* height: 100%; */
				position: absolute;
				bottom: 25px;
			">
				<span class="number_style estimate_3">31,980,426</span><br>
				累计交易额(元)
			</div>
			<div style="height: 10px; width: 1px;"></div>

		</div>
	</div>
  	<div class="icp">
		爱钱进有限公司 京ICP备  16006448号	</div>



	<div class="am-modal am-modal-no-btn" tabindex="-1" id="cop">
	<div class="am-modal-dialog" style="width: 340px;background: none;">
		<div class="am-modal-hd">
			<a href="javascript: void(0);" class="am-close am-close-spin" data-am-modal-close style="color: #ffffff;font-size: 30px;opacity: 1;background: #ff0000;">&times;</a>
		</div>
		<div class="am-modal-bd">
			<div>

				<div class="cop_box">
					<div class="cop_txt">
						<div class="cop_title">恭喜您获得新人首单免息券</div>
						<div class="am-g ">
							<span class="cop_number f_number"></span>
							<span class="left cop_number_txt">期免息</span>
						</div>
						<div style="font-weight: bold;padding: 10px 0 0;">全场无门槛</div>
						<div style="font-size: 12px;padding: 5px 0 0;">有效期至 <span class="overtime"></span> </div>
					</div>

					<div class="cop_btn">
						立即收下
					</div>

				</div>

			</div>
		</div>
	</div>
</div>




	<div class="message">
		<p></p>
	</div>


	

<!-- 底部导航条 -->


<div data-am-widget="navbar" class="am-navbar am-cf am-navbar-default " id="bm-nav">
  <ul class="am-navbar-nav am-cf am-avg-sm-4" style="background-color: #ffffff;">
  
  						
					
					
					 <li class="nva_sy">
      <a href="/index.php/Home/Index/no_attestation?kefuid=" class="">
        <img src="https://aiqianjin193.com/Application/Home/Public/img/nav/2-1.png" alt="消息"/>

        <span class="am-navbar-label">首页</span>
      </a>
    </li>
    <li class="nva_qb">
      <a href="/index.php/Home/Wallet/index?kefuid=" class="">
        <img src="https://aiqianjin193.com/Application/Home/Public/img/nav/3-1.png" alt="消息"/>

        <span class="am-navbar-label">钱包</span>
      </a>
    </li>
    <li class="nva_kf">
      <a href="/index.php/Home/Cservice/index?kefuid=" class="">
        <img src="https://aiqianjin193.com/Application/Home/Public/img/nav/1-1.png" alt="消息"/>

        <span class="am-navbar-label">客服</span>
      </a>
    </li>
    <li class="nva_wd">
      <a href="/index.php/Home/Mine/index?kefuid=" class="">
        <img src="https://aiqianjin193.com/Application/Home/Public/img/nav/4-1.png" alt="消息"/>

        <span class="am-navbar-label">我的</span>
      </a>
    </li>   
    
    
    
    
  </ul>
</div>


<!--	<div id="kefu"></div>-->
	<script type="text/javascript">
    document.documentElement.addEventListener('touchmove', function(event) {
        if (event.touches.length > 1) {
            event.preventDefault();
        }
    }, false);
</script>


<!--[if lt IE 9]>
<script src="https://aiqianjin193.com/Application/Home/Public/assets/js/jquery3.2.min.js"></script>
<script src="http://cdn.staticfile.org/modernizr/2.8.3/modernizr.js"></script>
<script src="https://aiqianjin193.com/Application/Home/Public/assets/js/amazeui.ie8polyfill.min.js"></script>
<![endif]-->

<!--[if (gte IE 9)|!(IE)]><!-->
<script src="https://aiqianjin193.com/Application/Home/Public/assets/js/jquery3.2.min.js"></script>
<!--<![endif]-->
<script src="https://aiqianjin193.com/Application/Home/Public/assets/js/amazeui.min.js"></script>

	<script>

		$("#sy #bm-nav .nva_sy a img").attr('src','https://aiqianjin193.com/Application/Home/Public/img/nav/2-2.png');

		$(".ts").height($(".ts").width() / 1.7);

		$(window).resize(function() {
			$(".ts").height($(".ts").width() / 1.7);
		});

		$(window).scroll( function (){
			var x = $('.no_attestation').offset().top - $(window).scrollTop();

			if (x != 0)
			{
				$(".fixed_top").css('background','rgba(255,255,255,0.9)');
			}else{
				$(".fixed_top").css('background','none');
			}
			// console.log(x);
		});

	</script>

	<script type="text/javascript" src="https://aiqianjin193.com/Application/Home/Public/js/iindex.js"></script>
<div id="kefu"></div>
	<script>
		setInterval(function () {
			$.post('up_estimate',
					{

					},function (data,status) {
						var obj = JSON.parse(data);
						$(".estimate_1").html(("" + obj.estimate_1).replace(/(\d{1,3})(?=(\d{3})+(?:$|\.))/g, "$1,"));
						$(".estimate_2").html(("" + obj.estimate_2).replace(/(\d{1,3})(?=(\d{3})+(?:$|\.))/g, "$1,"));
						$(".estimate_3").html(("" + obj.estimate_3).replace(/(\d{1,3})(?=(\d{3})+(?:$|\.))/g, "$1,"));
					}
			);
		},5000);
	</script>

</body>
</html>