<?php
/**
 * Smarty plugin
 *
 * @package Smarty
 * @subpackage PluginsFunction
 */

/**
 * Smarty {cycle} function plugin
 *
 * Type:     function<br>
 * Name:     cycle<br>
 * Date:     May 3, 2002<br>
 * Purpose:  cycle through given values<br>
 * Params:
 * <pre>
 * - name      - name of cycle (optional)
 * - values    - comma separated list of values to cycle, or an array of values to cycle
 *               (this can be left out for subsequent calls)
 * - reset     - boolean - resets given var to true
 * - print     - boolean - print var or not. default is true
 * - advance   - boolean - whether or not to advance the cycle
 * - delimiter - the value delimiter, default is ","
 * - assign    - boolean, assigns to template var instead of printed.
 * </pre>
 * Examples:<br>
 * <pre>
 * {cycle values="#eeeeee,#d0d0d0d"}
 * {cycle name=row values="one,two,three" reset=true}
 * {cycle name=row}
 * </pre>
 *
 * @link http://www.smarty.net/manual/en/language.function.cycle.php {cycle}
 *       (Smarty online manual)
 * <AUTHOR> <monte at ohrt dot com>
 * <AUTHOR> to <PERSON> <<EMAIL>>
 * <AUTHOR> to <PERSON> <<EMAIL>>
 * <AUTHOR> to <PERSON> <<EMAIL>>
 * @version  1.3
 * @param array                    $params   parameters
 * @param Smarty_Internal_Template $template template object
 * @return string|null
 */

function smarty_function_cycle($params, $template)
{
    static $cycle_vars;

    $name = (empty($params['name'])) ? 'default' : $params['name'];
    $print = (isset($params['print'])) ? (bool)$params['print'] : true;
    $advance = (isset($params['advance'])) ? (bool)$params['advance'] : true;
    $reset = (isset($params['reset'])) ? (bool)$params['reset'] : false;

    if (!isset($params['values'])) {
        if(!isset($cycle_vars[$name]['values'])) {
            trigger_error("cycle: missing 'values' parameter");
            return;
        }
    } else {
        if(isset($cycle_vars[$name]['values'])
            && $cycle_vars[$name]['values'] != $params['values'] ) {
            $cycle_vars[$name]['index'] = 0;
        }
        $cycle_vars[$name]['values'] = $params['values'];
    }

    if (isset($params['delimiter'])) {
        $cycle_vars[$name]['delimiter'] = $params['delimiter'];
    } elseif (!isset($cycle_vars[$name]['delimiter'])) {
        $cycle_vars[$name]['delimiter'] = ',';
    }

    if(is_array($cycle_vars[$name]['values'])) {
        $cycle_array = $cycle_vars[$name]['values'];
    } else {
        $cycle_array = explode($cycle_vars[$name]['delimiter'],$cycle_vars[$name]['values']);
    }

    if(!isset($cycle_vars[$name]['index']) || $reset ) {
        $cycle_vars[$name]['index'] = 0;
    }

    if (isset($params['assign'])) {
        $print = false;
        $template->assign($params['assign'], $cycle_array[$cycle_vars[$name]['index']]);
    }

    if($print) {
        $retval = $cycle_array[$cycle_vars[$name]['index']];
    } else {
        $retval = null;
    }

    if($advance) {
        if ( $cycle_vars[$name]['index'] >= count($cycle_array) -1 ) {
            $cycle_vars[$name]['index'] = 0;
        } else {
            $cycle_vars[$name]['index']++;
        }
    }

    return $retval;
}

?>