void function(a,b){var c=a.alogObjectName||"alog",d=a[c]=a[c]||function(){a[c].l=a[c].l||+new Date,(a[c].q=a[c].q||[]).push(arguments)},e="feature";d("define",e,function(){function c(a,b){return typeof a===b}function f(a,b,d){var e;for(var f in a)if(a[f]in b)return d===!1?a[f]:(e=b[a[f]],c(e,"function")?fnBind(e,d||b):e);return!1}function g(a,b,c){return h(a,void 0,void 0,b,c)}function h(a,b,d,e,g){var h=a.charAt(0).toUpperCase()+a.slice(1),j=(a+" "+da.join(h+" ")+h).split(" ");return c(b,"string")||c(b,"undefined")?i(j,b,e,g):(j=(a+" "+da.join(h+" ")+h).split(" "),f(j,b,d))}function i(a,b,d,e){e=c(e,"undefined")?!1:e;var f,g,h,i;for(g=a.length,f=0;g>f;f++)if(h=a[f],i=ha.style[h],~(""+h).indexOf("-")&&(h=j(h)),void 0!==ha.style[h])return"pfx"==b?h:!0;return!1}function j(a){return a.replace(/([a-z])-([a-z])/g,function(a,b,c){return b+c.toUpperCase()}).replace(/^-/,"")}function k(){var a=fa("canvas");return!(!a.getContext||!a.getContext("2d"))}function l(){var a=fa("div");return"draggable"in a||"ondragstart"in a&&"ondrop"in a}function m(){var a="localStorage";try{return localStorage.setItem(a,a),localStorage.removeItem(a),!0}catch(b){return!1}}function n(){return"content"in b.createElement("template")}function o(){return"createShadowRoot"in b.createElement("a")}function p(){return"registerElement"in b}function q(){return"import"in b.createElement("link")}function r(){return"getItems"in b}function s(){return"EventSource"in window}function t(a,b){var c={lossy:"UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA",lossless:"UklGRhoAAABXRUJQVlA4TA0AAAAvAAAAEAcQERGIiP4HAA==",alpha:"UklGRkoAAABXRUJQVlA4WAoAAAAQAAAAAAAAAAAAQUxQSAwAAAARBxAR/Q9ERP8DAABWUDggGAAAABQBAJ0BKgEAAQAAAP4AAA3AAP7mtQAAAA==",animation:"UklGRlIAAABXRUJQVlA4WAoAAAASAAAAAAAAAAAAQU5JTQYAAAD/////AABBTk1GJgAAAAAAAAAAAAAAAAAAAGQAAABWUDhMDQAAAC8AAAAQBxAREYiI/gcA"},d=new Image;d.onload=function(){var c=d.width>0&&d.height>0;b(a,c)},d.onerror=function(){b(a,!1)},d.src="data:image/webp;base64,"+c[a]}function u(a,b){return ja.f["WebP-"+a]=b,b}function v(){return"openDatabase"in a}function w(){return"performance"in a&&"timing"in a.performance}function x(){return"performance"in a&&"mark"in a.performance}function y(){return!!(Array.prototype&&Array.prototype.every&&Array.prototype.filter&&Array.prototype.forEach&&Array.prototype.indexOf&&Array.prototype.lastIndexOf&&Array.prototype.map&&Array.prototype.some&&Array.prototype.reduce&&Array.prototype.reduceRight&&Array.isArray)}function z(){return"Promise"in a&&"cast"in a.Promise&&"resolve"in a.Promise&&"reject"in a.Promise&&"all"in a.Promise&&"race"in a.Promise&&function(){var b;return new a.Promise(function(a){b=a}),"function"==typeof b}()}function A(){var b=!!a.ProgressEvent,c=!!a.FormData,d=a.XMLHttpRequest&&"withCredentials"in new XMLHttpRequest;return b&&c&&d}function B(){return"geolocation"in navigator}function C(){var b=fa("canvas"),c="probablySupportsContext"in b?"probablySupportsContext":"supportsContext";return c in b?b[c]("webgl")||b[c]("experimental-webgl"):"WebGLRenderingContext"in a}function D(){return!!b.createElementNS&&!!b.createElementNS("http://www.w3.org/2000/svg","svg").createSVGRect}function E(){return!!a.Worker}function F(){return"WebSocket"in a&&2===a.WebSocket.CLOSING}function G(){var a=b.createElement("video");return!!a.canPlayType}function H(){var a=b.createElement("audio");return!!a.canPlayType}function I(){return!!(a.history&&"pushState"in a.history)}function J(){return!(!a.File||!a.FileReader)}function K(){return"postMessage"in window}function L(){return!!a.webkitNotifications||"Notification"in a&&"permission"in a.Notification&&"requestPermission"in a.Notification}function M(){for(var b=["webkit","moz","o","ms"],c=a.requestAnimationFrame,d=0;d<b.length&&!c;++d)c=a[b[d]+"RequestAnimationFrame"];return!!c}function N(){return"JSON"in a&&"parse"in JSON&&"stringify"in JSON}function O(){return!(!ia("exitFullscreen",b,!1)&&!ia("cancelFullScreen",b,!1))}function P(){return!!ia("Intl",a)}function Q(){return g("flexBasis","1px",!0)}function R(){return!!g("perspective","1px",!0)}function S(){return g("shapeOutside","content-box",!0)}function T(){var a=fa("div");return a.style.cssText=ea.join("filter:blur(2px); "),!!a.style.length&&(void 0===b.documentMode||b.documentMode>9)}function U(){return"XMLHttpRequest"in a&&"withCredentials"in new XMLHttpRequest}function V(){return void 0!==fa("progress").max}function W(){return void 0!==fa("meter").max}function X(){return"sendBeacon"in navigator}function Y(){return g("borderRadius","0px",!0)}function Z(){return g("boxShadow","1px 1px",!0)}function $(){var a=fa("div"),b=a.style;return b.cssText=ea.join("opacity:.55;"),/^0.55$/.test(b.opacity)}function _(){return i(["textShadow"],void 0,"1px 1px")}function aa(){return g("animationName","a",!0)}function ba(){return g("transition","all",!0)}function ca(){return-1===navigator.userAgent.indexOf("Android 2.")&&g("transform","scale(1)",!0)}var da=["Moz","O","ms","Webkit"],ea=["-webkit-","-moz-","-o-","-ms-"],fa=function(){return"function"!=typeof b.createElement?b.createElement(arguments[0]):b.createElement.apply(b,arguments)},ga={elem:fa("dpFeatureTest")},ha={style:ga.elem.style},ia=function(a,b,c){return 0===a.indexOf("@")?atRule(a):(-1!=a.indexOf("-")&&(a=j(a)),b?h(a,b,c):h(a,"pfx"))},ja={f:{},t:function(a,b,c){this.f[a]=b.apply(this,[].slice.call(arguments,2))},c:function(a,b){a.apply(this,[].slice.call(arguments,1))},runAllTest:function(){var a=this;a.t("bdrs",Y),a.t("bxsd",Z),a.t("opat",$),a.t("txsd",_),a.t("anim",aa),a.t("trsi",ba),a.t("trfm",ca),a.t("flex",Q),a.t("3dtr",R),a.t("shpe",S),a.t("fltr",T),a.t("cavs",k),a.t("dgdp",l),a.t("locs",m),a.t("wctem",n),a.t("wcsdd",o),a.t("wccse",p),a.t("wchti",q),a.c(t,"lossy",u),a.c(t,"lossless",u),a.c(t,"alpha",u),a.c(t,"animation",u),a.t("wsql",v),a.t("natm",w),a.t("ustm",x),a.t("arra",y),a.t("prms",z),a.t("xhr2",A),a.t("wbgl",C),a.t("geol",B),a.t("svg",D),a.t("work",E),a.t("wbsk",F),a.t("vido",G),a.t("audo",H),a.t("hsty",I),a.t("file",J),a.t("psmg",K),a.t("wknf",L),a.t("rqaf",M),a.t("json",N),a.t("flsc",O),a.t("i18n",P),a.t("cors",U),a.t("prog",V),a.t("metr",W),a.t("becn",X),a.t("mcrd",r),a.t("esrc",s)}},ka=d.tracker(e);return ka.on("commit",function(){ja.runAllTest();var a=setInterval(function(){if("WebP-lossy"in ja.f&&"WebP-lossless"in ja.f&&"WebP-alpha"in ja.f&&"WebP-animation"in ja.f){for(var b in ja.f)ja.f[b]=ja.f[b]?"y":"n";ka.send("feature",ja.f),clearInterval(a)}},500)}),ka})}(window,document);