html{

    font-size: 65%;

    font-family: "Helvetica Neue", Helvetica, STHeiTi, sans-serif;

}

body, div, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, pre, form, fieldset, input, p, blockquote, table, th, td, embed{

    padding: 0;

    margin: 0;

    font-family: Helvetica;

    background-color: #f4f4f4;

    font-size: 1.5rem;

}

img{

    display: block;

    width: 100%;

}

img{

    border: none;

    display: inline-block;

    vertical-align: middle;

}

a, button, input, textarea{

    -webkit-tap-highlight-color: transparent;

    background-color: transparent;

    -webkit-tap-highlight-color: rgba(255,255,255,0);

    -webkit-appearance: none;

    border: 0 none;

    outline: none;

}

ul,li{

    list-style: none;

}

input[type=button]{

    -webkit-appearance: none;

    outline: none;

}

.jk_btn{

    width: 92%;

    display: block;

    margin: 0 auto;

    text-align: center;

    color: #fff;

    font-size: 1.8rem;

    border: 0 none;

    background-color: #1b8eee;

    height: 4.5rem;

    line-height: 4.5rem;

    border-radius: 4px;

}

.head-ad img{

    display: block;

    width: 100%;

}

.bg_fff{

    background-color: #fff;

}

.ml_borrowMoney{

    padding: 1.3rem 0 1.9rem 0;

    box-sizing: border-box;

    border-bottom: 1px solid #f4f4f4;

}

.f_88868F{

    color: #88868F;

}

.f13{

    font-size: 1.3rem;

}

.tac{

    text-align: center;

}

.ml_borrowMoneyNum{

    margin-top: 1.6rem;

    line-height: 5.3rem;

    text-align: center;

    width: 100%;

}

.clearfix{

    display: block;

    zoom: 1;

}

.ml_borrowMoneyNum span{

    width: 33.33%;

    text-align: center;

    color: #88868F;

}

span.activeMoney{

    color: #5462EB;

    font-size: 3.8rem;

}

.fl{

    float: left;

}

.ml_borrowMonth{

    padding-top: 1.3rem;

    box-sizing: border-box;

}

.clear{

    clear:both;

}

.ml_borrowMonthChoice{

    margin-top: 2rem;

    color: #88868F;

}

.ml_borrowMonthChoice>li{

    float: left;

    width: 33.33%;

    display: block;

    text-align: center;

    background-color: #fff;

    margin-bottom: 1rem;

}

.ml_borrowMonthChoice>li span{

    width: 6rem;

    height: 4rem;

    border-radius: 4px;

    display: block;

    margin: 0 auto;

    text-align: center;

    border: 1px solid #E9E9E9;

    line-height: 4rem;

}

ul li span.activeMonth{

    border: 1px solid #5462EB;

    color: #5462EB;

}

.jk_registerSubmit{

    width: 100%;

    box-sizing: border-box;

    padding-top: 2rem;

}

.mb_2{

    margin-bottom: 2rem;

}

.mb_10{

    margin-bottom: 1.0rem;

}

.mt_2{

    margin-top: 2rem;

}

.mt_20{

    margin-top: 2rem;

}

.fs_16{

    font-size: 1.6rem;

}

.fc_6{

    color: #666;

}

input{

    display: inline-block;

    vertical-align: middle;

    font-family: arial,\5FAE\8F6F\96C5\9ED1,sans-serif;

}

.fc_blue{

    color: #0697da;

}

.mt_10{

    margin-top: 1rem;

}

.mb_5{

    margin-bottom: .5rem;

}

.w_10{

    width: 100%;

}

.pr_15{

    padding-right: 1.5rem;

}

.pt_15{

    padding-top: 1.5rem;

}

.tc{

    text-align: center;

}

.fs_14{

    font-size: 1.4rem;

}

.a_blue{

    color: #0697da;

}

.line_h22{

    line-height: 2.2rem;

}

.plr_15{

    padding-left: 1.5rem;

    padding-right: 1.5rem;

}

.fc_f{

    color: #fff;

}

.main_input .hl{

    width: 8rem;

    position: absolute;

    display: block;

    top: 0;

    line-height: 4.4rem;

    left: 0;

    z-index: 999;

}

.border_b{

    border-bottom: 1px solid #e1e1e1;

}

.btn_blue{

    background: #0697da;

    color: #fff;

}

.bg_white{

    background: #fff;

}

.pd{

    padding: 0.8rem 1.5rem;

}

.mb_15{

    margin-bottom: 1.5rem;

}









.footer{

    position: fixed;

    bottom: 0;

    left: 0;

    width: 100%;

    height: 5.5rem;

    background-color: #fff;

    overflow: hidden;

    text-align: center;

    z-index: 99;

}

.border_t{

    border-top: 1px solid #e1e1e1;

}

.footDiv{

    height: 7rem;

}

.footer a{width:25%;float: left;box-sizing: border-box;font-size:1.4rem;height:5.5rem;}

.footer a span{width:25px;height:25px;margin:.8rem auto .2rem;display:block;}

.footer a:nth-child(1) span{background:url(../imgs/ico_foot1.png) no-repeat center center;background-size:contain;}

.footer a:nth-child(2) span{background:url(../imgs/ico_foot2.png) no-repeat center center;background-size:contain;}

.footer a:nth-child(3) span{background:url(../imgs/ico_foot3.png) no-repeat center center;background-size:contain;}

.footer a:nth-child(4) span{background:url(../imgs/ico_foot4.png) no-repeat center center;background-size:contain;}

.footer a span:hover,.footer a span.nav_on{color:#0697da;}

.footer a:nth-child(1).nav_on span{background-image:url(../imgs/ico_foota.png);}

.footer a:nth-child(2).nav_on span{background-image:url(../imgs/ico_footb.png);}

.footer a:nth-child(3).nav_on span{background-image:url(../imgs/ico_footc.png);}

.footer a:nth-child(4).nav_on span{background-image:url(../imgs/ico_footd.png);}





.page44 .banner{

    width: 100%;

    background: url(../img/mobile/usr_1.jpg) center top no-repeat;

    background-size: cover;

}

.page44 .b_top{

    height: 6.5rem;

    position: relative;

    background-color: transparent;!important

}

.page44 .msg_box{

    position: absolute;

    width: 2.2rem;

    height: 2.2rem;

    line-height: 3.6rem;

    left: 1.5rem;

    top: -.8rem;

    z-index: 1;

    text-align: center;

    background-color: transparent;!important

}

.page44 .msg_ico{

    width: 2.6rem;

    height: 2.6rem;

    background: url(../../img/mobile/msg.png) center no-repeat;

    background-size: contain;

    display: block;

}

.page44 .photo{

    width: 6.5rem;

    height: 6.5rem;

    margin: 0 auto;

    border-radius: 50%;

    overflow: hidden;

}

.photo img{

    border: none;

    display: inline-block;

    vertical-align: middle;

}

.page44 .b_center{

    height: 4rem;

    line-height: 4rem;

    background-color: transparent;!important

}

.page44 .b_bott{

    height: 4rem;

    background-color: transparent;!important

}

.page44 .b_bott a{

    display: inline-block;

    height: 2.8rem;

    line-height: 2.8rem;

    color: #fff;

    border: .1rem solid #fff;

    border-radius: 1.8rem;

    padding: 0 1.5rem;

    text-decoration: none;

}

.page44 .no1 .con_bott{

    padding-top: .8rem;

    background-color: #fff;!important

}

.page44 .no1 .con_bott div{

    width: 50%;

    box-sizing: border-box;

    float: left;

    background-color: #fff;!important

}

.h_65{

    height: 6.5rem;

}

.page44 .no1 h4{

    height: 2.6rem;

    line-height: 2.6rem;

    font-size: 1.4rem;

    background-color: #fff;!important

    color: #999;

}

.page44 .no1 .bott{

    height: 3rem;

    line-height: 2rem;

    background-color: #fff;!important

}

.fc_orange{

    color: #f60;

}

.airal{

    font-family: Arial;

}

.fs_18{

    font-size: 1.8rem;

}

.page44 .no1 .con_bott .r{

    padding-left: 1.5rem;

}

.border_l{

    border-left: 1px solid #e1e1e1;

}

.page44 .btn_box .l, .page44 .btn_box .r{

    width: 45%;

}

.btn_white{

    height: 4.4rem;

    font-size: 1.8rem;

    color: #fff;

    border: .1rem solid #e1e1e1;

    color: #0697da;

    background: #fff;

    cursor: pointer;

    border-radius: 8px;

}

.page44 .btn_box .l{

    margin-right: 5%;

}

.mb_40{

    margin-bottom: 4rem;

}

.pro_table div{

    background-color: #fff;!important

}

.empty p{

    background-color: #fff;!important

}

.empty .img{

    display: inline-block;

    width: 5rem;

    height: 5rem;

    overflow: hidden;

    background: url(../../img/mobile/empty.png) center no-repeat;

    background-size: contain;

}



.module_list .li{

    height: 4.4rem;

    line-height: 4.4rem;

}

.module_list .th{

    position: absolute;

    left: 0;

    top: 0;

    height: 4.4rem;

    line-height: 4.4rem;

    padding-right: 3rem;

    color: #666;

}

.module_list .td_r{

    text-align: right;

}

.wap_main{

    width: 100%;

    font-size: 1.6rem;

}

.pr_40{

    padding-right: 4rem;

}

.ml_15{

    margin-left: 1.5rem;

}

.module_list .li, .module_list .li2{

    position: relative;

}

.pt_10{

    padding-top: 1rem;

}

.more_ico .td, .more_ico .td_r{

    padding-right: 4rem;

}

.more_ico{

    background: url(../../img/mobile/ico_more.png) right center no-repeat;

}

.module_list div{

    background-color: #fff;!important

}

.module_list .li:last-child{

    border-bottom: none !important;

}

.btn_border_red{

    border: .1rem solid #ff4444 !important;

    color: #ff4444;

}

.max_64{

    margin: 0 auto;

    max-width: 64rem;

}

.jk_perfectDataCom{

    width: 92%;

    margin: 0 auto;

    padding: 1rem 0;

    box-sizing: border-box;

    position: relative;

     height: 8.1rem;

}

.jk_A_BASE_INF_icon{

    width: 72px;

    height: 72px;

    background: url(../../img/mobile/BASE_INF_icon.png) no-repeat;

    transform: scale(.7);

    -webkit-transform: scale(.7);

    -moz-transform: scale(.7);

    -ms-scroll-translation: scale(.7);

    background-size: cover;

}

.jk_B_QUALIFY_VERIFY_icon{

    width: 72px;

    height: 72px;

    background: url(../../img/mobile/QUALIFY_VERIFY_icon.png) no-repeat;

    transform: scale(.7);

    -webkit-transform: scale(.7);

    -moz-transform: scale(.7);

    -ms-scroll-translation: scale(.7);

    background-size: cover;

}

.jk_C_UPLOAD_PIC_icon{

    width: 72px;

    height: 72px;

    background: url(../../img/mobile/UPLOAD_PIC_icon.png) no-repeat;

    transform: scale(.7);

    -webkit-transform: scale(.7);

    -moz-transform: scale(.7);

    -ms-scroll-translation: scale(.7);

    background-size: cover;

}

.jk_D_BANK_CARD_icon{

    width: 72px;

    height: 66px;

    background: url(../../img/mobile/BANK_CARD_icon.png) no-repeat;

    transform: scale(.7);

    -webkit-transform: scale(.7);

    -moz-transform: scale(.7);

    -ms-scroll-translation: scale(.7);

}

.jk_perfectDataCom>article{

    width: 65%;

}

.fs15{

    font-size: 1.5rem;

}

.jk_perfectDataCom article>div{

    font-size: 1rem;

    color: #999;

    line-height: 3rem;

    margin-right: 10px;

}

.jk_perfectDataCom article>div >i{

    -webkit-transform: scale(1.1);

    -moz-transform: scale(1.1);

    -ms-transform: scale(1.1);

    transform: scale(1.1);

    position: relative;

    top: 2px;

    left: 3px;

}

.jk_icon_0{

    width: 1.25rem;

    height: 1.25rem;

    background: url(../../img/mobile/unfinish.png) no-repeat top left;

    transform: scale(1.5);

    -webkit-transform: scale(1.5);

    -ms-transform: scale(1.5);

    -o-transform: scale(1.5);

    background-size: cover;

    display: inline-block;

}

.mt_1{

    margin-top: 1rem;

}

.ml_1{

    margin-left: 1rem;

}

.jk_perfectSpan{

    font-size: 1.4rem;

    position: absolute;

    top: 50%;

    margin-top: -2.5rem;

    right: 0;

}

.fc_3{

    color: #333;

}

.fc_9{

    color: #999;

}

.f_status_0{

    color: #3c9ef1;

}

.f_status_1{

    color: #999;

}

.jk_icon_1{

    width: 1.22rem;

    height: 1.22rem;

    background: url(../../img/mobile/suess.png) no-repeat top left;

    transform: scale(1.5);

    -webkit-transform: scale(1.5);

    -ms-transform: scale(1.5);

    -o-transform: scale(1.5);

    background-size: cover;

    display: inline-block;

}

.jk_pCom{

    padding-left: 1.6rem;

    box-sizing: border-box;

    font-size: 1.4rem;

    height: 3.3rem;

    line-height: 3.3rem;

}

.borrowInfo{

    padding-top: 1rem;

    line-height: 2.4rem;

    font-size: 1.4rem;

    background-color: #fff;

    border-top: 1px solid #f4f4f4;

    border-bottom: 1px solid #f4f4f4;

    padding-bottom: 1rem;

}

.borrowInfo span{

    color: #ff4444;

    font-size: 1.8rem;

    margin: 0 0.6rem 0 0.6rem;

}

.borrowInfo .service{

    color: #ff4444;

    font-size: 1.6rem;

    margin: 0 0.6rem 0 0.6rem;

}

.jk_personData>p, .jk_personDataCom{

    height: 3.4rem;

    line-height: 3.4rem;

    color: #999;

    padding-left: 1.6rem;

    box-sizing: border-box;

}

.border_b{

    border-bottom: 1px solid #e1e1e1;

}

.jk_wrapInput{

    height: 5.4rem;

    position: relative;

    box-sizing: border-box;

    padding-left: 1.6rem;

    line-height: 5.4rem;

    background-color: #fff;

    overflow: hidden;

}

.jk_wrapInput >span:nth-of-type(1){

    float: left;

    width: 30%;

}

.jk_wrapInput >input, .jk_wrapInput >select{

    float: left;

    height: 100%;

    width: 70%;

    padding-left: 5%;

}

.selectCom{

    -webkit-appearance: none;

    border: none;

    font-size: 1.5rem;

    -webkit-tap-highlight-color: transparent;

    box-sizing: border-box;

}

.arrow_down{

    position: absolute;

    width: 1.5rem;

    height: 0.9rem;

    background: url(../../img/mobile/arrow_down.png) no-repeat top left;

    top: 50%;

    margin-top: -0.45rem;

    right: 1.6rem;

    background-size: cover;

}

select, option{

    -webkit-tap-highlight-color: transparent;

    -webkit-tap-highlight-color: rgba(255,255,255,0);

    -webkit-appearance: none;

    -moz-appearance: none;

    appearance: none;

    outline: none 0;

    background-color: transparent;

}

.input_common{

    width: 100%;

    height: 100%;

    font-size: 1.5rem;

    box-sizing: border-box;

    border-radius: 0;

}

.jk_personData>p, .jk_personDataCom{

    height: 3.4rem;

    line-height: 3.4rem;

    color: #999;

    padding-left: 1.6rem;

    box-sizing: border-box;

}

.foot_bottom{

    /* bottom: 0; */

    width: 100%;

    padding-bottom: .5rem;

    background-color: #f4f4f4;

    overflow: hidden;

    text-align: center;

}

.input_c100{

    width: 100%;

    height: 2.4rem;

    line-height: 2.4rem;

    margin: 1rem 0;

    position: relative;

    box-sizing: border-box;

    background-color: #fff;

}



.input_c100 span{

    position: absolute;

    right: 0;

    top: 0;

    font-size: 1.4rem;

    margin-right: 1rem;

}

.foot_bottom .ft_r{

    width: 30%;

}

.fr{

    float: right;

}

.foot_bottom .ft_l{

    width: 85%;

    margin-left: 7%;

    border: 1px solid #e1e1e1;

    border-radius: 5px;

    box-sizing: border-box;

    text-align: center;

    background-color: #fff;

    overflow: hidden;

}

.msg_box span{

    font-size: 0.8rem;

    position: absolute;

    top: 0;

    left: 2.1rem;

    color: #fff;

    background-color: red;

    height: 1.2rem;

    line-height: 1.2rem;

    border-radius: 22px;

    width: 1.2rem;

}

/*待审核*/

.borrow-status0{

    position: absolute;

    top: 4rem;

    right: 2rem;

    border: 1px solid #666;

    line-height: 4rem;

    border-radius: 12rem;

    width: 4rem;

    text-align: center;

    font-size: 1rem;

    color: #666;

}

/*已还清*/

.borrow-status1{

    position: absolute;

    top: 4rem;

    right: 2rem;

    border: 1px solid green;

    line-height: 4rem;

    border-radius: 12rem;

    width: 4rem;

    text-align: center;

    font-size: 1rem;

    color: green;

}

/*失败*/

.borrow-status2{

    position: absolute;

    top: 4rem;

    right: 2rem;

    border: 1px solid red;

    line-height: 4rem;

    border-radius: 12rem;

    width: 4rem;

    text-align: center;

    font-size: 1rem;

    color: red;

}

/*待还款*/

.borrow-status3{

    position: absolute;

    top: 4rem;

    right: 2rem;

    border: 1px solid rgba(255, 177, 0, 0.82);

    line-height: 4rem;

    border-radius: 12rem;

    width: 4rem;

    text-align: center;

    font-size: 1rem;

    color: rgba(255, 177, 0, 0.82);

}

.msg_response .line1 .bt span{

    color: #d67c1c;

    font-size: 2rem;

}

.msg_response .line2 span{

    color: #d67e7e;

}

.msg_response .time{

    color: #1cb996;

    font-size: 1.5rem;

}

.loadmore{

    font-size: 2rem;

    text-align: center;

    line-height: 3.4rem;

}

