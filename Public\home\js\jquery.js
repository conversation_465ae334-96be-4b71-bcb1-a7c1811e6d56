!function(e,t){function n(){if(!b.isReady){try{w.documentElement.doScroll("left")}catch(e){return void setTimeout(n,1)}b.ready()}}function r(e,t){t.src?b.ajax({url:t.src,async:!1,dataType:"script"}):b.globalEval(t.text||t.textContent||t.innerHTML||""),t.parentNode&&t.parentNode.removeChild(t)}function i(e,n,r,o,a,s){var l=e.length;if("object"==typeof n){for(var c in n)i(e,c,n[c],o,a,r);return e}if(r!==t){o=!s&&o&&b.isFunction(r);for(var u=0;u<l;u++)a(e[u],n,o?r.call(e[u],u,a(e[u],n)):r,s);return e}return l?a(e[0],n):t}function o(){return(new Date).getTime()}function a(){return!1}function s(){return!0}function l(e,t,n){return n[0].type=e,b.event.handle.apply(t,n)}function c(e){var t,n,r,i,o,a,s,l,c=[],u=[],f=arguments,d=b.data(this,"events");if(e.liveFired!==this&&d&&d.live&&(!e.button||"click"!==e.type)){e.liveFired=this;var p=d.live.slice(0);for(a=0;a<p.length;a++)i=p[a],i.origType.replace(V,"")===e.type?u.push(i.selector):p.splice(a--,1);for(r=b(e.target).closest(u,e.currentTarget),s=0,l=r.length;s<l;s++)for(a=0;a<p.length;a++)i=p[a],r[s].selector===i.selector&&(o=r[s].elem,n=null,"mouseenter"!==i.preType&&"mouseleave"!==i.preType||(n=b(e.relatedTarget).closest(i.selector)[0]),n&&n===o||c.push({elem:o,handleObj:i}));for(s=0,l=c.length;s<l;s++)if(r=c[s],e.currentTarget=r.elem,e.data=r.handleObj.data,e.handleObj=r.handleObj,r.handleObj.origHandler.apply(r.elem,f)===!1){t=!1;break}return t}}function u(e,t){return"live."+(e&&"*"!==e?e+".":"")+t.replace(/\./g,"`").replace(/ /g,"&")}function f(e){return!e||!e.parentNode||11===e.parentNode.nodeType}function d(e,t){var n=0;t.each(function(){if(this.nodeName===(e[n]&&e[n].nodeName)){var t=b.data(e[n++]),r=b.data(this,t),i=t&&t.events;if(i){delete r.handle,r.events={};for(var o in i)for(var a in i[o])b.event.add(this,o,i[o][a],i[o][a].data)}}})}function p(e,t,n){var r,i,o,a=t&&t[0]?t[0].ownerDocument||t[0]:w;return 1===e.length&&"string"==typeof e[0]&&e[0].length<512&&a===w&&!he.test(e[0])&&(b.support.checkClone||!me.test(e[0]))&&(i=!0,o=b.fragments[e[0]],o&&1!==o&&(r=o)),r||(r=a.createDocumentFragment(),b.clean(e,a,r,n)),i&&(b.fragments[e[0]]=o?r:1),{fragment:r,cacheable:i}}function h(e,t){var n={};return b.each(Ue.concat.apply([],Ue.slice(0,t)),function(){n[this]=e}),n}function m(e){return"scrollTo"in e&&e.document?e:9===e.nodeType&&(e.defaultView||e.parentWindow)}var v,g,y,b=function(e,t){return new b.fn.init(e,t)},x=e.jQuery,T=e.$,w=e.document,N=/^[^<]*(<[\w\W]+>)[^>]*$|^#([\w-]+)$/,S=/^.[^:#\[\.,]*$/,E=/\S/,C=/^(\s|\u00A0)+|(\s|\u00A0)+$/g,A=/^<(\w+)\s*\/?>(?:<\/\1>)?$/,F=navigator.userAgent,j=!1,L=[],D=Object.prototype.toString,k=Object.prototype.hasOwnProperty,O=Array.prototype.push,M=Array.prototype.slice,I=Array.prototype.indexOf;b.fn=b.prototype={init:function(e,n){var r,i,o,a;if(!e)return this;if(e.nodeType)return this.context=this[0]=e,this.length=1,this;if("body"===e&&!n)return this.context=w,this[0]=w.body,this.selector="body",this.length=1,this;if("string"==typeof e){if(r=N.exec(e),!r||!r[1]&&n)return!n&&/^\w+$/.test(e)?(this.selector=e,this.context=w,e=w.getElementsByTagName(e),b.merge(this,e)):!n||n.jquery?(n||v).find(e):b(n).find(e);if(r[1])return a=n?n.ownerDocument||n:w,o=A.exec(e),o?b.isPlainObject(n)?(e=[w.createElement(o[1])],b.fn.attr.call(e,n,!0)):e=[a.createElement(o[1])]:(o=p([r[1]],[a]),e=(o.cacheable?o.fragment.cloneNode(!0):o.fragment).childNodes),b.merge(this,e);if(i=w.getElementById(r[2])){if(i.id!==r[2])return v.find(e);this.length=1,this[0]=i}return this.context=w,this.selector=e,this}return b.isFunction(e)?v.ready(e):(e.selector!==t&&(this.selector=e.selector,this.context=e.context),b.makeArray(e,this))},selector:"",jquery:"1.4.2",length:0,size:function(){return this.length},toArray:function(){return M.call(this,0)},get:function(e){return null==e?this.toArray():e<0?this.slice(e)[0]:this[e]},pushStack:function(e,t,n){var r=b();return b.isArray(e)?O.apply(r,e):b.merge(r,e),r.prevObject=this,r.context=this.context,"find"===t?r.selector=this.selector+(this.selector?" ":"")+n:t&&(r.selector=this.selector+"."+t+"("+n+")"),r},each:function(e,t){return b.each(this,e,t)},ready:function(e){return b.bindReady(),b.isReady?e.call(w,b):L&&L.push(e),this},eq:function(e){return e===-1?this.slice(e):this.slice(e,+e+1)},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},slice:function(){return this.pushStack(M.apply(this,arguments),"slice",M.call(arguments).join(","))},map:function(e){return this.pushStack(b.map(this,function(t,n){return e.call(t,n,t)}))},end:function(){return this.prevObject||b(null)},push:O,sort:[].sort,splice:[].splice},b.fn.init.prototype=b.fn,b.extend=b.fn.extend=function(){var e,n,r,i,o=arguments[0]||{},a=1,s=arguments.length,l=!1;for("boolean"==typeof o&&(l=o,o=arguments[1]||{},a=2),"object"==typeof o||b.isFunction(o)||(o={}),s===a&&(o=this,--a);a<s;a++)if(null!=(e=arguments[a]))for(n in e)if(r=o[n],i=e[n],o!==i)if(l&&i&&(b.isPlainObject(i)||b.isArray(i))){var c=r&&(b.isPlainObject(r)||b.isArray(r))?r:b.isArray(i)?[]:{};o[n]=b.extend(l,c,i)}else i!==t&&(o[n]=i);return o},b.extend({noConflict:function(t){return e.$=T,t&&(e.jQuery=x),b},isReady:!1,ready:function(){if(!b.isReady){if(!w.body)return setTimeout(b.ready,13);if(b.isReady=!0,L){for(var e,t=0;e=L[t++];)e.call(w,b);L=null}b.fn.triggerHandler&&b(w).triggerHandler("ready")}},bindReady:function(){if(!j){if(j=!0,"complete"===w.readyState)return b.ready();if(w.addEventListener)w.addEventListener("DOMContentLoaded",y,!1),e.addEventListener("load",b.ready,!1);else if(w.attachEvent){w.attachEvent("onreadystatechange",y),e.attachEvent("onload",b.ready);var t=!1;try{t=null==e.frameElement}catch(r){}w.documentElement.doScroll&&t&&n()}}},isFunction:function(e){return"[object Function]"===D.call(e)},isArray:function(e){return"[object Array]"===D.call(e)},isPlainObject:function(e){if(!e||"[object Object]"!==D.call(e)||e.nodeType||e.setInterval)return!1;if(e.constructor&&!k.call(e,"constructor")&&!k.call(e.constructor.prototype,"isPrototypeOf"))return!1;var n;for(n in e);return n===t||k.call(e,n)},isEmptyObject:function(e){for(var t in e)return!1;return!0},error:function(e){throw e},parseJSON:function(t){return"string"==typeof t&&t?(t=b.trim(t),/^[\],:{}\s]*$/.test(t.replace(/\\(?:["\\\/bfnrt]|u[0-9a-fA-F]{4})/g,"@").replace(/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,"]").replace(/(?:^|:|,)(?:\s*\[)+/g,""))?e.JSON&&e.JSON.parse?e.JSON.parse(t):new Function("return "+t)():void b.error("Invalid JSON: "+t)):null},noop:function(){},globalEval:function(e){if(e&&E.test(e)){var t=w.getElementsByTagName("head")[0]||w.documentElement,n=w.createElement("script");n.type="text/javascript",b.support.scriptEval?n.appendChild(w.createTextNode(e)):n.text=e,t.insertBefore(n,t.firstChild),t.removeChild(n)}},nodeName:function(e,t){return e.nodeName&&e.nodeName.toUpperCase()===t.toUpperCase()},each:function(e,n,r){var i,o=0,a=e.length,s=a===t||b.isFunction(e);if(r)if(s){for(i in e)if(n.apply(e[i],r)===!1)break}else for(;o<a&&n.apply(e[o++],r)!==!1;);else if(s){for(i in e)if(n.call(e[i],i,e[i])===!1)break}else for(var l=e[0];o<a&&n.call(l,o,l)!==!1;l=e[++o]);return e},trim:function(e){return(e||"").replace(C,"")},makeArray:function(e,t){var n=t||[];return null!=e&&(null==e.length||"string"==typeof e||b.isFunction(e)||"function"!=typeof e&&e.setInterval?O.call(n,e):b.merge(n,e)),n},inArray:function(e,t){if(t.indexOf)return t.indexOf(e);for(var n=0,r=t.length;n<r;n++)if(t[n]===e)return n;return-1},merge:function(e,n){var r=e.length,i=0;if("number"==typeof n.length)for(var o=n.length;i<o;i++)e[r++]=n[i];else for(;n[i]!==t;)e[r++]=n[i++];return e.length=r,e},grep:function(e,t,n){for(var r=[],i=0,o=e.length;i<o;i++)!n!=!t(e[i],i)&&r.push(e[i]);return r},map:function(e,t,n){for(var r,i=[],o=0,a=e.length;o<a;o++)r=t(e[o],o,n),null!=r&&(i[i.length]=r);return i.concat.apply([],i)},guid:1,proxy:function(e,n,r){return 2===arguments.length&&("string"==typeof n?(r=e,e=r[n],n=t):n&&!b.isFunction(n)&&(r=n,n=t)),!n&&e&&(n=function(){return e.apply(r||this,arguments)}),e&&(n.guid=e.guid=e.guid||n.guid||b.guid++),n},uaMatch:function(e){e=e.toLowerCase();var t=/(webkit)[ \/]([\w.]+)/.exec(e)||/(opera)(?:.*version)?[ \/]([\w.]+)/.exec(e)||/(msie) ([\w.]+)/.exec(e)||!/compatible/.test(e)&&/(mozilla)(?:.*? rv:([\w.]+))?/.exec(e)||[];return{browser:t[1]||"",version:t[2]||"0"}},browser:{}}),g=b.uaMatch(F),g.browser&&(b.browser[g.browser]=!0,b.browser.version=g.version),b.browser.webkit&&(b.browser.safari=!0),I&&(b.inArray=function(e,t){return I.call(t,e)}),v=b(w),w.addEventListener?y=function(){w.removeEventListener("DOMContentLoaded",y,!1),b.ready()}:w.attachEvent&&(y=function(){"complete"===w.readyState&&(w.detachEvent("onreadystatechange",y),b.ready())}),function(){b.support={};var t=w.documentElement,n=w.createElement("script"),r=w.createElement("div"),i="script"+o();r.style.display="none",r.innerHTML="   <link/><table></table><a href='/a' style='color:red;float:left;opacity:.55;'>a</a><input type='checkbox'/>";var a=r.getElementsByTagName("*"),s=r.getElementsByTagName("a")[0];if(a&&a.length&&s){b.support={leadingWhitespace:3===r.firstChild.nodeType,tbody:!r.getElementsByTagName("tbody").length,htmlSerialize:!!r.getElementsByTagName("link").length,style:/red/.test(s.getAttribute("style")),hrefNormalized:"/a"===s.getAttribute("href"),opacity:/^0.55$/.test(s.style.opacity),cssFloat:!!s.style.cssFloat,checkOn:"on"===r.getElementsByTagName("input")[0].value,optSelected:w.createElement("select").appendChild(w.createElement("option")).selected,parentNode:null===r.removeChild(r.appendChild(w.createElement("div"))).parentNode,deleteExpando:!0,checkClone:!1,scriptEval:!1,noCloneEvent:!0,boxModel:null},n.type="text/javascript";try{n.appendChild(w.createTextNode("window."+i+"=1;"))}catch(l){}t.insertBefore(n,t.firstChild),e[i]&&(b.support.scriptEval=!0,delete e[i]);try{delete n.test}catch(l){b.support.deleteExpando=!1}t.removeChild(n),r.attachEvent&&r.fireEvent&&(r.attachEvent("onclick",function f(){b.support.noCloneEvent=!1,r.detachEvent("onclick",f)}),r.cloneNode(!0).fireEvent("onclick")),r=w.createElement("div"),r.innerHTML="<input type='radio' name='radiotest' checked='checked'/>";var c=w.createDocumentFragment();c.appendChild(r.firstChild),b.support.checkClone=c.cloneNode(!0).cloneNode(!0).lastChild.checked,b(function(){var e=w.createElement("div");e.style.width=e.style.paddingLeft="1px",w.body.appendChild(e),b.boxModel=b.support.boxModel=2===e.offsetWidth,w.body.removeChild(e).style.display="none",e=null});var u=function(e){var t=w.createElement("div");e="on"+e;var n=e in t;return n||(t.setAttribute(e,"return;"),n="function"==typeof t[e]),t=null,n};b.support.submitBubbles=u("submit"),b.support.changeBubbles=u("change"),t=n=r=a=s=null}}(),b.props={"for":"htmlFor","class":"className",readonly:"readOnly",maxlength:"maxLength",cellspacing:"cellSpacing",rowspan:"rowSpan",colspan:"colSpan",tabindex:"tabIndex",usemap:"useMap",frameborder:"frameBorder"};var B="jQuery"+o(),P=0,q={};b.extend({cache:{},expando:B,noData:{embed:!0,object:!0,applet:!0},data:function(n,r,i){if(!n.nodeName||!b.noData[n.nodeName.toLowerCase()]){n=n==e?q:n;var o,a=n[B],s=b.cache;return a||"string"!=typeof r||i!==t?(a||(a=++P),"object"==typeof r?(n[B]=a,o=s[a]=b.extend(!0,{},r)):s[a]||(n[B]=a,s[a]={}),o=s[a],i!==t&&(o[r]=i),"string"==typeof r?o[r]:o):null}},removeData:function(t,n){if(!t.nodeName||!b.noData[t.nodeName.toLowerCase()]){t=t==e?q:t;var r=t[B],i=b.cache,o=i[r];n?o&&(delete o[n],b.isEmptyObject(o)&&b.removeData(t)):(b.support.deleteExpando?delete t[b.expando]:t.removeAttribute&&t.removeAttribute(b.expando),delete i[r])}}}),b.fn.extend({data:function(e,n){if("undefined"==typeof e&&this.length)return b.data(this[0]);if("object"==typeof e)return this.each(function(){b.data(this,e)});var r=e.split(".");if(r[1]=r[1]?"."+r[1]:"",n===t){var i=this.triggerHandler("getData"+r[1]+"!",[r[0]]);return i===t&&this.length&&(i=b.data(this[0],e)),i===t&&r[1]?this.data(r[0]):i}return this.trigger("setData"+r[1]+"!",[r[0],n]).each(function(){b.data(this,e,n)})},removeData:function(e){return this.each(function(){b.removeData(this,e)})}}),b.extend({queue:function(e,t,n){if(e){t=(t||"fx")+"queue";var r=b.data(e,t);return n?(!r||b.isArray(n)?r=b.data(e,t,b.makeArray(n)):r.push(n),r):r||[]}},dequeue:function(e,t){t=t||"fx";var n=b.queue(e,t),r=n.shift();"inprogress"===r&&(r=n.shift()),r&&("fx"===t&&n.unshift("inprogress"),r.call(e,function(){b.dequeue(e,t)}))}}),b.fn.extend({queue:function(e,n){return"string"!=typeof e&&(n=e,e="fx"),n===t?b.queue(this[0],e):this.each(function(t,r){var i=b.queue(this,e,n);"fx"===e&&"inprogress"!==i[0]&&b.dequeue(this,e)})},dequeue:function(e){return this.each(function(){b.dequeue(this,e)})},delay:function(e,t){return e=b.fx?b.fx.speeds[e]||e:e,t=t||"fx",this.queue(t,function(){var n=this;setTimeout(function(){b.dequeue(n,t)},e)})},clearQueue:function(e){return this.queue(e||"fx",[])}});var H=/[\n\t]/g,R=/\s+/,$=/\r/g,_=/href|src|style/,z=/(button|input)/i,W=/(button|input|object|select|textarea)/i,X=/^(a|area)$/i,U=/radio|checkbox/;b.fn.extend({attr:function(e,t){return i(this,e,t,!0,b.attr)},removeAttr:function(e,t){return this.each(function(){b.attr(this,e,""),1===this.nodeType&&this.removeAttribute(e)})},addClass:function(e){if(b.isFunction(e))return this.each(function(t){var n=b(this);n.addClass(e.call(this,t,n.attr("class")))});if(e&&"string"==typeof e)for(var t=(e||"").split(R),n=0,r=this.length;n<r;n++){var i=this[n];if(1===i.nodeType)if(i.className){for(var o=" "+i.className+" ",a=i.className,s=0,l=t.length;s<l;s++)o.indexOf(" "+t[s]+" ")<0&&(a+=" "+t[s]);i.className=b.trim(a)}else i.className=e}return this},removeClass:function(e){if(b.isFunction(e))return this.each(function(t){var n=b(this);n.removeClass(e.call(this,t,n.attr("class")))});if(e&&"string"==typeof e||e===t)for(var n=(e||"").split(R),r=0,i=this.length;r<i;r++){var o=this[r];if(1===o.nodeType&&o.className)if(e){for(var a=(" "+o.className+" ").replace(H," "),s=0,l=n.length;s<l;s++)a=a.replace(" "+n[s]+" "," ");o.className=b.trim(a)}else o.className=""}return this},toggleClass:function(e,t){var n=typeof e,r="boolean"==typeof t;return b.isFunction(e)?this.each(function(n){var r=b(this);r.toggleClass(e.call(this,n,r.attr("class"),t),t)}):this.each(function(){if("string"===n)for(var i,o=0,a=b(this),s=t,l=e.split(R);i=l[o++];)s=r?s:!a.hasClass(i),a[s?"addClass":"removeClass"](i);else"undefined"!==n&&"boolean"!==n||(this.className&&b.data(this,"__className__",this.className),this.className=this.className||e===!1?"":b.data(this,"__className__")||"")})},hasClass:function(e){for(var t=" "+e+" ",n=0,r=this.length;n<r;n++)if((" "+this[n].className+" ").replace(H," ").indexOf(t)>-1)return!0;return!1},val:function(e){if(e===t){var n=this[0];if(n){if(b.nodeName(n,"option"))return(n.attributes.value||{}).specified?n.value:n.text;if(b.nodeName(n,"select")){var r=n.selectedIndex,i=[],o=n.options,a="select-one"===n.type;if(r<0)return null;for(var s=a?r:0,l=a?r+1:o.length;s<l;s++){var c=o[s];if(c.selected){if(e=b(c).val(),a)return e;i.push(e)}}return i}return U.test(n.type)&&!b.support.checkOn?null===n.getAttribute("value")?"on":n.value:(n.value||"").replace($,"")}return t}var u=b.isFunction(e);return this.each(function(t){var n=b(this),r=e;if(1===this.nodeType)if(u&&(r=e.call(this,t,n.val())),"number"==typeof r&&(r+=""),b.isArray(r)&&U.test(this.type))this.checked=b.inArray(n.val(),r)>=0;else if(b.nodeName(this,"select")){var i=b.makeArray(r);b("option",this).each(function(){this.selected=b.inArray(b(this).val(),i)>=0}),i.length||(this.selectedIndex=-1)}else this.value=r})}}),b.extend({attrFn:{val:!0,css:!0,html:!0,text:!0,data:!0,width:!0,height:!0,offset:!0},attr:function(e,n,r,i){if(!e||3===e.nodeType||8===e.nodeType)return t;if(i&&n in b.attrFn)return b(e)[n](r);var o=1!==e.nodeType||!b.isXMLDoc(e),a=r!==t;if(n=o&&b.props[n]||n,1===e.nodeType){var s=_.test(n);if("selected"===n&&!b.support.optSelected){var l=e.parentNode;l&&(l.selectedIndex,l.parentNode&&l.parentNode.selectedIndex)}if(n in e&&o&&!s){if(a&&("type"===n&&z.test(e.nodeName)&&e.parentNode&&b.error("type property can't be changed"),e[n]=r),b.nodeName(e,"form")&&e.getAttributeNode(n))return e.getAttributeNode(n).nodeValue;if("tabIndex"===n){var c=e.getAttributeNode("tabIndex");return c&&c.specified?c.value:W.test(e.nodeName)||X.test(e.nodeName)&&e.href?0:t}return e[n]}if(!b.support.style&&o&&"style"===n)return a&&(e.style.cssText=""+r),e.style.cssText;a&&e.setAttribute(n,""+r);var u=!b.support.hrefNormalized&&o&&s?e.getAttribute(n,2):e.getAttribute(n);return null===u?t:u}return b.style(e,n,r)}});var V=/\.(.*)$/,G=function(e){return e.replace(/[^\w\s\.\|`]/g,function(e){return"\\"+e})};b.event={add:function(n,r,i,o){if(3!==n.nodeType&&8!==n.nodeType){n.setInterval&&n!==e&&!n.frameElement&&(n=e);var a,s;i.handler&&(a=i,i=a.handler),i.guid||(i.guid=b.guid++);var l=b.data(n);if(l){var c,u=l.events=l.events||{},c=l.handle;c||(l.handle=c=function(){return"undefined"==typeof b||b.event.triggered?t:b.event.handle.apply(c.elem,arguments)}),c.elem=n,r=r.split(" ");for(var f,d,p=0;f=r[p++];){s=a?b.extend({},a):{handler:i,data:o},f.indexOf(".")>-1?(d=f.split("."),f=d.shift(),s.namespace=d.slice(0).sort().join(".")):(d=[],s.namespace=""),s.type=f,s.guid=i.guid;var h=u[f],m=b.event.special[f]||{};h||(h=u[f]=[],m.setup&&m.setup.call(n,o,d,c)!==!1||(n.addEventListener?n.addEventListener(f,c,!1):n.attachEvent&&n.attachEvent("on"+f,c))),m.add&&(m.add.call(n,s),s.handler.guid||(s.handler.guid=i.guid)),h.push(s),b.event.global[f]=!0}n=null}}},global:{},remove:function(e,t,n,r){if(3!==e.nodeType&&8!==e.nodeType){var i,o,a,s,l,c,u,f,d,p=0,h=b.data(e),m=h&&h.events;if(h&&m)if(t&&t.type&&(n=t.handler,t=t.type),!t||"string"==typeof t&&"."===t.charAt(0)){t=t||"";for(o in m)b.event.remove(e,o+t)}else{for(t=t.split(" ");o=t[p++];)if(d=o,f=null,a=o.indexOf(".")<0,s=[],a||(s=o.split("."),o=s.shift(),l=new RegExp("(^|\\.)"+b.map(s.slice(0).sort(),G).join("\\.(?:.*\\.)?")+"(\\.|$)")),u=m[o])if(n){c=b.event.special[o]||{};for(var v=r||0;v<u.length&&(f=u[v],n.guid!==f.guid||((a||l.test(f.namespace))&&(null==r&&u.splice(v--,1),c.remove&&c.remove.call(e,f)),null==r));v++);(0===u.length||null!=r&&1===u.length)&&(c.teardown&&c.teardown.call(e,s)!==!1||Y(e,o,h.handle),i=null,delete m[o])}else for(var v=0;v<u.length;v++)f=u[v],(a||l.test(f.namespace))&&(b.event.remove(e,d,f.handler,v),u.splice(v--,1));if(b.isEmptyObject(m)){var g=h.handle;g&&(g.elem=null),delete h.events,delete h.handle,b.isEmptyObject(h)&&b.removeData(e)}}}},trigger:function(e,n,r){var i=e.type||e,o=arguments[3];if(!o){if(e="object"==typeof e?e[B]?e:b.extend(b.Event(i),e):b.Event(i),i.indexOf("!")>=0&&(e.type=i=i.slice(0,-1),e.exclusive=!0),r||(e.stopPropagation(),b.event.global[i]&&b.each(b.cache,function(){this.events&&this.events[i]&&b.event.trigger(e,n,this.handle.elem)})),!r||3===r.nodeType||8===r.nodeType)return t;e.result=t,e.target=r,n=b.makeArray(n),n.unshift(e)}e.currentTarget=r;var a=b.data(r,"handle");a&&a.apply(r,n);var s=r.parentNode||r.ownerDocument;try{r&&r.nodeName&&b.noData[r.nodeName.toLowerCase()]||r["on"+i]&&r["on"+i].apply(r,n)===!1&&(e.result=!1)}catch(l){}if(!e.isPropagationStopped()&&s)b.event.trigger(e,n,s,!0);else if(!e.isDefaultPrevented()){var c,u=e.target,f=b.nodeName(u,"a")&&"click"===i,d=b.event.special[i]||{};if(!(d._default&&d._default.call(r,e)!==!1||f||u&&u.nodeName&&b.noData[u.nodeName.toLowerCase()])){try{u[i]&&(c=u["on"+i],c&&(u["on"+i]=null),b.event.triggered=!0,u[i]())}catch(l){}c&&(u["on"+i]=c),b.event.triggered=!1}}},handle:function(n){var r,i,o,a,s;n=arguments[0]=b.event.fix(n||e.event),n.currentTarget=this,r=n.type.indexOf(".")<0&&!n.exclusive,r||(o=n.type.split("."),n.type=o.shift(),a=new RegExp("(^|\\.)"+o.slice(0).sort().join("\\.(?:.*\\.)?")+"(\\.|$)"));var s=b.data(this,"events"),i=s[n.type];if(s&&i){i=i.slice(0);for(var l=0,c=i.length;l<c;l++){var u=i[l];if(r||a.test(u.namespace)){n.handler=u.handler,n.data=u.data,n.handleObj=u;var f=u.handler.apply(this,arguments);if(f!==t&&(n.result=f,f===!1&&(n.preventDefault(),n.stopPropagation())),n.isImmediatePropagationStopped())break}}}return n.result},props:"altKey attrChange attrName bubbles button cancelable charCode clientX clientY ctrlKey currentTarget data detail eventPhase fromElement handler keyCode layerX layerY metaKey newValue offsetX offsetY originalTarget pageX pageY prevValue relatedNode relatedTarget screenX screenY shiftKey srcElement target toElement view wheelDelta which".split(" "),fix:function(e){if(e[B])return e;var n=e;e=b.Event(n);for(var r,i=this.props.length;i;)r=this.props[--i],e[r]=n[r];if(e.target||(e.target=e.srcElement||w),3===e.target.nodeType&&(e.target=e.target.parentNode),!e.relatedTarget&&e.fromElement&&(e.relatedTarget=e.fromElement===e.target?e.toElement:e.fromElement),null==e.pageX&&null!=e.clientX){var o=w.documentElement,a=w.body;e.pageX=e.clientX+(o&&o.scrollLeft||a&&a.scrollLeft||0)-(o&&o.clientLeft||a&&a.clientLeft||0),e.pageY=e.clientY+(o&&o.scrollTop||a&&a.scrollTop||0)-(o&&o.clientTop||a&&a.clientTop||0)}return!e.which&&(e.charCode||0===e.charCode?e.charCode:e.keyCode)&&(e.which=e.charCode||e.keyCode),!e.metaKey&&e.ctrlKey&&(e.metaKey=e.ctrlKey),e.which||e.button===t||(e.which=1&e.button?1:2&e.button?3:4&e.button?2:0),e},guid:1e8,proxy:b.proxy,special:{ready:{setup:b.bindReady,teardown:b.noop},live:{add:function(e){b.event.add(this,e.origType,b.extend({},e,{handler:c}))},remove:function(e){var t=!0,n=e.origType.replace(V,"");b.each(b.data(this,"events").live||[],function(){if(n===this.origType.replace(V,""))return t=!1,!1}),t&&b.event.remove(this,e.origType,c)}},beforeunload:{setup:function(e,t,n){return this.setInterval&&(this.onbeforeunload=n),!1},teardown:function(e,t){this.onbeforeunload===t&&(this.onbeforeunload=null)}}}};var Y=w.removeEventListener?function(e,t,n){e.removeEventListener(t,n,!1)}:function(e,t,n){e.detachEvent("on"+t,n)};b.Event=function(e){return this.preventDefault?(e&&e.type?(this.originalEvent=e,this.type=e.type):this.type=e,this.timeStamp=o(),void(this[B]=!0)):new b.Event(e)},b.Event.prototype={preventDefault:function(){this.isDefaultPrevented=s;var e=this.originalEvent;e&&(e.preventDefault&&e.preventDefault(),e.returnValue=!1)},stopPropagation:function(){this.isPropagationStopped=s;var e=this.originalEvent;e&&(e.stopPropagation&&e.stopPropagation(),e.cancelBubble=!0)},stopImmediatePropagation:function(){this.isImmediatePropagationStopped=s,this.stopPropagation()},isDefaultPrevented:a,isPropagationStopped:a,isImmediatePropagationStopped:a};var K=function(e){var t=e.relatedTarget;try{for(;t&&t!==this;)t=t.parentNode;t!==this&&(e.type=e.data,b.event.handle.apply(this,arguments))}catch(n){}},J=function(e){e.type=e.data,b.event.handle.apply(this,arguments)};if(b.each({mouseenter:"mouseover",mouseleave:"mouseout"},function(e,t){b.event.special[e]={setup:function(n){b.event.add(this,t,n&&n.selector?J:K,e)},teardown:function(e){b.event.remove(this,t,e&&e.selector?J:K)}}}),b.support.submitBubbles||(b.event.special.submit={setup:function(e,t){return"form"!==this.nodeName.toLowerCase()&&(b.event.add(this,"click.specialSubmit",function(e){var t=e.target,n=t.type;if(("submit"===n||"image"===n)&&b(t).closest("form").length)return l("submit",this,arguments)}),void b.event.add(this,"keypress.specialSubmit",function(e){var t=e.target,n=t.type;if(("text"===n||"password"===n)&&b(t).closest("form").length&&13===e.keyCode)return l("submit",this,arguments)}))},teardown:function(e){b.event.remove(this,".specialSubmit")}}),!b.support.changeBubbles){var Q,Z=/textarea|input|select/i,ee=function(e){var t=e.type,n=e.value;return"radio"===t||"checkbox"===t?n=e.checked:"select-multiple"===t?n=e.selectedIndex>-1?b.map(e.options,function(e){return e.selected}).join("-"):"":"select"===e.nodeName.toLowerCase()&&(n=e.selectedIndex),n},te=function(e){var n,r,i=e.target;if(Z.test(i.nodeName)&&!i.readOnly&&(n=b.data(i,"_change_data"),r=ee(i),"focusout"===e.type&&"radio"===i.type||b.data(i,"_change_data",r),n!==t&&r!==n))return null!=n||r?(e.type="change",b.event.trigger(e,arguments[1],i)):void 0};b.event.special.change={filters:{focusout:te,click:function(e){var t=e.target,n=t.type;if("radio"===n||"checkbox"===n||"select"===t.nodeName.toLowerCase())return te.call(this,e)},keydown:function(e){var t=e.target,n=t.type;if(13===e.keyCode&&"textarea"!==t.nodeName.toLowerCase()||32===e.keyCode&&("checkbox"===n||"radio"===n)||"select-multiple"===n)return te.call(this,e)},beforeactivate:function(e){var t=e.target;b.data(t,"_change_data",ee(t))}},setup:function(e,t){if("file"===this.type)return!1;for(var n in Q)b.event.add(this,n+".specialChange",Q[n]);return Z.test(this.nodeName)},teardown:function(e){return b.event.remove(this,".specialChange"),Z.test(this.nodeName)}},Q=b.event.special.change.filters}w.addEventListener&&b.each({focus:"focusin",blur:"focusout"},function(e,t){function n(e){return e=b.event.fix(e),e.type=t,b.event.handle.call(this,e)}b.event.special[t]={setup:function(){this.addEventListener(e,n,!0)},teardown:function(){this.removeEventListener(e,n,!0)}}}),b.each(["bind","one"],function(e,n){b.fn[n]=function(e,r,i){if("object"==typeof e){for(var o in e)this[n](o,r,e[o],i);return this}b.isFunction(r)&&(i=r,r=t);var a="one"===n?b.proxy(i,function(e){return b(this).unbind(e,a),i.apply(this,arguments)}):i;if("unload"===e&&"one"!==n)this.one(e,r,i);else for(var s=0,l=this.length;s<l;s++)b.event.add(this[s],e,a,r);return this}}),b.fn.extend({unbind:function(e,t){if("object"!=typeof e||e.preventDefault)for(var n=0,r=this.length;n<r;n++)b.event.remove(this[n],e,t);else for(var i in e)this.unbind(i,e[i]);return this},delegate:function(e,t,n,r){return this.live(t,n,r,e)},undelegate:function(e,t,n){return 0===arguments.length?this.unbind("live"):this.die(t,null,n,e)},trigger:function(e,t){return this.each(function(){b.event.trigger(e,t,this)})},triggerHandler:function(e,t){if(this[0]){var n=b.Event(e);return n.preventDefault(),n.stopPropagation(),b.event.trigger(n,t,this[0]),n.result}},toggle:function(e){for(var t=arguments,n=1;n<t.length;)b.proxy(e,t[n++]);return this.click(b.proxy(e,function(r){var i=(b.data(this,"lastToggle"+e.guid)||0)%n;return b.data(this,"lastToggle"+e.guid,i+1),r.preventDefault(),t[i].apply(this,arguments)||!1}))},hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)}});var ne={focus:"focusin",blur:"focusout",mouseenter:"mouseover",mouseleave:"mouseout"};b.each(["live","die"],function(e,n){b.fn[n]=function(e,r,i,o){var a,s,l,c,f=0,d=o||this.selector,p=o?this:b(this.context);for(b.isFunction(r)&&(i=r,r=t),e=(e||"").split(" ");null!=(a=e[f++]);)s=V.exec(a),l="",s&&(l=s[0],a=a.replace(V,"")),"hover"!==a?(c=a,"focus"===a||"blur"===a?(e.push(ne[a]+l),a+=l):a=(ne[a]||a)+l,"live"===n?p.each(function(){b.event.add(this,u(a,d),{data:r,selector:d,handler:i,origType:a,origHandler:i,preType:c})}):p.unbind(u(a,d),i)):e.push("mouseenter"+l,"mouseleave"+l);return this}}),b.each("blur focus focusin focusout load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup error".split(" "),function(e,t){b.fn[t]=function(e){return e?this.bind(t,e):this.trigger(t)},b.attrFn&&(b.attrFn[t]=!0)}),e.attachEvent&&!e.addEventListener&&e.attachEvent("onunload",function(){for(var e in b.cache)if(b.cache[e].handle)try{b.event.remove(b.cache[e].handle.elem)}catch(t){}}),function(){function e(t){for(var n,r="",i=0;t[i];i++)n=t[i],3===n.nodeType||4===n.nodeType?r+=n.nodeValue:8!==n.nodeType&&(r+=e(n.childNodes));return r}function n(e,t,n,r,i,o){for(var a=0,s=r.length;a<s;a++){var l=r[a];if(l){l=l[e];for(var c=!1;l;){if(l.sizcache===n){c=r[l.sizset];break}if(1!==l.nodeType||o||(l.sizcache=n,l.sizset=a),l.nodeName.toLowerCase()===t){c=l;break}l=l[e]}r[a]=c}}}function r(e,t,n,r,i,o){for(var a=0,s=r.length;a<s;a++){var l=r[a];if(l){l=l[e];for(var u=!1;l;){if(l.sizcache===n){u=r[l.sizset];break}if(1===l.nodeType)if(o||(l.sizcache=n,l.sizset=a),"string"!=typeof t){if(l===t){u=!0;break}}else if(c.filter(t,[l]).length>0){u=l;break}l=l[e]}r[a]=u}}}var i=/((?:\((?:\([^()]+\)|[^()]+)+\)|\[(?:\[[^[\]]*\]|['"][^'"]*['"]|[^[\]'"]+)+\]|\\.|[^ >+~,(\[\\]+)+|[>+~])(\s*,\s*)?((?:.|\r|\n)*)/g,o=0,a=Object.prototype.toString,s=!1,l=!0;[0,0].sort(function(){return l=!1,0});var c=function(e,t,n,r){n=n||[];var o=t=t||w;if(1!==t.nodeType&&9!==t.nodeType)return[];if(!e||"string"!=typeof e)return n;for(var s,l,d,h,m=[],b=!0,x=g(t),T=e;null!==(i.exec(""),s=i.exec(T));)if(T=s[3],m.push(s[1]),s[2]){h=s[3];break}if(m.length>1&&f.exec(e))if(2===m.length&&u.relative[m[0]])l=y(m[0]+m[1],t);else for(l=u.relative[m[0]]?[t]:c(m.shift(),t);m.length;)e=m.shift(),u.relative[e]&&(e+=m.shift()),l=y(e,l);else{if(!r&&m.length>1&&9===t.nodeType&&!x&&u.match.ID.test(m[0])&&!u.match.ID.test(m[m.length-1])){var N=c.find(m.shift(),t,x);t=N.expr?c.filter(N.expr,N.set)[0]:N.set[0]}if(t){var N=r?{expr:m.pop(),set:p(r)}:c.find(m.pop(),1!==m.length||"~"!==m[0]&&"+"!==m[0]||!t.parentNode?t:t.parentNode,x);for(l=N.expr?c.filter(N.expr,N.set):N.set,m.length>0?d=p(l):b=!1;m.length;){var S=m.pop(),E=S;u.relative[S]?E=m.pop():S="",null==E&&(E=t),u.relative[S](d,E,x)}}else d=m=[]}if(d||(d=l),d||c.error(S||e),"[object Array]"===a.call(d))if(b)if(t&&1===t.nodeType)for(var C=0;null!=d[C];C++)d[C]&&(d[C]===!0||1===d[C].nodeType&&v(t,d[C]))&&n.push(l[C]);else for(var C=0;null!=d[C];C++)d[C]&&1===d[C].nodeType&&n.push(l[C]);else n.push.apply(n,d);else p(d,n);return h&&(c(h,o,n,r),c.uniqueSort(n)),n};c.uniqueSort=function(e){if(m&&(s=l,e.sort(m),s))for(var t=1;t<e.length;t++)e[t]===e[t-1]&&e.splice(t--,1);return e},c.matches=function(e,t){return c(e,null,null,t)},c.find=function(e,t,n){var r,i;if(!e)return[];for(var o=0,a=u.order.length;o<a;o++){var i,s=u.order[o];if(i=u.leftMatch[s].exec(e)){var l=i[1];if(i.splice(1,1),"\\"!==l.substr(l.length-1)&&(i[1]=(i[1]||"").replace(/\\/g,""),r=u.find[s](i,t,n),null!=r)){e=e.replace(u.match[s],"");break}}}return r||(r=t.getElementsByTagName("*")),{set:r,expr:e}},c.filter=function(e,n,r,i){for(var o,a,s=e,l=[],f=n,d=n&&n[0]&&g(n[0]);e&&n.length;){for(var p in u.filter)if(null!=(o=u.leftMatch[p].exec(e))&&o[2]){var h,m,v=u.filter[p],y=o[1];if(a=!1,o.splice(1,1),"\\"===y.substr(y.length-1))continue;if(f===l&&(l=[]),u.preFilter[p])if(o=u.preFilter[p](o,f,r,l,i,d)){if(o===!0)continue}else a=h=!0;if(o)for(var b=0;null!=(m=f[b]);b++)if(m){h=v(m,o,b,f);var x=i^!!h;r&&null!=h?x?a=!0:f[b]=!1:x&&(l.push(m),a=!0)}if(h!==t){if(r||(f=l),e=e.replace(u.match[p],""),!a)return[];break}}if(e===s){if(null!=a)break;c.error(e)}s=e}return f},c.error=function(e){throw"Syntax error, unrecognized expression: "+e};var u=c.selectors={order:["ID","NAME","TAG"],match:{ID:/#((?:[\w\u00c0-\uFFFF-]|\\.)+)/,CLASS:/\.((?:[\w\u00c0-\uFFFF-]|\\.)+)/,NAME:/\[name=['"]*((?:[\w\u00c0-\uFFFF-]|\\.)+)['"]*\]/,ATTR:/\[\s*((?:[\w\u00c0-\uFFFF-]|\\.)+)\s*(?:(\S?=)\s*(['"]*)(.*?)\3|)\s*\]/,TAG:/^((?:[\w\u00c0-\uFFFF\*-]|\\.)+)/,CHILD:/:(only|nth|last|first)-child(?:\((even|odd|[\dn+-]*)\))?/,POS:/:(nth|eq|gt|lt|first|last|even|odd)(?:\((\d*)\))?(?=[^-]|$)/,PSEUDO:/:((?:[\w\u00c0-\uFFFF-]|\\.)+)(?:\((['"]?)((?:\([^\)]+\)|[^\(\)]*)+)\2\))?/},leftMatch:{},attrMap:{"class":"className","for":"htmlFor"},attrHandle:{href:function(e){return e.getAttribute("href")}},relative:{"+":function(e,t){var n="string"==typeof t,r=n&&!/\W/.test(t),i=n&&!r;r&&(t=t.toLowerCase());for(var o,a=0,s=e.length;a<s;a++)if(o=e[a]){for(;(o=o.previousSibling)&&1!==o.nodeType;);e[a]=i||o&&o.nodeName.toLowerCase()===t?o||!1:o===t}i&&c.filter(t,e,!0)},">":function(e,t){var n="string"==typeof t;if(n&&!/\W/.test(t)){t=t.toLowerCase();for(var r=0,i=e.length;r<i;r++){var o=e[r];if(o){var a=o.parentNode;e[r]=a.nodeName.toLowerCase()===t&&a}}}else{for(var r=0,i=e.length;r<i;r++){var o=e[r];o&&(e[r]=n?o.parentNode:o.parentNode===t)}n&&c.filter(t,e,!0)}},"":function(e,t,i){var a=o++,s=r;if("string"==typeof t&&!/\W/.test(t)){var l=t=t.toLowerCase();
s=n}s("parentNode",t,a,e,l,i)},"~":function(e,t,i){var a=o++,s=r;if("string"==typeof t&&!/\W/.test(t)){var l=t=t.toLowerCase();s=n}s("previousSibling",t,a,e,l,i)}},find:{ID:function(e,t,n){if("undefined"!=typeof t.getElementById&&!n){var r=t.getElementById(e[1]);return r?[r]:[]}},NAME:function(e,t){if("undefined"!=typeof t.getElementsByName){for(var n=[],r=t.getElementsByName(e[1]),i=0,o=r.length;i<o;i++)r[i].getAttribute("name")===e[1]&&n.push(r[i]);return 0===n.length?null:n}},TAG:function(e,t){return t.getElementsByTagName(e[1])}},preFilter:{CLASS:function(e,t,n,r,i,o){if(e=" "+e[1].replace(/\\/g,"")+" ",o)return e;for(var a,s=0;null!=(a=t[s]);s++)a&&(i^(a.className&&(" "+a.className+" ").replace(/[\t\n]/g," ").indexOf(e)>=0)?n||r.push(a):n&&(t[s]=!1));return!1},ID:function(e){return e[1].replace(/\\/g,"")},TAG:function(e,t){return e[1].toLowerCase()},CHILD:function(e){if("nth"===e[1]){var t=/(-?)(\d*)n((?:\+|-)?\d*)/.exec("even"===e[2]&&"2n"||"odd"===e[2]&&"2n+1"||!/\D/.test(e[2])&&"0n+"+e[2]||e[2]);e[2]=t[1]+(t[2]||1)-0,e[3]=t[3]-0}return e[0]=o++,e},ATTR:function(e,t,n,r,i,o){var a=e[1].replace(/\\/g,"");return!o&&u.attrMap[a]&&(e[1]=u.attrMap[a]),"~="===e[2]&&(e[4]=" "+e[4]+" "),e},PSEUDO:function(e,t,n,r,o){if("not"===e[1]){if(!((i.exec(e[3])||"").length>1||/^\w/.test(e[3]))){var a=c.filter(e[3],t,n,!0^o);return n||r.push.apply(r,a),!1}e[3]=c(e[3],null,null,t)}else if(u.match.POS.test(e[0])||u.match.CHILD.test(e[0]))return!0;return e},POS:function(e){return e.unshift(!0),e}},filters:{enabled:function(e){return e.disabled===!1&&"hidden"!==e.type},disabled:function(e){return e.disabled===!0},checked:function(e){return e.checked===!0},selected:function(e){return e.parentNode.selectedIndex,e.selected===!0},parent:function(e){return!!e.firstChild},empty:function(e){return!e.firstChild},has:function(e,t,n){return!!c(n[3],e).length},header:function(e){return/h\d/i.test(e.nodeName)},text:function(e){return"text"===e.type},radio:function(e){return"radio"===e.type},checkbox:function(e){return"checkbox"===e.type},file:function(e){return"file"===e.type},password:function(e){return"password"===e.type},submit:function(e){return"submit"===e.type},image:function(e){return"image"===e.type},reset:function(e){return"reset"===e.type},button:function(e){return"button"===e.type||"button"===e.nodeName.toLowerCase()},input:function(e){return/input|select|textarea|button/i.test(e.nodeName)}},setFilters:{first:function(e,t){return 0===t},last:function(e,t,n,r){return t===r.length-1},even:function(e,t){return t%2===0},odd:function(e,t){return t%2===1},lt:function(e,t,n){return t<n[3]-0},gt:function(e,t,n){return t>n[3]-0},nth:function(e,t,n){return n[3]-0===t},eq:function(e,t,n){return n[3]-0===t}},filter:{PSEUDO:function(t,n,r,i){var o=n[1],a=u.filters[o];if(a)return a(t,r,n,i);if("contains"===o)return(t.textContent||t.innerText||e([t])||"").indexOf(n[3])>=0;if("not"===o){for(var s=n[3],r=0,l=s.length;r<l;r++)if(s[r]===t)return!1;return!0}c.error("Syntax error, unrecognized expression: "+o)},CHILD:function(e,t){var n=t[1],r=e;switch(n){case"only":case"first":for(;r=r.previousSibling;)if(1===r.nodeType)return!1;if("first"===n)return!0;r=e;case"last":for(;r=r.nextSibling;)if(1===r.nodeType)return!1;return!0;case"nth":var i=t[2],o=t[3];if(1===i&&0===o)return!0;var a=t[0],s=e.parentNode;if(s&&(s.sizcache!==a||!e.nodeIndex)){var l=0;for(r=s.firstChild;r;r=r.nextSibling)1===r.nodeType&&(r.nodeIndex=++l);s.sizcache=a}var c=e.nodeIndex-o;return 0===i?0===c:c%i===0&&c/i>=0}},ID:function(e,t){return 1===e.nodeType&&e.getAttribute("id")===t},TAG:function(e,t){return"*"===t&&1===e.nodeType||e.nodeName.toLowerCase()===t},CLASS:function(e,t){return(" "+(e.className||e.getAttribute("class"))+" ").indexOf(t)>-1},ATTR:function(e,t){var n=t[1],r=u.attrHandle[n]?u.attrHandle[n](e):null!=e[n]?e[n]:e.getAttribute(n),i=r+"",o=t[2],a=t[4];return null==r?"!="===o:"="===o?i===a:"*="===o?i.indexOf(a)>=0:"~="===o?(" "+i+" ").indexOf(a)>=0:a?"!="===o?i!==a:"^="===o?0===i.indexOf(a):"$="===o?i.substr(i.length-a.length)===a:"|="===o&&(i===a||i.substr(0,a.length+1)===a+"-"):i&&r!==!1},POS:function(e,t,n,r){var i=t[2],o=u.setFilters[i];if(o)return o(e,n,t,r)}}},f=u.match.POS;for(var d in u.match)u.match[d]=new RegExp(u.match[d].source+/(?![^\[]*\])(?![^\(]*\))/.source),u.leftMatch[d]=new RegExp(/(^(?:.|\r|\n)*?)/.source+u.match[d].source.replace(/\\(\d+)/g,function(e,t){return"\\"+(t-0+1)}));var p=function(e,t){return e=Array.prototype.slice.call(e,0),t?(t.push.apply(t,e),t):e};try{Array.prototype.slice.call(w.documentElement.childNodes,0)[0].nodeType}catch(h){p=function(e,t){var n=t||[];if("[object Array]"===a.call(e))Array.prototype.push.apply(n,e);else if("number"==typeof e.length)for(var r=0,i=e.length;r<i;r++)n.push(e[r]);else for(var r=0;e[r];r++)n.push(e[r]);return n}}var m;w.documentElement.compareDocumentPosition?m=function(e,t){if(!e.compareDocumentPosition||!t.compareDocumentPosition)return e==t&&(s=!0),e.compareDocumentPosition?-1:1;var n=4&e.compareDocumentPosition(t)?-1:e===t?0:1;return 0===n&&(s=!0),n}:"sourceIndex"in w.documentElement?m=function(e,t){if(!e.sourceIndex||!t.sourceIndex)return e==t&&(s=!0),e.sourceIndex?-1:1;var n=e.sourceIndex-t.sourceIndex;return 0===n&&(s=!0),n}:w.createRange&&(m=function(e,t){if(!e.ownerDocument||!t.ownerDocument)return e==t&&(s=!0),e.ownerDocument?-1:1;var n=e.ownerDocument.createRange(),r=t.ownerDocument.createRange();n.setStart(e,0),n.setEnd(e,0),r.setStart(t,0),r.setEnd(t,0);var i=n.compareBoundaryPoints(Range.START_TO_END,r);return 0===i&&(s=!0),i}),function(){var e=w.createElement("div"),n="script"+(new Date).getTime();e.innerHTML="<a name='"+n+"'/>";var r=w.documentElement;r.insertBefore(e,r.firstChild),w.getElementById(n)&&(u.find.ID=function(e,n,r){if("undefined"!=typeof n.getElementById&&!r){var i=n.getElementById(e[1]);return i?i.id===e[1]||"undefined"!=typeof i.getAttributeNode&&i.getAttributeNode("id").nodeValue===e[1]?[i]:t:[]}},u.filter.ID=function(e,t){var n="undefined"!=typeof e.getAttributeNode&&e.getAttributeNode("id");return 1===e.nodeType&&n&&n.nodeValue===t}),r.removeChild(e),r=e=null}(),function(){var e=w.createElement("div");e.appendChild(w.createComment("")),e.getElementsByTagName("*").length>0&&(u.find.TAG=function(e,t){var n=t.getElementsByTagName(e[1]);if("*"===e[1]){for(var r=[],i=0;n[i];i++)1===n[i].nodeType&&r.push(n[i]);n=r}return n}),e.innerHTML="<a href='#'></a>",e.firstChild&&"undefined"!=typeof e.firstChild.getAttribute&&"#"!==e.firstChild.getAttribute("href")&&(u.attrHandle.href=function(e){return e.getAttribute("href",2)}),e=null}(),w.querySelectorAll&&!function(){var e=c,t=w.createElement("div");if(t.innerHTML="<p class='TEST'></p>",!t.querySelectorAll||0!==t.querySelectorAll(".TEST").length){c=function(t,n,r,i){if(n=n||w,!i&&9===n.nodeType&&!g(n))try{return p(n.querySelectorAll(t),r)}catch(o){}return e(t,n,r,i)};for(var n in e)c[n]=e[n];t=null}}(),function(){var e=w.createElement("div");e.innerHTML="<div class='test e'></div><div class='test'></div>",e.getElementsByClassName&&0!==e.getElementsByClassName("e").length&&(e.lastChild.className="e",1!==e.getElementsByClassName("e").length&&(u.order.splice(1,0,"CLASS"),u.find.CLASS=function(e,t,n){if("undefined"!=typeof t.getElementsByClassName&&!n)return t.getElementsByClassName(e[1])},e=null))}();var v=w.compareDocumentPosition?function(e,t){return!!(16&e.compareDocumentPosition(t))}:function(e,t){return e!==t&&(!e.contains||e.contains(t))},g=function(e){var t=(e?e.ownerDocument||e:0).documentElement;return!!t&&"HTML"!==t.nodeName},y=function(e,t){for(var n,r=[],i="",o=t.nodeType?[t]:t;n=u.match.PSEUDO.exec(e);)i+=n[0],e=e.replace(u.match.PSEUDO,"");e=u.relative[e]?e+"*":e;for(var a=0,s=o.length;a<s;a++)c(e,o[a],r);return c.filter(i,r)};b.find=c,b.expr=c.selectors,b.expr[":"]=b.expr.filters,b.unique=c.uniqueSort,b.text=e,b.isXMLDoc=g,b.contains=v}();var re=/Until$/,ie=/^(?:parents|prevUntil|prevAll)/,oe=/,/,M=Array.prototype.slice,ae=function(e,t,n){if(b.isFunction(t))return b.grep(e,function(e,r){return!!t.call(e,r,e)===n});if(t.nodeType)return b.grep(e,function(e,r){return e===t===n});if("string"==typeof t){var r=b.grep(e,function(e){return 1===e.nodeType});if(S.test(t))return b.filter(t,r,!n);t=b.filter(t,r)}return b.grep(e,function(e,r){return b.inArray(e,t)>=0===n})};b.fn.extend({find:function(e){for(var t=this.pushStack("","find",e),n=0,r=0,i=this.length;r<i;r++)if(n=t.length,b.find(e,this[r],t),r>0)for(var o=n;o<t.length;o++)for(var a=0;a<n;a++)if(t[a]===t[o]){t.splice(o--,1);break}return t},has:function(e){var t=b(e);return this.filter(function(){for(var e=0,n=t.length;e<n;e++)if(b.contains(this,t[e]))return!0})},not:function(e){return this.pushStack(ae(this,e,!1),"not",e)},filter:function(e){return this.pushStack(ae(this,e,!0),"filter",e)},is:function(e){return!!e&&b.filter(e,this).length>0},closest:function(e,t){if(b.isArray(e)){var n,r,i=[],o=this[0],a={};if(o&&e.length){for(var s=0,l=e.length;s<l;s++)r=e[s],a[r]||(a[r]=b.expr.match.POS.test(r)?b(r,t||this.context):r);for(;o&&o.ownerDocument&&o!==t;){for(r in a)n=a[r],(n.jquery?n.index(o)>-1:b(o).is(n))&&(i.push({selector:r,elem:o}),delete a[r]);o=o.parentNode}}return i}var c=b.expr.match.POS.test(e)?b(e,t||this.context):null;return this.map(function(n,r){for(;r&&r.ownerDocument&&r!==t;){if(c?c.index(r)>-1:b(r).is(e))return r;r=r.parentNode}return null})},index:function(e){return e&&"string"!=typeof e?b.inArray(e.jquery?e[0]:e,this):b.inArray(this[0],e?b(e):this.parent().children())},add:function(e,t){var n="string"==typeof e?b(e,t||this.context):b.makeArray(e),r=b.merge(this.get(),n);return this.pushStack(f(n[0])||f(r[0])?r:b.unique(r))},andSelf:function(){return this.add(this.prevObject)}}),b.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return b.dir(e,"parentNode")},parentsUntil:function(e,t,n){return b.dir(e,"parentNode",n)},next:function(e){return b.nth(e,2,"nextSibling")},prev:function(e){return b.nth(e,2,"previousSibling")},nextAll:function(e){return b.dir(e,"nextSibling")},prevAll:function(e){return b.dir(e,"previousSibling")},nextUntil:function(e,t,n){return b.dir(e,"nextSibling",n)},prevUntil:function(e,t,n){return b.dir(e,"previousSibling",n)},siblings:function(e){return b.sibling(e.parentNode.firstChild,e)},children:function(e){return b.sibling(e.firstChild)},contents:function(e){return b.nodeName(e,"iframe")?e.contentDocument||e.contentWindow.document:b.makeArray(e.childNodes)}},function(e,t){b.fn[e]=function(n,r){var i=b.map(this,t,n);return re.test(e)||(r=n),r&&"string"==typeof r&&(i=b.filter(r,i)),i=this.length>1?b.unique(i):i,(this.length>1||oe.test(r))&&ie.test(e)&&(i=i.reverse()),this.pushStack(i,e,M.call(arguments).join(","))}}),b.extend({filter:function(e,t,n){return n&&(e=":not("+e+")"),b.find.matches(e,t)},dir:function(e,n,r){for(var i=[],o=e[n];o&&9!==o.nodeType&&(r===t||1!==o.nodeType||!b(o).is(r));)1===o.nodeType&&i.push(o),o=o[n];return i},nth:function(e,t,n,r){t=t||1;for(var i=0;e&&(1!==e.nodeType||++i!==t);e=e[n]);return e},sibling:function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n}});var se=/ jQuery\d+="(?:\d+|null)"/g,le=/^\s+/,ce=/(<([\w:]+)[^>]*?)\/>/g,ue=/^(?:area|br|col|embed|hr|img|input|link|meta|param)$/i,fe=/<([\w:]+)/,de=/<tbody/i,pe=/<|&#?\w+;/,he=/<script|<object|<embed|<option|<style/i,me=/checked\s*(?:[^=]|=\s*.checked.)/i,ve=function(e,t,n){return ue.test(n)?e:t+"></"+n+">"},ge={option:[1,"<select multiple='multiple'>","</select>"],legend:[1,"<fieldset>","</fieldset>"],thead:[1,"<table>","</table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],col:[2,"<table><tbody></tbody><colgroup>","</colgroup></table>"],area:[1,"<map>","</map>"],_default:[0,"",""]};ge.optgroup=ge.option,ge.tbody=ge.tfoot=ge.colgroup=ge.caption=ge.thead,ge.th=ge.td,b.support.htmlSerialize||(ge._default=[1,"div<div>","</div>"]),b.fn.extend({text:function(e){return b.isFunction(e)?this.each(function(t){var n=b(this);n.text(e.call(this,t,n.text()))}):"object"!=typeof e&&e!==t?this.empty().append((this[0]&&this[0].ownerDocument||w).createTextNode(e)):b.text(this)},wrapAll:function(e){if(b.isFunction(e))return this.each(function(t){b(this).wrapAll(e.call(this,t))});if(this[0]){var t=b(e,this[0].ownerDocument).eq(0).clone(!0);this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){for(var e=this;e.firstChild&&1===e.firstChild.nodeType;)e=e.firstChild;return e}).append(this)}return this},wrapInner:function(e){return b.isFunction(e)?this.each(function(t){b(this).wrapInner(e.call(this,t))}):this.each(function(){var t=b(this),n=t.contents();n.length?n.wrapAll(e):t.append(e)})},wrap:function(e){return this.each(function(){b(this).wrapAll(e)})},unwrap:function(){return this.parent().each(function(){b.nodeName(this,"body")||b(this).replaceWith(this.childNodes)}).end()},append:function(){return this.domManip(arguments,!0,function(e){1===this.nodeType&&this.appendChild(e)})},prepend:function(){return this.domManip(arguments,!0,function(e){1===this.nodeType&&this.insertBefore(e,this.firstChild)})},before:function(){if(this[0]&&this[0].parentNode)return this.domManip(arguments,!1,function(e){this.parentNode.insertBefore(e,this)});if(arguments.length){var e=b(arguments[0]);return e.push.apply(e,this.toArray()),this.pushStack(e,"before",arguments)}},after:function(){if(this[0]&&this[0].parentNode)return this.domManip(arguments,!1,function(e){this.parentNode.insertBefore(e,this.nextSibling)});if(arguments.length){var e=this.pushStack(this,"after",arguments);return e.push.apply(e,b(arguments[0]).toArray()),e}},remove:function(e,t){for(var n,r=0;null!=(n=this[r]);r++)e&&!b.filter(e,[n]).length||(t||1!==n.nodeType||(b.cleanData(n.getElementsByTagName("*")),b.cleanData([n])),n.parentNode&&n.parentNode.removeChild(n));return this},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)for(1===e.nodeType&&b.cleanData(e.getElementsByTagName("*"));e.firstChild;)e.removeChild(e.firstChild);return this},clone:function(e){var t=this.map(function(){if(b.support.noCloneEvent||b.isXMLDoc(this))return this.cloneNode(!0);var e=this.outerHTML,t=this.ownerDocument;if(!e){var n=t.createElement("div");n.appendChild(this.cloneNode(!0)),e=n.innerHTML}return b.clean([e.replace(se,"").replace(/=([^="'>\s]+\/)>/g,'="$1">').replace(le,"")],t)[0]});return e===!0&&(d(this,t),d(this.find("*"),t.find("*"))),t},html:function(e){if(e===t)return this[0]&&1===this[0].nodeType?this[0].innerHTML.replace(se,""):null;if("string"!=typeof e||he.test(e)||!b.support.leadingWhitespace&&le.test(e)||ge[(fe.exec(e)||["",""])[1].toLowerCase()])b.isFunction(e)?this.each(function(t){var n=b(this),r=n.html();n.empty().append(function(){return e.call(this,t,r)})}):this.empty().append(e);else{e=e.replace(ce,ve);try{for(var n=0,r=this.length;n<r;n++)1===this[n].nodeType&&(b.cleanData(this[n].getElementsByTagName("*")),this[n].innerHTML=e)}catch(i){this.empty().append(e)}}return this},replaceWith:function(e){return this[0]&&this[0].parentNode?b.isFunction(e)?this.each(function(t){var n=b(this),r=n.html();n.replaceWith(e.call(this,t,r))}):("string"!=typeof e&&(e=b(e).detach()),this.each(function(){var t=this.nextSibling,n=this.parentNode;b(this).remove(),t?b(t).before(e):b(n).append(e)})):this.pushStack(b(b.isFunction(e)?e():e),"replaceWith",e)},detach:function(e){return this.remove(e,!0)},domManip:function(e,n,i){function o(e,t){return b.nodeName(e,"table")?e.getElementsByTagName("tbody")[0]||e.appendChild(e.ownerDocument.createElement("tbody")):e}var a,s,l,c,u=e[0],f=[];if(!b.support.checkClone&&3===arguments.length&&"string"==typeof u&&me.test(u))return this.each(function(){b(this).domManip(e,n,i,!0)});if(b.isFunction(u))return this.each(function(r){var o=b(this);e[0]=u.call(this,r,n?o.html():t),o.domManip(e,n,i)});if(this[0]){if(c=u&&u.parentNode,a=b.support.parentNode&&c&&11===c.nodeType&&c.childNodes.length===this.length?{fragment:c}:p(e,this,f),l=a.fragment,s=1===l.childNodes.length?l=l.firstChild:l.firstChild){n=n&&b.nodeName(s,"tr");for(var d=0,h=this.length;d<h;d++)i.call(n?o(this[d],s):this[d],d>0||a.cacheable||this.length>1?l.cloneNode(!0):l)}f.length&&b.each(f,r)}return this}}),b.fragments={},b.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,t){b.fn[e]=function(n){var r=[],i=b(n),o=1===this.length&&this[0].parentNode;if(o&&11===o.nodeType&&1===o.childNodes.length&&1===i.length)return i[t](this[0]),this;for(var a=0,s=i.length;a<s;a++){var l=(a>0?this.clone(!0):this).get();b.fn[t].apply(b(i[a]),l),r=r.concat(l)}return this.pushStack(r,e,i.selector)}}),b.extend({clean:function(e,t,n,r){t=t||w,"undefined"==typeof t.createElement&&(t=t.ownerDocument||t[0]&&t[0].ownerDocument||w);for(var i,o=[],a=0;null!=(i=e[a]);a++)if("number"==typeof i&&(i+=""),i){if("string"!=typeof i||pe.test(i)){if("string"==typeof i){i=i.replace(ce,ve);var s=(fe.exec(i)||["",""])[1].toLowerCase(),l=ge[s]||ge._default,c=l[0],u=t.createElement("div");for(u.innerHTML=l[1]+i+l[2];c--;)u=u.lastChild;if(!b.support.tbody)for(var f=de.test(i),d="table"!==s||f?"<table>"!==l[1]||f?[]:u.childNodes:u.firstChild&&u.firstChild.childNodes,p=d.length-1;p>=0;--p)b.nodeName(d[p],"tbody")&&!d[p].childNodes.length&&d[p].parentNode.removeChild(d[p]);!b.support.leadingWhitespace&&le.test(i)&&u.insertBefore(t.createTextNode(le.exec(i)[0]),u.firstChild),i=u.childNodes}}else i=t.createTextNode(i);i.nodeType?o.push(i):o=b.merge(o,i)}if(n)for(var a=0;o[a];a++)!r||!b.nodeName(o[a],"script")||o[a].type&&"text/javascript"!==o[a].type.toLowerCase()?(1===o[a].nodeType&&o.splice.apply(o,[a+1,0].concat(b.makeArray(o[a].getElementsByTagName("script")))),n.appendChild(o[a])):r.push(o[a].parentNode?o[a].parentNode.removeChild(o[a]):o[a]);return o},cleanData:function(e){for(var t,n,r,i=b.cache,o=b.event.special,a=b.support.deleteExpando,s=0;null!=(r=e[s]);s++)if(n=r[b.expando]){if(t=i[n],t.events)for(var l in t.events)o[l]?b.event.remove(r,l):Y(r,l,t.handle);a?delete r[b.expando]:r.removeAttribute&&r.removeAttribute(b.expando),delete i[n]}}});var ye=/z-?index|font-?weight|opacity|zoom|line-?height/i,be=/alpha\([^)]*\)/,xe=/opacity=([^)]*)/,Te=/float/i,we=/-([a-z])/gi,Ne=/([A-Z])/g,Se=/^-?\d+(?:px)?$/i,Ee=/^-?\d/,Ce={position:"absolute",visibility:"hidden",display:"block"},Ae=["Left","Right"],Fe=["Top","Bottom"],je=w.defaultView&&w.defaultView.getComputedStyle,Le=b.support.cssFloat?"cssFloat":"styleFloat",De=function(e,t){return t.toUpperCase()};b.fn.css=function(e,n){return i(this,e,n,!0,function(e,n,r){return r===t?b.curCSS(e,n):("number"!=typeof r||ye.test(n)||(r+="px"),void b.style(e,n,r))})},b.extend({style:function(e,n,r){if(!e||3===e.nodeType||8===e.nodeType)return t;("width"===n||"height"===n)&&parseFloat(r)<0&&(r=t);var i=e.style||e,o=r!==t;if(!b.support.opacity&&"opacity"===n){if(o){i.zoom=1;var a=parseInt(r,10)+""=="NaN"?"":"alpha(opacity="+100*r+")",s=i.filter||b.curCSS(e,"filter")||"";i.filter=be.test(s)?s.replace(be,a):a}return i.filter&&i.filter.indexOf("opacity=")>=0?parseFloat(xe.exec(i.filter)[1])/100+"":""}return Te.test(n)&&(n=Le),n=n.replace(we,De),o&&(i[n]=r),i[n]},css:function(e,t,n,r){function i(){o="width"===t?e.offsetWidth:e.offsetHeight,"border"!==r&&b.each(s,function(){r||(o-=parseFloat(b.curCSS(e,"padding"+this,!0))||0),"margin"===r?o+=parseFloat(b.curCSS(e,"margin"+this,!0))||0:o-=parseFloat(b.curCSS(e,"border"+this+"Width",!0))||0})}if("width"===t||"height"===t){var o,a=Ce,s="width"===t?Ae:Fe;return 0!==e.offsetWidth?i():b.swap(e,a,i),Math.max(0,Math.round(o))}return b.curCSS(e,t,n)},curCSS:function(e,t,n){var r,i=e.style;if(!b.support.opacity&&"opacity"===t&&e.currentStyle)return r=xe.test(e.currentStyle.filter||"")?parseFloat(RegExp.$1)/100+"":"",""===r?"1":r;if(Te.test(t)&&(t=Le),!n&&i&&i[t])r=i[t];else if(je){Te.test(t)&&(t="float"),t=t.replace(Ne,"-$1").toLowerCase();var o=e.ownerDocument.defaultView;if(!o)return null;var a=o.getComputedStyle(e,null);a&&(r=a.getPropertyValue(t)),"opacity"===t&&""===r&&(r="1")}else if(e.currentStyle){var s=t.replace(we,De);if(r=e.currentStyle[t]||e.currentStyle[s],!Se.test(r)&&Ee.test(r)){var l=i.left,c=e.runtimeStyle.left;e.runtimeStyle.left=e.currentStyle.left,i.left="fontSize"===s?"1em":r||0,r=i.pixelLeft+"px",i.left=l,e.runtimeStyle.left=c}}return r},swap:function(e,t,n){var r={};for(var i in t)r[i]=e.style[i],e.style[i]=t[i];n.call(e);for(var i in t)e.style[i]=r[i]}}),b.expr&&b.expr.filters&&(b.expr.filters.hidden=function(e){var t=e.offsetWidth,n=e.offsetHeight,r="tr"===e.nodeName.toLowerCase();return 0===t&&0===n&&!r||!(t>0&&n>0&&!r)&&"none"===b.curCSS(e,"display")},b.expr.filters.visible=function(e){return!b.expr.filters.hidden(e)});var ke=o(),Oe=/<script(.|\s)*?\/script>/gi,Me=/select|textarea/i,Ie=/color|date|datetime|email|hidden|month|number|password|range|search|tel|text|time|url|week/i,Be=/=\?(&|$)/,Pe=/\?/,qe=/(\?|&)_=.*?(&|$)/,He=/^(\w+:)?\/\/([^\/?#]+)/,Re=/%20/g,$e=b.fn.load;b.fn.extend({load:function(e,t,n){if("string"!=typeof e)return $e.call(this,e);if(!this.length)return this;var r=e.indexOf(" ");if(r>=0){var i=e.slice(r,e.length);e=e.slice(0,r)}var o="GET";t&&(b.isFunction(t)?(n=t,t=null):"object"==typeof t&&(t=b.param(t,b.ajaxSettings.traditional),o="POST"));var a=this;return b.ajax({url:e,type:o,dataType:"html",data:t,complete:function(e,t){"success"!==t&&"notmodified"!==t||a.html(i?b("<div />").append(e.responseText.replace(Oe,"")).find(i):e.responseText),n&&a.each(n,[e.responseText,t,e])}}),this},serialize:function(){return b.param(this.serializeArray())},serializeArray:function(){return this.map(function(){return this.elements?b.makeArray(this.elements):this}).filter(function(){return this.name&&!this.disabled&&(this.checked||Me.test(this.nodeName)||Ie.test(this.type))}).map(function(e,t){var n=b(this).val();return null==n?null:b.isArray(n)?b.map(n,function(e,n){return{name:t.name,value:e}}):{name:t.name,value:n}}).get()}}),b.each("ajaxStart ajaxStop ajaxComplete ajaxError ajaxSuccess ajaxSend".split(" "),function(e,t){b.fn[t]=function(e){return this.bind(t,e)}}),b.extend({get:function(e,t,n,r){return b.isFunction(t)&&(r=r||n,n=t,t=null),b.ajax({type:"GET",url:e,data:t,success:n,dataType:r})},getScript:function(e,t){return b.get(e,null,t,"script")},getJSON:function(e,t,n){return b.get(e,t,n,"json")},post:function(e,t,n,r){return b.isFunction(t)&&(r=r||n,n=t,t={}),b.ajax({type:"POST",url:e,data:t,success:n,dataType:r})},ajaxSetup:function(e){b.extend(b.ajaxSettings,e)},ajaxSettings:{url:location.href,global:!0,type:"GET",contentType:"application/x-www-form-urlencoded",processData:!0,async:!0,xhr:!e.XMLHttpRequest||"file:"===e.location.protocol&&e.ActiveXObject?function(){try{return new e.ActiveXObject("Microsoft.XMLHTTP")}catch(t){}}:function(){return new e.XMLHttpRequest},accepts:{xml:"application/xml, text/xml",html:"text/html",script:"text/javascript, application/javascript",json:"application/json, text/javascript",text:"text/plain",_default:"*/*"}},lastModified:{},etag:{},ajax:function(n){function r(){u.success&&u.success.call(f,c,l,N),u.global&&a("ajaxSuccess",[N,u])}function i(){u.complete&&u.complete.call(f,N,l),u.global&&a("ajaxComplete",[N,u]),u.global&&!--b.active&&b.event.trigger("ajaxStop")}function a(e,t){(u.context?b(u.context):b.event).trigger(e,t)}var s,l,c,u=b.extend(!0,{},b.ajaxSettings,n),f=n&&n.context||u,d=u.type.toUpperCase();if(u.data&&u.processData&&"string"!=typeof u.data&&(u.data=b.param(u.data,u.traditional)),"jsonp"===u.dataType&&("GET"===d?Be.test(u.url)||(u.url+=(Pe.test(u.url)?"&":"?")+(u.jsonp||"callback")+"=?"):u.data&&Be.test(u.data)||(u.data=(u.data?u.data+"&":"")+(u.jsonp||"callback")+"=?"),u.dataType="json"),"json"===u.dataType&&(u.data&&Be.test(u.data)||Be.test(u.url))&&(s=u.jsonpCallback||"jsonp"+ke++,u.data&&(u.data=(u.data+"").replace(Be,"="+s+"$1")),u.url=u.url.replace(Be,"="+s+"$1"),u.dataType="script",e[s]=e[s]||function(n){c=n,r(),i(),e[s]=t;try{delete e[s]}catch(o){}g&&g.removeChild(y)}),"script"===u.dataType&&null===u.cache&&(u.cache=!1),u.cache===!1&&"GET"===d){var p=o(),h=u.url.replace(qe,"$1_="+p+"$2");u.url=h+(h===u.url?(Pe.test(u.url)?"&":"?")+"_="+p:"")}u.data&&"GET"===d&&(u.url+=(Pe.test(u.url)?"&":"?")+u.data),u.global&&!b.active++&&b.event.trigger("ajaxStart");var m=He.exec(u.url),v=m&&(m[1]&&m[1]!==location.protocol||m[2]!==location.host);if("script"===u.dataType&&"GET"===d&&v){var g=w.getElementsByTagName("head")[0]||w.documentElement,y=w.createElement("script");if(y.src=u.url,u.scriptCharset&&(y.charset=u.scriptCharset),!s){var x=!1;y.onload=y.onreadystatechange=function(){x||this.readyState&&"loaded"!==this.readyState&&"complete"!==this.readyState||(x=!0,r(),i(),y.onload=y.onreadystatechange=null,g&&y.parentNode&&g.removeChild(y))}}return g.insertBefore(y,g.firstChild),t}var T=!1,N=u.xhr();if(N){u.username?N.open(d,u.url,u.async,u.username,u.password):N.open(d,u.url,u.async);try{(u.data||n&&n.contentType)&&N.setRequestHeader("Content-Type",u.contentType),u.ifModified&&(b.lastModified[u.url]&&N.setRequestHeader("If-Modified-Since",b.lastModified[u.url]),b.etag[u.url]&&N.setRequestHeader("If-None-Match",b.etag[u.url])),v||N.setRequestHeader("X-Requested-With","XMLHttpRequest"),N.setRequestHeader("Accept",u.dataType&&u.accepts[u.dataType]?u.accepts[u.dataType]+", */*":u.accepts._default)}catch(S){}if(u.beforeSend&&u.beforeSend.call(f,N,u)===!1)return u.global&&!--b.active&&b.event.trigger("ajaxStop"),N.abort(),!1;u.global&&a("ajaxSend",[N,u]);var E=N.onreadystatechange=function(e){if(N&&0!==N.readyState&&"abort"!==e){if(!T&&N&&(4===N.readyState||"timeout"===e)){T=!0,N.onreadystatechange=b.noop,l="timeout"===e?"timeout":b.httpSuccess(N)?u.ifModified&&b.httpNotModified(N,u.url)?"notmodified":"success":"error";var t;if("success"===l)try{c=b.httpData(N,u.dataType,u)}catch(n){l="parsererror",t=n}"success"===l||"notmodified"===l?s||r():b.handleError(u,N,l,t),i(),"timeout"===e&&N.abort(),u.async&&(N=null)}}else T||i(),T=!0,N&&(N.onreadystatechange=b.noop)};try{var C=N.abort;N.abort=function(){N&&C.call(N),E("abort")}}catch(S){}u.async&&u.timeout>0&&setTimeout(function(){N&&!T&&E("timeout")},u.timeout);try{N.send("POST"===d||"PUT"===d||"DELETE"===d?u.data:null)}catch(S){b.handleError(u,N,null,S),i()}return u.async||E(),N}},handleError:function(e,t,n,r){e.error&&e.error.call(e.context||e,t,n,r),e.global&&(e.context?b(e.context):b.event).trigger("ajaxError",[t,e,r])},active:0,httpSuccess:function(e){try{return!e.status&&"file:"===location.protocol||e.status>=200&&e.status<300||304===e.status||1223===e.status||0===e.status}catch(t){}return!1},httpNotModified:function(e,t){var n=e.getResponseHeader("Last-Modified"),r=e.getResponseHeader("Etag");return n&&(b.lastModified[t]=n),r&&(b.etag[t]=r),304===e.status||0===e.status},httpData:function(e,t,n){var r=e.getResponseHeader("content-type")||"",i="xml"===t||!t&&r.indexOf("xml")>=0,o=i?e.responseXML:e.responseText;return i&&"parsererror"===o.documentElement.nodeName&&b.error("parsererror"),n&&n.dataFilter&&(o=n.dataFilter(o,t)),"string"==typeof o&&("json"===t||!t&&r.indexOf("json")>=0?o=b.parseJSON(o):("script"===t||!t&&r.indexOf("javascript")>=0)&&b.globalEval(o)),o},param:function(e,n){function r(e,t){b.isArray(t)?b.each(t,function(t,o){n||/\[\]$/.test(e)?i(e,o):r(e+"["+("object"==typeof o||b.isArray(o)?t:"")+"]",o)}):n||null==t||"object"!=typeof t?i(e,t):b.each(t,function(t,n){r(e+"["+t+"]",n)})}function i(e,t){t=b.isFunction(t)?t():t,o[o.length]=encodeURIComponent(e)+"="+encodeURIComponent(t)}var o=[];if(n===t&&(n=b.ajaxSettings.traditional),b.isArray(e)||e.jquery)b.each(e,function(){i(this.name,this.value)});else for(var a in e)r(a,e[a]);return o.join("&").replace(Re,"+")}});var _e,ze={},We=/toggle|show|hide/,Xe=/^([+-]=)?([\d+-.]+)(.*)$/,Ue=[["height","marginTop","marginBottom","paddingTop","paddingBottom"],["width","marginLeft","marginRight","paddingLeft","paddingRight"],["opacity"]];b.fn.extend({show:function(e,t){if(e||0===e)return this.animate(h("show",3),e,t);for(var n=0,r=this.length;n<r;n++){var i=b.data(this[n],"olddisplay");if(this[n].style.display=i||"","none"===b.css(this[n],"display")){var o,a=this[n].nodeName;if(ze[a])o=ze[a];else{var s=b("<"+a+" />").appendTo("body");o=s.css("display"),"none"===o&&(o="block"),s.remove(),ze[a]=o}b.data(this[n],"olddisplay",o)}}for(var l=0,c=this.length;l<c;l++)this[l].style.display=b.data(this[l],"olddisplay")||"";return this},hide:function(e,t){if(e||0===e)return this.animate(h("hide",3),e,t);for(var n=0,r=this.length;n<r;n++){var i=b.data(this[n],"olddisplay");i||"none"===i||b.data(this[n],"olddisplay",b.css(this[n],"display"))}for(var o=0,a=this.length;o<a;o++)this[o].style.display="none";return this},_toggle:b.fn.toggle,toggle:function(e,t){var n="boolean"==typeof e;return b.isFunction(e)&&b.isFunction(t)?this._toggle.apply(this,arguments):null==e||n?this.each(function(){var t=n?e:b(this).is(":hidden");b(this)[t?"show":"hide"]()}):this.animate(h("toggle",3),e,t),this},fadeTo:function(e,t,n){return this.filter(":hidden").css("opacity",0).show().end().animate({opacity:t},e,n)},animate:function(e,t,n,r){var i=b.speed(t,n,r);return b.isEmptyObject(e)?this.each(i.complete):this[i.queue===!1?"each":"queue"](function(){var t,n=b.extend({},i),r=1===this.nodeType&&b(this).is(":hidden"),o=this;for(t in e){var a=t.replace(we,De);if(t!==a&&(e[a]=e[t],delete e[t],t=a),"hide"===e[t]&&r||"show"===e[t]&&!r)return n.complete.call(this);"height"!==t&&"width"!==t||!this.style||(n.display=b.css(this,"display"),n.overflow=this.style.overflow),b.isArray(e[t])&&((n.specialEasing=n.specialEasing||{})[t]=e[t][1],e[t]=e[t][0])}return null!=n.overflow&&(this.style.overflow="hidden"),n.curAnim=b.extend({},e),b.each(e,function(t,i){var a=new b.fx(o,n,t);if(We.test(i))a["toggle"===i?r?"show":"hide":i](e);else{var s=Xe.exec(i),l=a.cur(!0)||0;if(s){var c=parseFloat(s[2]),u=s[3]||"px";"px"!==u&&(o.style[t]=(c||1)+u,l=(c||1)/a.cur(!0)*l,o.style[t]=l+u),s[1]&&(c=("-="===s[1]?-1:1)*c+l),a.custom(l,c,u)}else a.custom(l,i,"")}}),!0})},stop:function(e,t){var n=b.timers;return e&&this.queue([]),this.each(function(){for(var e=n.length-1;e>=0;e--)n[e].elem===this&&(t&&n[e](!0),n.splice(e,1))}),t||this.dequeue(),this}}),b.each({slideDown:h("show",1),slideUp:h("hide",1),slideToggle:h("toggle",1),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"}},function(e,t){b.fn[e]=function(e,n){return this.animate(t,e,n)}}),b.extend({speed:function(e,t,n){var r=e&&"object"==typeof e?e:{complete:n||!n&&t||b.isFunction(e)&&e,duration:e,easing:n&&t||t&&!b.isFunction(t)&&t};return r.duration=b.fx.off?0:"number"==typeof r.duration?r.duration:b.fx.speeds[r.duration]||b.fx.speeds._default,r.old=r.complete,r.complete=function(){r.queue!==!1&&b(this).dequeue(),b.isFunction(r.old)&&r.old.call(this)},r},easing:{linear:function(e,t,n,r){return n+r*e},swing:function(e,t,n,r){return(-Math.cos(e*Math.PI)/2+.5)*r+n}},timers:[],fx:function(e,t,n){this.options=t,this.elem=e,this.prop=n,t.orig||(t.orig={})}}),b.fx.prototype={update:function(){this.options.step&&this.options.step.call(this.elem,this.now,this),(b.fx.step[this.prop]||b.fx.step._default)(this),"height"!==this.prop&&"width"!==this.prop||!this.elem.style||(this.elem.style.display="block")},cur:function(e){if(null!=this.elem[this.prop]&&(!this.elem.style||null==this.elem.style[this.prop]))return this.elem[this.prop];var t=parseFloat(b.css(this.elem,this.prop,e));return t&&t>-1e4?t:parseFloat(b.curCSS(this.elem,this.prop))||0},custom:function(e,t,n){function r(e){return i.step(e)}this.startTime=o(),this.start=e,this.end=t,this.unit=n||this.unit||"px",this.now=this.start,this.pos=this.state=0;var i=this;r.elem=this.elem,r()&&b.timers.push(r)&&!_e&&(_e=setInterval(b.fx.tick,13))},show:function(){this.options.orig[this.prop]=b.style(this.elem,this.prop),this.options.show=!0,this.custom("width"===this.prop||"height"===this.prop?1:0,this.cur()),b(this.elem).show()},hide:function(){this.options.orig[this.prop]=b.style(this.elem,this.prop),
this.options.hide=!0,this.custom(this.cur(),0)},step:function(e){var t=o(),n=!0;if(e||t>=this.options.duration+this.startTime){this.now=this.end,this.pos=this.state=1,this.update(),this.options.curAnim[this.prop]=!0;for(var r in this.options.curAnim)this.options.curAnim[r]!==!0&&(n=!1);if(n){if(null!=this.options.display){this.elem.style.overflow=this.options.overflow;var i=b.data(this.elem,"olddisplay");this.elem.style.display=i?i:this.options.display,"none"===b.css(this.elem,"display")&&(this.elem.style.display="block")}if(this.options.hide&&b(this.elem).hide(),this.options.hide||this.options.show)for(var a in this.options.curAnim)b.style(this.elem,a,this.options.orig[a]);this.options.complete.call(this.elem)}return!1}var s=t-this.startTime;this.state=s/this.options.duration;var l=this.options.specialEasing&&this.options.specialEasing[this.prop],c=this.options.easing||(b.easing.swing?"swing":"linear");return this.pos=b.easing[l||c](this.state,s,0,1,this.options.duration),this.now=this.start+(this.end-this.start)*this.pos,this.update(),!0}},b.extend(b.fx,{tick:function(){for(var e=b.timers,t=0;t<e.length;t++)e[t]()||e.splice(t--,1);e.length||b.fx.stop()},stop:function(){clearInterval(_e),_e=null},speeds:{slow:600,fast:200,_default:400},step:{opacity:function(e){b.style(e.elem,"opacity",e.now)},_default:function(e){e.elem.style&&null!=e.elem.style[e.prop]?e.elem.style[e.prop]=("width"===e.prop||"height"===e.prop?Math.max(0,e.now):e.now)+e.unit:e.elem[e.prop]=e.now}}}),b.expr&&b.expr.filters&&(b.expr.filters.animated=function(e){return b.grep(b.timers,function(t){return e===t.elem}).length}),"getBoundingClientRect"in w.documentElement?b.fn.offset=function(e){var t=this[0];if(e)return this.each(function(t){b.offset.setOffset(this,e,t)});if(!t||!t.ownerDocument)return null;if(t===t.ownerDocument.body)return b.offset.bodyOffset(t);var n=t.getBoundingClientRect(),r=t.ownerDocument,i=r.body,o=r.documentElement,a=o.clientTop||i.clientTop||0,s=o.clientLeft||i.clientLeft||0,l=n.top+(self.pageYOffset||b.support.boxModel&&o.scrollTop||i.scrollTop)-a,c=n.left+(self.pageXOffset||b.support.boxModel&&o.scrollLeft||i.scrollLeft)-s;return{top:l,left:c}}:b.fn.offset=function(e){var t=this[0];if(e)return this.each(function(t){b.offset.setOffset(this,e,t)});if(!t||!t.ownerDocument)return null;if(t===t.ownerDocument.body)return b.offset.bodyOffset(t);b.offset.initialize();for(var n,r=t.offsetParent,i=t,o=t.ownerDocument,a=o.documentElement,s=o.body,l=o.defaultView,c=l?l.getComputedStyle(t,null):t.currentStyle,u=t.offsetTop,f=t.offsetLeft;(t=t.parentNode)&&t!==s&&t!==a&&(!b.offset.supportsFixedPosition||"fixed"!==c.position);)n=l?l.getComputedStyle(t,null):t.currentStyle,u-=t.scrollTop,f-=t.scrollLeft,t===r&&(u+=t.offsetTop,f+=t.offsetLeft,!b.offset.doesNotAddBorder||b.offset.doesAddBorderForTableAndCells&&/^t(able|d|h)$/i.test(t.nodeName)||(u+=parseFloat(n.borderTopWidth)||0,f+=parseFloat(n.borderLeftWidth)||0),i=r,r=t.offsetParent),b.offset.subtractsBorderForOverflowNotVisible&&"visible"!==n.overflow&&(u+=parseFloat(n.borderTopWidth)||0,f+=parseFloat(n.borderLeftWidth)||0),c=n;return"relative"!==c.position&&"static"!==c.position||(u+=s.offsetTop,f+=s.offsetLeft),b.offset.supportsFixedPosition&&"fixed"===c.position&&(u+=Math.max(a.scrollTop,s.scrollTop),f+=Math.max(a.scrollLeft,s.scrollLeft)),{top:u,left:f}},b.offset={initialize:function(){var e,t,n,r,i=w.body,o=w.createElement("div"),a=parseFloat(b.curCSS(i,"marginTop",!0))||0,s="<div style='position:absolute;top:0;left:0;margin:0;border:5px solid #000;padding:0;width:1px;height:1px;'><div></div></div><table style='position:absolute;top:0;left:0;margin:0;border:5px solid #000;padding:0;width:1px;height:1px;' cellpadding='0' cellspacing='0'><tr><td></td></tr></table>";b.extend(o.style,{position:"absolute",top:0,left:0,margin:0,border:0,width:"1px",height:"1px",visibility:"hidden"}),o.innerHTML=s,i.insertBefore(o,i.firstChild),e=o.firstChild,t=e.firstChild,r=e.nextSibling.firstChild.firstChild,this.doesNotAddBorder=5!==t.offsetTop,this.doesAddBorderForTableAndCells=5===r.offsetTop,t.style.position="fixed",t.style.top="20px",this.supportsFixedPosition=20===t.offsetTop||15===t.offsetTop,t.style.position=t.style.top="",e.style.overflow="hidden",e.style.position="relative",this.subtractsBorderForOverflowNotVisible=t.offsetTop===-5,this.doesNotIncludeMarginInBodyOffset=i.offsetTop!==a,i.removeChild(o),i=o=e=t=n=r=null,b.offset.initialize=b.noop},bodyOffset:function(e){var t=e.offsetTop,n=e.offsetLeft;return b.offset.initialize(),b.offset.doesNotIncludeMarginInBodyOffset&&(t+=parseFloat(b.curCSS(e,"marginTop",!0))||0,n+=parseFloat(b.curCSS(e,"marginLeft",!0))||0),{top:t,left:n}},setOffset:function(e,t,n){/static/.test(b.curCSS(e,"position"))&&(e.style.position="relative");var r=b(e),i=r.offset(),o=parseInt(b.curCSS(e,"top",!0),10)||0,a=parseInt(b.curCSS(e,"left",!0),10)||0;b.isFunction(t)&&(t=t.call(e,n,i));var s={top:t.top-i.top+o,left:t.left-i.left+a};"using"in t?t.using.call(e,s):r.css(s)}},b.fn.extend({position:function(){if(!this[0])return null;var e=this[0],t=this.offsetParent(),n=this.offset(),r=/^body|html$/i.test(t[0].nodeName)?{top:0,left:0}:t.offset();return n.top-=parseFloat(b.curCSS(e,"marginTop",!0))||0,n.left-=parseFloat(b.curCSS(e,"marginLeft",!0))||0,r.top+=parseFloat(b.curCSS(t[0],"borderTopWidth",!0))||0,r.left+=parseFloat(b.curCSS(t[0],"borderLeftWidth",!0))||0,{top:n.top-r.top,left:n.left-r.left}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent||w.body;e&&!/^body|html$/i.test(e.nodeName)&&"static"===b.css(e,"position");)e=e.offsetParent;return e})}}),b.each(["Left","Top"],function(e,n){var r="scroll"+n;b.fn[r]=function(n){var i,o=this[0];return o?n!==t?this.each(function(){i=m(this),i?i.scrollTo(e?b(i).scrollLeft():n,e?n:b(i).scrollTop()):this[r]=n}):(i=m(o),i?"pageXOffset"in i?i[e?"pageYOffset":"pageXOffset"]:b.support.boxModel&&i.document.documentElement[r]||i.document.body[r]:o[r]):null}}),b.each(["Height","Width"],function(e,n){var r=n.toLowerCase();b.fn["inner"+n]=function(){return this[0]?b.css(this[0],r,!1,"padding"):null},b.fn["outer"+n]=function(e){return this[0]?b.css(this[0],r,!1,e?"margin":"border"):null},b.fn[r]=function(e){var i=this[0];return i?b.isFunction(e)?this.each(function(t){var n=b(this);n[r](e.call(this,t,n[r]()))}):"scrollTo"in i&&i.document?"CSS1Compat"===i.document.compatMode&&i.document.documentElement["client"+n]||i.document.body["client"+n]:9===i.nodeType?Math.max(i.documentElement["client"+n],i.body["scroll"+n],i.documentElement["scroll"+n],i.body["offset"+n],i.documentElement["offset"+n]):e===t?b.css(i,r):this.css(r,"string"==typeof e?e:e+"px"):null==e?null:this}}),e.jQuery=e.$=b}(window);