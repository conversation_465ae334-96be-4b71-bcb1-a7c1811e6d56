<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html>
<html lang="en" class="no-js">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="description" content="">
	<meta name="keywords" content="">

	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/amazeui.min.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/app.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/all.css">
	<title>个人中心 - 站长源码库（zzmaku.com） </title>

	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/common.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/mine.css">

	<style>
		.head_input {
			position: absolute;
			width: 100%;
			height: 100%;
			top: 0;
			opacity: 0;
		}

		.head_height img {
			width: 100%;
			height: 100%;
		}
	</style>

</head>

<body id="wd">


	<div class="wd-head">
		<div class="comm_top_nav" style="color: rgb(39, 37, 40);margin: 0px;background: none;" data-am-sticky="">
			<b>个人中心</b>
		</div>
		<div class="mine_top_bg">

		</div>
		<div class="mine_top_div"></div>
		<div class="mine_top">
			<div class="am-g">
				<div class="am-u-sm-4  am-u-sm-offset-1 head_height">
					<img class="am-circle am-img-responsive head" src="__PUBLIC__/home/<USER>/picture/default_head.png">

				</div>
				<div class="am-u-sm-7" style="line-height: 200%;color: rgb(39, 37, 40);">
					<b>账号</b>
					<br>
					<?php if($user == 0): ?><b class="please_login" onclick="javascript:window.location.href='<?php echo U('User/login');?>'">请登录</b>
						<?php else: ?>
						<span class="f_number"><?php echo ($user); ?></span><?php endif; ?>

				</div>
			</div>

			<div class="am-g" style="overflow: hidden;line-height: 40px;padding: 10px 0;">
				<div class="am-u-sm-6">
					<a href="<?php echo U('Qianbao/index');?>" style="color: rgb(39, 37, 40);">
						<div class="left_li_div">
							<img class="left_li_img am-img-responsive menu-icon"
								src="__PUBLIC__/home/<USER>/picture/yhq.png">
							<div class="left_li_txt">我的钱包</div>
							<div class="clear"></div>
						</div>
					</a>
				</div>
				<div class="am-u-sm-6">
					<a href="<?php echo U('User/setup');?>" style="color: rgb(39, 37, 40);">

						<div class="left_li_div">
							<img class="left_li_img am-img-responsive menu-icon"
								src="__PUBLIC__/home/<USER>/picture/sz.png">
							<div class="left_li_txt">设置</div>
							<div class="clear"></div>
						</div>
					</a>
				</div>

			</div>
		</div>


	</div>

	<div class="am-g tx-title">
		<div class="am-u-sm-1">
			<div class="point"></div>
		</div>
		<a href="<?php echo U('Qianbao/index');?>" style="color: #000;">
		<div class="am-u-sm-9">
			可提现金额:
			<b class="f_number quota">
				<?php if(empty($users["zhanghuyue"])): ?>0
					<?php else: ?>
					<?php echo ($users["zhanghuyue"]); endif; ?>

			</b>
			元
		</div>
		</a>
		<div class="am-u-sm-2" style="text-align: right;"><i class="am-icon-angle-right am-icon-fw"></i></div>
	</div>

	<div class="menu-list">
		<div class="am-g">
			<a href="<?php echo U('Info/index');?>">
				<div class="am-u-sm-10">
					<img class="am-circle am-img-responsive menu-icon" src="__PUBLIC__/home/<USER>/picture/wdzl.png">

					我的资料
				</div>
				<div class="am-u-sm-2" style="text-align: right;"><i class="am-icon-angle-right am-icon-fw"></i></div>
			</a>
		</div>
		<div class="am-g">
			<a href="<?php echo U('Info/contract');?>">
				<div class="am-u-sm-10">
					<img class="am-circle am-img-responsive menu-icon" src="__PUBLIC__/home/<USER>/picture/yzm.png">
					我的合同
				</div>
				<div class="am-u-sm-2" style="text-align: right;"><i class="am-icon-angle-right am-icon-fw"></i></div>
			</a>
		</div>
		
		<div class="am-g">
			<a href="<?php echo U('Order/lists');?>">
				<div class="am-u-sm-10">
					<img class="am-circle am-img-responsive menu-icon" src="__PUBLIC__/home/<USER>/picture/wdjk.png">

					我的借款
				</div>
				<div class="am-u-sm-2" style="text-align: right;"><i class="am-icon-angle-right am-icon-fw"></i></div>
			</a>
		</div>
		<div class="am-g">
			<a href="<?php echo U('Order/bills');?>">
				<div class="am-u-sm-10">
					<img class="am-circle am-img-responsive menu-icon" src="__PUBLIC__/home/<USER>/picture/wdhk.png">

					我的还款
				</div>
				<div class="am-u-sm-2" style="text-align: right;"><i class="am-icon-angle-right am-icon-fw"></i></div>
			</a>
		</div>
	</div>

	<div class="system-list">
		<div class="am-g">
			<a href="<?php echo U('User/evaluation');?>">
				<div class="am-u-sm-10">
					<img class="am-circle am-img-responsive menu-icon" src="__PUBLIC__/home/<USER>/picture/fxcp.png">

					风险测评
				</div>
				<div class="am-u-sm-2" style="text-align: right;"><i class="am-icon-angle-right am-icon-fw"></i></div>
			</a>
		</div>
		<div class="am-g" style="display: none;">
			<a href="#">
				<div class="am-u-sm-10">
					<img class="am-circle am-img-responsive menu-icon" src="__PUBLIC__/home/<USER>/picture/wdxx.png">

					我的消息
				</div>
				<div class="am-u-sm-2" style="text-align: right;"><i class="am-icon-angle-right am-icon-fw"></i></div>
			</a>
		</div>
		<div class="am-g">
			<a href="<?php echo U('User/question');?>">
				<div class="am-u-sm-10">
					<img class="am-circle am-img-responsive menu-icon" src="__PUBLIC__/home/<USER>/picture/cjwt.png">

					常见问题
				</div>
				<div class="am-u-sm-2" style="text-align: right;"><i class="am-icon-angle-right am-icon-fw"></i></div>
			</a>
		</div>
	</div>

	<div class="message">
		<p></p>
	</div>
	<!-- 底部导航条 -->
	<div data-am-widget="navbar" class="am-navbar am-cf am-navbar-default " id="bm-nav">
		<ul class="am-navbar-nav am-cf am-avg-sm-4" style="background-color: #ffffff;">
			<li class="nva_sy">
				<a href="/" class="">
					<img src="__PUBLIC__/home/<USER>/picture/2-1.png" alt="消息">

					<span class="am-navbar-label">首页</span>
				</a>
			</li>
			<li class="nva_qb">
				<a href="<?php echo U('Qianbao/index');?>" class="">
					<img src="__PUBLIC__/home/<USER>/picture/3-1.png" alt="消息">

					<span class="am-navbar-label">钱包</span>
				</a>
			</li>
			<li class="nva_kf">
				<a href="<?php echo U('Help/index');?>" class="">
					<img src="__PUBLIC__/home/<USER>/picture/1-1.png" alt="消息">

					<span class="am-navbar-label">客服</span>
				</a>
			</li>
			<li class="nva_wd">
				<a href="<?php echo U('User/index');?>" class="">
					<img src="__PUBLIC__/home/<USER>/picture/4-1.png" alt="消息">

					<span class="am-navbar-label">我的</span>
				</a>
			</li>
		</ul>
	</div>


	<!--<div id="kefu"></div>-->
	<script type="text/javascript">
		document.documentElement.addEventListener('touchmove', function (event) {
			if (event.touches.length > 1) {
				event.preventDefault();
			}
		}, false);
	</script>

	<script src="__PUBLIC__/home/<USER>/js/jquery3.2.min.js"></script>
	<!--<![endif]-->
	<script src="__PUBLIC__/home/<USER>/js/amazeui.min.js"></script>

	<script>
		var upload_type,
			width = $(window).width(),
			timer,
			msg
			;

		$("#wd #bm-nav .nva_wd a img").attr('src', '__PUBLIC__/home/<USER>/picture/4-2.png');

		$(".head_height").height($(".head_height").width());

		$(".left_li_div").css("padding-left", $('.left_li_div').width() / 2 - ($(".left_li_img").width() + $(".left_li_txt").width()) / 2);
		$(window).resize(function () {
			$(".head_height").height($(".head_height").width());
			$(".left_li_div").css("padding-left", $('.left_li_div').width() / 2 - ($(".left_li_img").width() + $(".left_li_txt").width()) / 2);
		});
		// 弹窗
		// 倒计时
		function myTimer() {
			var sec = 3;
			clearInterval(timer);
			timer = setInterval(function () {
				console.log(sec--);
				if (sec == 1) {
					$(".message").addClass("m-hide");
					$(".message").removeClass("m-show");
				}
				if (sec == 0) {
					$(".message").hide();
					$(".message").removeClass("m-hide");
					clearInterval(timer);
				}
			}, 1000);
		}

		// 弹窗内容
		function message(data) {
			msg = $(".message p").html(data);
			$(".message").addClass("m-show");
			$(".message").show();

			myTimer();

		}

		// 初始化弹窗
		function mesg_default() {
			msg = '';
			$(".message").hide();
			$(".message").removeClass("m-show");
			$(".message").removeClass("m-hide");
		}


		//图像上传
		$('.head_input').change(function () {
			// upload_type = $(this).data("type");
			var file = this.files[0];
			var iname = $(this).val();
			//后台传值需要
			var size = file.size / 1024;
			//获取文件大小 用来判断是否超过多少kb
			var URL = window.URL || window.webkitURL;
			var blob = URL.createObjectURL(file);
			var image = new Image();
			image.src = blob;
			//console.log(blob);
			image.onload = function () {
				getUrlBase64(blob, size);
			};
			//将图片转为base64
			function getUrlBase64(url, size) {
				var canvas = document.createElement("canvas");   //创建canvas DOM元素
				var ctx = canvas.getContext("2d");
				var img = new Image;
				img.crossOrigin = 'Anonymous';
				img.src = url;
				img.onload = function () {
					var w = this.width, h = this.height, scale = w / h;
					w = w > 600 ? 600 : w;
					h = w / scale;
					canvas.height = h; //指定画板的高度,自定义
					canvas.width = w; //指定画板的宽度，自定义
					ctx.drawImage(img, 0, 0, w, h); //参数可自定义
					if (size > 200) {
						//判断 如果图片大图200kb就压缩 否则就不压缩
						var dataURL = canvas.toDataURL("image/jpeg", 0.9);
						//压缩主要代码 第二个参数表示压缩比例，指为1.0时表示不压缩
					} else {
						var dataURL = canvas.toDataURL("image/jpeg");
					}
					//显示预览
					// var img_div = $('#'+upload_type).parent(".upload_box");

					// img_div.css({"background":"url('"+dataURL+"')","background-repeat": "no-repeat","background-size": "auto 100%","background-position":" center center"});
					var oFormData = new FormData();
					// FormData()方法向后台传值
					// oFormData.append("uId",upload_type);

					oFormData.append('base64', dataURL);

					$.ajax({
						type: 'post',
						url: 'uphead',
						data: oFormData,
						cache: false,  // 不缓存
						contentType: false, // jQuery不要去处理发送的数据
						processData: false, // jQuery不要去设置Content-Type请求头
						success: function (data) {
							console.log(data.image_url);
							// var obj = JSON.parse(data);
							// $('#sfz'+upload_type).val(data.image_url);

							$('.head').attr('src', data.image_url);

							mesg_default();

							msg = data.mesg;

							message(msg);

						},
						error: function (err) {
							console.log(err);
						}
					});

					canvas = null;
				};
			}



		});




	</script>

  <div style="display: none;">
    <?php
 $name = "cfg_sitecode"; if(empty($name)){ echo ""; }else{ echo htmlspecialchars_decode(C($name)); } ?>
  </div>




</body>

</html>