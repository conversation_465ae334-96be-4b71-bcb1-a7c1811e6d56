<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">

<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title><?php echo ($title); ?> - <?php
 $name = "cfg_sitetitle"; if(empty($name)){ echo ""; }else{ echo htmlspecialchars_decode(C($name)); } ?>  - 站长源码库（zzmaku.com） </title>
    <link href="__PUBLIC__/main/css/layui.css" rel="stylesheet" type="text/css">
    <link href="__PUBLIC__/main/css/public.css" rel="stylesheet" type="text/css">
    <script type="text/javascript" src="__PUBLIC__/main/js/jquery.min.js"></script>
    <script type="text/javascript" src="__PUBLIC__/main/js/global.js"></script>
    <script type="text/javascript" src="__PUBLIC__/My97DatePicker/WdatePicker.js"></script>
    <script type="text/javascript" src="__PUBLIC__/main/js/jquery.tab.js"></script>
    <script type="text/javascript" src="__PUBLIC__/main/js/echarts.min.js"></script>
    <script src="__PUBLIC__/layer/layer.js"></script>
</head>
<body class="layui-layout-body">
    <div class="layui-layout layui-layout-admin">

      <script src="__PUBLIC__/layer/layui.js"></script>
<div class="layui-header">
    <a href="<?php echo U(GROUP_NAME.'/Main/index');?>" class="layui-logo"><?php echo C('cfg_sitename');?></a>
    <!-- 头部区域（可配合layui已有的水平导航） -->
    <ul class="layui-nav layui-layout-left">
       <!--
       <li class="layui-nav-item">
            <a href="javascript:;">新建</a>
            <dl class="layui-nav-child layui-anim layui-anim-upbit">
                <dd> <a href="<?php echo U(GROUP_NAME.'/Article/add');?>">文章</a></dd>
                <dd><a href="<?php echo U(GROUP_NAME.'/Article/addcat');?>">文章分类</a></dd>
                <dd> <a href="<?php echo U(GROUP_NAME.'/Admin/add');?>">管理员</a></dd>
            </dl>
        </li>
        -->
        <li class="layui-nav-item"><a href="<?php
 $name = "cfg_siteurl"; if(empty($name)){ echo ""; }else{ echo htmlspecialchars_decode(C($name)); } ?>" target="_blank">查看站点</a></li>
        <li class="layui-nav-item"><a href="<?php echo U(GROUP_NAME.'/Main/clearcache');?>">清除缓存</a></li>

    </ul>
    <ul class="layui-nav layui-layout-right">
        <li class="layui-nav-item">
            <a href="javascript:;">
                您好，<?php echo session('admin_user');?>
            </a>
          
            <dl class="layui-nav-child layui-anim layui-anim-upbit">
                <dd> <a href="<?php echo U(GROUP_NAME.'/Admin/chagemypass');?>">修改密码</a></dd>
            </dl>
        </li>
        <li class="layui-nav-item"> <a href="<?php echo U(GROUP_NAME.'/Index/logout');?>">退出</a></li>
    </ul>
</div>


      <!-- dcHead 结束 -->
        <div class="layui-side layui-bg-black">
<div class="layui-side-scroll">
	<!-- 左侧导航区域（可配合layui已有的垂直导航） -->
	<ul class="layui-nav layui-nav-tree"  lay-filter="test">
		<li class="layui-nav-item"><a href="<?php echo U(GROUP_NAME.'/Main/index');?>">管理首页</a></li>
		<li class="layui-nav-item"><a href="<?php echo U(GROUP_NAME.'/System/index');?>">系统设置</a></li>
		<li class="layui-nav-item"><a href="<?php echo U(GROUP_NAME.'/Admin/index');?>">后台管理员</a></li>
		<li class="layui-nav-item"><a href="<?php echo U('User/index');?>">用户管理</a></li>
		<li class="layui-nav-item"><a href="<?php echo U(GROUP_NAME.'/Daikuan/index');?>">借款列表</a></li>
		<li class="layui-nav-item"><a href="<?php echo U('User/qianbao');?>">钱包管理</a></li>
		<li class="layui-nav-item"><a href="<?php echo U(GROUP_NAME.'/Daikuan/contract');?>">借款合同</a></li>
		<li class="layui-nav-item"><a href="<?php echo U('Tixian/index');?>">提现管理</a></li>
		<li class="layui-nav-item"><a href="<?php echo U(GROUP_NAME.'/Bills/index');?>">还款管理</a></li>	
		<li class="layui-nav-item"><a href="<?php echo U(GROUP_NAME.'/Duanxin/addsms');?>">短信状态</a></li>
		<li class="layui-nav-item"><a href="<?php echo U(GROUP_NAME.'/Block/index');?>">自 由 块</a></li>
	<!--	<li class="layui-nav-item"><a href="<?php echo U(GROUP_NAME.'/Article/catlist');?>">文章分类</a></li> !-->
		<li class="layui-nav-item"><a href="<?php echo U(GROUP_NAME.'/Article/index');?>">常见问题</a></li>
	</ul>
  </div>
</div>  
      <div class="layui-body">
        <!-- 内容主体区域 -->
        <blockquote class="layui-elem-quote layui-text">
           <?php echo ($title); ?>
          </blockquote>
        <div style="padding: 15px;">
<style>
body{
    background-color: #f2f2f2;
}
.layuiadmin-span-color {
    font-size: 14px
}

.layuiadmin-span-color i {
    padding-left: 5px
}
.layuiadmin-badge,.layuiadmin-btn-group,.layuiadmin-span-color {
    position: absolute;
    right: 15px
}

.layuiadmin-card-list p.layuiadmin-big-font {
    font-size: 36px;
    color: #666;
    line-height: 36px;
    padding: 5px 0 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
    white-space: nowrap
}
</style>
<div id="douApi"></div>
<div class="indexBox layui-col-space15">
    <div class="layui-col-sm6 layui-col-md3">
        <div class="layui-card">
            <div class="layui-card-header">
                <center><font color="#EE0000"><b>今日注册用户数量</b></font></center>
            </div>
            <div class="layui-card-body layuiadmin-card-list">
               <center><p class="layuiadmin-big-font"><font color="#EE0000"><b><?php echo (num2str($data["dayRegNum"])); ?></b></font></p></center> 
                <p>
                    <font color="#D2691E"><b>总注册用户数量</b></font>
                    <span class="layuiadmin-span-color"><b><?php echo (num2str($data["sumRegNum"])); ?></b><i class="layui-inline layui-icon layui-icon-flag"></i></span>
                </p>
            </div>
        </div>
    </div>
    <div class="layui-col-sm6 layui-col-md3">
        <div class="layui-card">
            <div class="layui-card-header">
                <center><font color="#9A32CD"><b>今日借款申请订单数量</b></font></center>
            </div>
            <div class="layui-card-body layuiadmin-card-list">
                <center><p class="layuiadmin-big-font"><font color="#9A32CD"><b><?php echo (num2str($data["dayLoanNum"])); ?></b></font></p></center> 
                <p>
                    <font color="#008B45"><b>总借款申请订单数量</b></font>
                    <span class="layuiadmin-span-color"><b><?php echo (num2str($data["sumLoanNum"])); ?></b><i class="layui-inline layui-icon layui-icon-face-smile-b"></i></span>
                </p>
            </div>
        </div>
    </div>
    <div class="layui-col-sm6 layui-col-md3">
        <div class="layui-card">
            <div class="layui-card-header">
                <center><font color="#FFC125"><b>今日放款订单数量</b></font></center>
            </div>
            <div class="layui-card-body layuiadmin-card-list">

                <center><p class="layuiadmin-big-font"><b><font color="FFC125"><?php echo (num2str($data["dayAgreeOrderNum"])); ?></b></p></font></center>
                <p>
                    <b><font color="0000EE">总放款订单数量</b></font>
                    <span class="layuiadmin-span-color"><b><?php echo (num2str($data["sumAgreeOrderNum"])); ?></b><i class="layui-inline layui-icon layui-icon-face-smile-b"></i></span>
                </p>
            </div>
        </div>
    </div>
    <div class="layui-col-sm6 layui-col-md3">
        <div class="layui-card">
            <div class="layui-card-header">
                <center><font color="#F08080"><b>今日借款金额（约）</b></font></center>
            </div>
            <div class="layui-card-body layuiadmin-card-list">

                <center><p class="layuiadmin-big-font"><b><font color="#F08080"><?php echo (num2str($data["dayAgreeOrderMoney"])); ?></b></font></p></center>
                <p>
                    <b><font color="CDAD00">总借款金额（约）</b></font>
                    <span class="layuiadmin-span-color"><b><?php echo (num2str($data["sumAgreeOrderMoney"])); ?></b><i class="layui-inline layui-icon layui-icon-dollar"></i></span>
                </p>
            </div>
        </div>
    </div>
    </ul>
</div>

<table width="100%" height="400px" border="0" cellspacing="0" cellpadding="0" class="indexBoxTwo">
    <tr>
        <td width="50%" valign="top" class="pr">
            <div id="container" style="height: 100%"></div>
        </td>
        <td valign="top" class="pl">
            <div id="container1" style="height: 100%"></div>
        </td>
    </tr>
</table>




<table width="100%" border="0" cellspacing="0" cellpadding="0" class="indexBoxTwo">
    <tr>
        <td width="50%" valign="top" class="pr">
            <div class="indexBox">

                <ul>
                    <table class="layui-table" lay-size="lg">
                        <colgroup>
                            <col width="150">
                            <col width="200">
                            <col>
                        </colgroup>
                        <thead>
                        <tr>
                            <th><center><b><font color="0000EE">用户名</b></font></center></th>
                            <th><center><b><font color="0000EE">最后登录时间</b></font></center></th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php if(is_array($frontData)): foreach($frontData as $key=>$vo): ?><tr>
                                <td><center><b><font color="red"><?php echo ($vo["phone"]); ?></b></font></center></td>
                               <td><center><b><font color="red"><?php echo (date('Y/m/d H:i:s',$vo["last_time"])); ?></b></font></center></td>
                            </tr><?php endforeach; endif; ?>
                        </tbody>
                    </table>
                </ul>
            </div>
        </td>
        <td valign="top" class="pl">
            <div class="indexBox">

                <ul>
                    <table class="layui-table" lay-size="lg">
                        <colgroup>
                            <col width="150">
                            <col width="200">
                            <col>
                        </colgroup>
                        <thead>
                        <tr>
                            <th><center><b><font color="0000EE">后台登录IP</b></font></center></th>
                            <th><center><b><font color="0000EE">登录时间</b></font></center></th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php if(is_array($loginData)): foreach($loginData as $key=>$vo): ?><tr>
                                <td><center><b><font color="red"><?php echo ($vo["loginip"]); ?></b></font></center></td>
                                <td><center><b><font color="red"><?php echo (date('Y/m/d H:i:s',$vo["logintime"])); ?></b></font></center></td>
                            </tr><?php endforeach; endif; ?>
                        </tbody>
                    </table>
                </ul>
            </div>
        </td>
    </tr>
</table>
<script type="text/javascript">
    var dom = document.getElementById("container");
    var myChart = echarts.init(dom);
    var app = {};
    var arr1=[],arr2=[];
    function arrTest(){
        $.ajax({
            type:"post",
            async:false,
            url:"<?php echo U(GROUP_NAME.'/Main/vipnum');?>",
            data:{},
            dataType:"json",
            success:function(result){
                if (result) {
                    for (var i = 0; i < result.vipnum.length; i++) {
                        arr1.push(result.vipnum[i]);
                        arr2.push(result.date[i]);
                    }
                }
            }
        })
        return arr1,arr2;
    }
    /*    console.log(arr1);
        console.log(arr2);*/

    arrTest();
    option = {
        title: {
            text: '近7天会员注册情况'
        },
        tooltip: {
            trigger: 'axis'
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        toolbox: {
            feature: {
                saveAsImage: {}
            }
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: arr2
        },
        yAxis: {
            type: 'value'
        },
        series: [

            {
                name:'新增会员数',
                type:'line',
                stack: '总量',
                data:arr1
            }
        ]
    };
    ;
    if (option && typeof option === "object") {
        myChart.setOption(option, true);
    }
</script>
<script type="text/javascript">
    var dom = document.getElementById("container1");
    var myChart = echarts.init(dom);
    var app = {};
    var arr1=[],arr2=[],arr3 = [];
    function arrTest1(){
        $.ajax({
            type:"post",
            async:false,
            url:"<?php echo U(GROUP_NAME.'/Main/orders');?>",
            data:{},
            dataType:"json",
            success:function(result){
                //alert(result.date.length);
                if (result) {
                    for (var i = 0; i < result.date.length; i++) {
                        arr1.push(result.ordernum[i]);
                        arr2.push(result.date[i]);
                        arr3.push(result.vouchernum[i]);
                    }
                }
            }
        })
        return arr1,arr2,arr3;
    }
    console.log(arr3);
    console.log(arr1);
    arrTest1();
    option = {
        title: {
            text: '近7天订单情况'
        },
        tooltip: {
            trigger: 'axis'
        },
        legend: {
            data:['借款申请订单数','放款订单数']
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        toolbox: {
            feature: {
                saveAsImage: {}
            }
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: arr2
        },
        yAxis: {
            type: 'value'
        },
        series: [

            {
                name:'借款申请订单数',
                type:'line',
                stack: '总量',
                data:arr1
            },
            {
                name:'放款订单数',
                type:'line',
                stack: '总量',
                data:arr3
            }
        ]
    };
    ;
    if (option && typeof option === "object") {
        myChart.setOption(option, true);
    }
</script>

</div>
      </div>
      
    
        <div class="layui-footer">
		 <div id="footer">
			  <ul>
            <h1>源码分享：<font color="red" size="3">站长源码库（zzmaku.com）</font></b></h1> 
			  </ul>
		 </div>
	</div>

      </div>
      <script>
      //JavaScript代码区域
      layui.use('element', function(){
        var element = layui.element;
      });
      </script>
      </body>
      
      <script>
      //JavaScript代码区域
      layui.use('element', function(){
        var element = layui.element;
        
      });
      </script>
</body>

</html>