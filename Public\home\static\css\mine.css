.wd-head
{
	color: rgb(39, 37, 40);
	background: linear-gradient(180deg, rgb(255,225,108) 0%, rgb(252,212,54) 100%);
	position: relative;
}
.avg-box li
{
	
	margin-top: 10px;
	line-height: 40px;
	font-size: 17px;
}

.left_li_txt
{
	float: left;
}

.tx-title
{
	font-size: 18px;
	background: #ffffff;
	padding: 5px 0;
}
.tx-title .point
{
	background: #f04a57;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin: 10px;
}

.menu-list,
.system-list
{
	background: #ffffff;
	padding: 5px;
	margin: 5px 0 0;
	line-height: 40px;
	color: #ad8f6d;
	font-size: 16px;
}

.menu-list a,
.system-list a
{
	color: #2f2f2f;
}

.menu-icon
{
	float: left;
	width: 30px;
	margin: 5px 5px 0 0;
}

.mine_top
{
	width: 80%;
	margin: 0 auto;
	background: #ffffff;
	border-radius: 5px;
	padding: 20px 0 0;
	position: absolute;
	right: 0;
	left: 0;
	bottom: 20px;
	box-shadow: 0px 1px 9px 0px #c7c7c794;
}

.mine_top_bg
{
	width: 90%;
	background: rgba(255, 255, 255, 0.5);
	height: 130px;
	margin: 70px auto 0;
	border-top-right-radius: 5px;
	border-top-left-radius: 5px;
}

.mine_top_div
{
    height: 40px;
    border-top-right-radius: 5px;
    border-top-left-radius: 5px;
    background: rgb(255,255,255);
    
    position: absolute;
    width: 100%;
    bottom: 0;
}


.coupon_box
{
	width: 95%;
	margin: 20px auto;
	color: #ffffff;
}

.coupon_list
{
	width: 100%;
	background: url("../image/coupon_list_bg.png") no-repeat center center;
	background-size: 100%;
	position: relative;
}

.coupon_list_top
{
	display: flex;
	align-items: center;
	align-content: center;
	height: 75%;
	font-size: 3rem;
}

.coupon_list_l,.coupon_list_r
{
	width: 50%;
	padding: 20px;
}

.coupon_list_r
{
	text-align: right;
	font-size: 1.6rem;
}

.coupon_num
{

	font-size: 4rem;
}

.coupon_list_bottom
{
	position: absolute;
	width: 100%;
	padding: 13px;
	bottom: 0;
	text-align: center;
	letter-spacing: 1.5px;
}