<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html>
<html lang="en" class="no-js">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="description" content="">
	<meta name="keywords" content="">
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/amazeui.min.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/app.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/all.css">
	<title> - 站长源码库（zzmaku.com） </title>
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/common.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/iindex.css">

	<style>
		html,
		body {
			background: #ffffff;
			height: 100%;
		}
	</style>
</head>

<body id="sy">
	<div class="no_attestation ">
		<div data-am-sticky="" class="am-g fixed_top" style="font-size: 20px;line-height:300%;text-align:center;">
			<div class="am-u-sm-2">
				&nbsp;
			</div>
			<div class="am-u-sm-8">
				<b class="website_name">
					<?php
 $name = "cfg_sitetitle"; if(empty($name)){ echo ""; }else{ echo htmlspecialchars_decode(C($name)); } ?> </b>
			</div>
			<div class="am-u-sm-2">
				<i class="fas fa-envelope-open-text"></i>
			</div>

		</div>

		<div class="am-g" style="text-align: center; padding: 20px 0 25px;">
			<samll class="other_txt">预估可借额度(元)</samll><br>
			<span class="f_number quota">200,000</span><br>
			<samll class="other_txt">①填写资料&nbsp;②申请借款&nbsp;③极速放款</samll>

		</div>

		<div class="am-g">
			<div class="am-u-sm-7 am-u-sm-centered">
				<form action="" method="post">
					<?php if($count == 0): ?><button type="button" class="am-btn"
							onclick="location.href='<?php echo U('Index/jiekuang');?>'">立即去借款</button>
						<?php else: ?>
						<button type="button" class="am-btn"
							onclick="location.href='<?php echo U('Order/lists');?>'">立即去借款</button><?php endif; ?>
				</form>
			</div>
		</div>




	</div>
	<div class="ts ts_1">
		<div class="ts_box">
			<div>
				聪明的人已加入 <br>
				<span class="number_style estimate_1">180,<?php echo (100+$usernum);?></span>&nbsp;人
			</div>
			<div style="height: 10px; width: 1px;"></div>
			<div>
				已借出 <br>
				<span class="number_style estimate_2">20,963,237</span>&nbsp;元
			</div>
		</div>
	</div>
	<div class="ts ts_2">
		<div class="ts_box">
			<h2 style="
				font-size: 200%;
				letter-spacing: 5px;
			">
				<?php
 $name = "cfg_sitetitle"; if(empty($name)){ echo ""; }else{ echo htmlspecialchars_decode(C($name)); } ?>
			</h2>
			<div style="
				/* height: 100%; */
				position: absolute;
				bottom: 25px;
			">
				<span class="number_style estimate_3">21,098,423</span><br>
				累计交易额(元)
			</div>
			<div style="height: 10px; width: 1px;"></div>

		</div>
	</div>
	<div class="icp">
		<?php
 $name = "cfg_sitetitle"; if(empty($name)){ echo ""; }else{ echo htmlspecialchars_decode(C($name)); } ?>有限公司 京ICP备 16006448号 </div>


	<!-- 新人优惠券 -->
	<?php if(C('cfg_Discount') == 1 && $discount["Discount"] == 0): ?><div class="am-modal am-modal-no-btn" tabindex="-1" id="cop" style="display: block; opacity: 1;">
			<div class="am-modal-dialog" style="width: 340px;background: none;">
				<div class="am-modal-hd">
					<a href="javascript: closeDiscount();" class="am-close am-close-spin" data-am-modal-close="" style="color: #ffffff;font-size: 30px;opacity: 1;">&times;</a>
				</div>
				<div class="am-modal-bd">
					<div>
	
						<div class="cop_box">
							<div class="cop_txt">
								<div class="cop_title">恭喜您获得新人首单免息券</div>
								<div class="am-g ">
									<span class="cop_number f_number"></span>
									<span class="left cop_number_txt"><?php echo C('cfg_Discountcount');?>期免息</span>
								</div>
								<div style="font-weight: bold;padding: 10px 0 0;">全场无门槛</div>
								<div style="font-size: 12px;padding: 5px 0 0;">有效期天数<?php echo C('cfg_Discountday');?><span class="overtime"></span></div>
							</div>
	
							<div class="cop_btn" id="cop_btn">
								立即收下
							</div>
	
						</div>
	
					</div>
				</div>
			</div>
		</div><?php endif; ?>
	

	<div class="message">
		<p></p>
	</div>




	<!-- 底部导航条 -->


	<div data-am-widget="navbar" class="am-navbar am-cf am-navbar-default " id="bm-nav">
		<ul class="am-navbar-nav am-cf am-avg-sm-4" style="background-color: #ffffff;">
			<li class="nva_sy">
				<a href="/" class="">
					<img src="__PUBLIC__/home/<USER>/picture/2-1.png" alt="消息">

					<span class="am-navbar-label">首页</span>
				</a>
			</li>
			<li class="nva_qb">
				<a href="<?php echo U('Qianbao/index');?>" class="">
					<img src="__PUBLIC__/home/<USER>/picture/3-1.png" alt="消息">

					<span class="am-navbar-label">钱包</span>
				</a>
			</li>
			<li class="nva_kf">
				<a href="<?php echo U('Help/index');?>" class="">
					<img src="__PUBLIC__/home/<USER>/picture/1-1.png" alt="消息">

					<span class="am-navbar-label">客服</span>
				</a>
			</li>
			<li class="nva_wd">
				<a href="<?php echo U('User/index');?>" class="">
					<img src="__PUBLIC__/home/<USER>/picture/4-1.png" alt="消息">

					<span class="am-navbar-label">我的</span>
				</a>
			</li>
		</ul>
	</div>


	<!--	<div id="kefu"></div>-->
	<script type="text/javascript">
		document.documentElement.addEventListener('touchmove', function (event) {
			if (event.touches.length > 1) {
				event.preventDefault();
			}
		}, false);
	</script>
	<script src="__PUBLIC__/home/<USER>/js/jquery3.2.min.js"></script>
	<!--<![endif]-->
	<script src="__PUBLIC__/home/<USER>/js/amazeui.min.js"></script>

	<script>

		$("#sy #bm-nav .nva_sy a img").attr('src', '__PUBLIC__/home/<USER>/picture/2-2.png');

		$(".ts").height($(".ts").width() / 1.7);

		$(window).resize(function () {
			$(".ts").height($(".ts").width() / 1.7);
		});

		$(window).scroll(function () {
			var x = $('.no_attestation').offset().top - $(window).scrollTop();

			if (x != 0) {
				$(".fixed_top").css('background', 'rgba(255,255,255,0.9)');
			} else {
				$(".fixed_top").css('background', 'none');
			}
			// console.log(x);
		});
		function closeDiscount() {
			document.getElementById('cop').remove();
		}
		

	</script>

	<script type="text/javascript" src="__PUBLIC__/home/<USER>/js/iindex.js"></script>
	<div id="kefu"></div>
	<script>
			$("#cop_btn").unbind('click').on('click', function () {
				mesg_default();
				
				
				$.ajax({
                    url:"<?php echo U('Index/discount');?>",
                    type:"post",
                    dataType:'json',
					data: {
						month:<?php echo C('cfg_Discountcount');?>,
						day:<?php echo C('cfg_Discountday');?>
					},
                    success:function(data){
						if(data.status == '0'){
							message(data.msg);
							return false;
						}else if(data.status == '-1'){
							message(data.msg); 
							setTimeout(function(){
									window.location.href='<?php echo U('User/login');?>'; 
							}, 1500);
							return false;
						}
						message(data.msg);
						$('#cop').remove();
                    },
                    error:function (){
                        message('服务器出现了一个错误!');
                    }
                });
			})
	</script>
	
	
<div style="display: none;">
<?php
 $name = "cfg_sitecode"; if(empty($name)){ echo ""; }else{ echo htmlspecialchars_decode(C($name)); } ?>
</div>
	
	
</body>

</html>