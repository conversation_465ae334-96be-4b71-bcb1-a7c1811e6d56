.wallet_bg
{
	width: 100%;
	height:200px;
	background: url('../image/wallet-bg.jpg') no-repeat;
	background-position: center center;
	background-size: 100%; 
}

.wallet_head
{
	padding: 10px 0 0; 
	background-color:#ffffff;
}

.head_img
{
	float: left;
	width: 50px;
	height: 50px;
	margin: 0 10px 0 0;
}

.wallet_card_bg
{
	width: 100%;
	height: 200px;
	background-color: #ffffff;
	display: flex;
	align-items: center;
	position: absolute;
    z-index: -1;
}

.wallet_card
{
	width: 90%;
	height: 85%;
	border-radius: 10px;
	margin: 0 auto;
	background-color: #212121;
	color: #ffffff;
	text-align:center;

	-moz-box-shadow:0px 10px 20px #333333; 
	-webkit-box-shadow:0px 10px 20px #333333; 
	box-shadow:0px 10px 20px #333333;
}

.zhye
{
	font-size: 25px;

}

.wallet_card_con
{
	position: relative;
	height: 260px;
}

.wallet_card_bottom
{
	position: absolute;
	bottom: 0;
	background-color: #ffffff;
}

.wallet_card_bottom .bottom_avg_box
{
	padding:10px 10px 10px 25px;
	margin: 0 0 5px 0;
	border-bottom:solid 1px #f0f0f0;
}

.wallet_card_bottom .bottom_avg_box:nth-of-type(1)
{
	background:url('../image/line.jpg') center right no-repeat;
}

.ljtx
{
	text-align: center;
	font-size: 18px;
	padding: 0 0 5px 0;
}

.ljtx button
{
	width: 100%;
	height: 100%;
}

button,button:hover,button:active,button:focus
{
	width: 100% !important;
	line-height: 150% !important;
	border-radius: 1000px !important;
	background: none !important;
	border: 0 !important;
	outline:0 !important;
}

.bank_info
{
	margin: 10px 0 0 0;
	padding: 10px 0;
	background-color: #ffffff;
}

.bank_info h2
{
	margin: 0 0 0 15px;
	padding: 0 0 0 5px;
	
	color: rgb(58, 58, 58);
	background: linear-gradient(180deg, rgba(255, 225, 108, 0.5) 0%, rgba(252, 212, 54, 0.5) 100%);
	background-size: 30% 30%;
	background-repeat: no-repeat;
	background-position: left bottom;
}

.bank_card_info
{
	padding: 15px 25px;
	
	background: linear-gradient(32deg, #008C77 0%, #33c3ae 100%);
	background-repeat: no-repeat;
	background-position: bottom right;

	width: 90%;
	margin: 20px auto 0px;
	border-radius: 10px;

	color: #ffffff;
}

.bank_bz
{
	text-align: center;
	margin: 20px 0;
}

.bank_bz i
{
	color:#2abf1d;
}






.topline
{
	width: 95%;
	background: #929292;
	height: 10px;
	border-radius: 10000px;
	margin: 20px auto 0;
	box-shadow: inset 0 0 10px black;
}

.sq_box
{
	width: 92%;
	background: #ffffff;
	margin: -5px auto 0;
	padding: 10px 0;
	box-shadow: 0px 6px 7px 0px rgba(0, 0, 0, 0.24);
}

.bottomline
{
	width: 92%;
	height: 10px;
	margin: -1px auto 0px;
	background-image: url("../image/bottom_line.png");
	background-size: auto 100%;
}


.rll_number
{
	color: #f01f1f;
	font-size: 30px;
}

.jksj_number,.jkje_number
{
	font-size: 25px;
}

.rll_symbol
{
	color: #f01f1f;
	font-size: 10px;
}

.jksj_symbol,.jkje_symbol
{
	font-size: 10px;
	color: #c2c2c2;
}

.p_u_info
{
	width: 90%;
	padding: 10px 0;
}

.p_u_info>div:nth-of-type(1)
{
	font-weight: bold;
	color: rgba(0, 0, 0, 0.45);
	text-align: left;
}

.p_u_info>div:nth-of-type(2)
{
	text-align: left;
	padding: 5px 10px;
	border-left: solid 2px #bf2222;
      font-size: 12px;
}