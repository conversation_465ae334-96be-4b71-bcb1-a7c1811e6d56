<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">

<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title><?php echo ($title); ?> - <?php
 $name = "cfg_sitetitle"; if(empty($name)){ echo ""; }else{ echo htmlspecialchars_decode(C($name)); } ?>  - 站长源码库（zzmaku.com） </title>
    <link href="__PUBLIC__/main/css/layui.css" rel="stylesheet" type="text/css">
    <link href="__PUBLIC__/main/css/public.css" rel="stylesheet" type="text/css">
    <script type="text/javascript" src="__PUBLIC__/main/js/jquery.min.js"></script>
    <script type="text/javascript" src="__PUBLIC__/main/js/global.js"></script>
    <script type="text/javascript" src="__PUBLIC__/My97DatePicker/WdatePicker.js"></script>
    <script type="text/javascript" src="__PUBLIC__/main/js/jquery.tab.js"></script>
    <script type="text/javascript" src="__PUBLIC__/main/js/echarts.min.js"></script>
    <script src="__PUBLIC__/layer/layer.js"></script>
</head>
<body class="layui-layout-body">
    <div class="layui-layout layui-layout-admin">

      <script src="__PUBLIC__/layer/layui.js"></script>
<div class="layui-header">
    <a href="<?php echo U(GROUP_NAME.'/Main/index');?>" class="layui-logo"><?php echo C('cfg_sitename');?></a>
    <!-- 头部区域（可配合layui已有的水平导航） -->
    <ul class="layui-nav layui-layout-left">
       <!--
       <li class="layui-nav-item">
            <a href="javascript:;">新建</a>
            <dl class="layui-nav-child layui-anim layui-anim-upbit">
                <dd> <a href="<?php echo U(GROUP_NAME.'/Article/add');?>">文章</a></dd>
                <dd><a href="<?php echo U(GROUP_NAME.'/Article/addcat');?>">文章分类</a></dd>
                <dd> <a href="<?php echo U(GROUP_NAME.'/Admin/add');?>">管理员</a></dd>
            </dl>
        </li>
        -->
        <li class="layui-nav-item"><a href="<?php
 $name = "cfg_siteurl"; if(empty($name)){ echo ""; }else{ echo htmlspecialchars_decode(C($name)); } ?>" target="_blank">查看站点</a></li>
        <li class="layui-nav-item"><a href="<?php echo U(GROUP_NAME.'/Main/clearcache');?>">清除缓存</a></li>

    </ul>
    <ul class="layui-nav layui-layout-right">
        <li class="layui-nav-item">
            <a href="javascript:;">
                您好，<?php echo session('admin_user');?>
            </a>
          
            <dl class="layui-nav-child layui-anim layui-anim-upbit">
                <dd> <a href="<?php echo U(GROUP_NAME.'/Admin/chagemypass');?>">修改密码</a></dd>
            </dl>
        </li>
        <li class="layui-nav-item"> <a href="<?php echo U(GROUP_NAME.'/Index/logout');?>">退出</a></li>
    </ul>
</div>


      <!-- dcHead 结束 -->
        <div class="layui-side layui-bg-black">
<div class="layui-side-scroll">
	<!-- 左侧导航区域（可配合layui已有的垂直导航） -->
	<ul class="layui-nav layui-nav-tree"  lay-filter="test">
		<li class="layui-nav-item"><a href="<?php echo U(GROUP_NAME.'/Main/index');?>">管理首页</a></li>
		<li class="layui-nav-item"><a href="<?php echo U(GROUP_NAME.'/System/index');?>">系统设置</a></li>
		<li class="layui-nav-item"><a href="<?php echo U(GROUP_NAME.'/Admin/index');?>">后台管理员</a></li>
		<li class="layui-nav-item"><a href="<?php echo U('User/index');?>">用户管理</a></li>
		<li class="layui-nav-item"><a href="<?php echo U(GROUP_NAME.'/Daikuan/index');?>">借款列表</a></li>
		<li class="layui-nav-item"><a href="<?php echo U('User/qianbao');?>">钱包管理</a></li>
		<li class="layui-nav-item"><a href="<?php echo U(GROUP_NAME.'/Daikuan/contract');?>">借款合同</a></li>
		<li class="layui-nav-item"><a href="<?php echo U('Tixian/index');?>">提现管理</a></li>
		<li class="layui-nav-item"><a href="<?php echo U(GROUP_NAME.'/Bills/index');?>">还款管理</a></li>	
		<li class="layui-nav-item"><a href="<?php echo U(GROUP_NAME.'/Duanxin/addsms');?>">短信状态</a></li>
		<li class="layui-nav-item"><a href="<?php echo U(GROUP_NAME.'/Block/index');?>">自 由 块</a></li>
	<!--	<li class="layui-nav-item"><a href="<?php echo U(GROUP_NAME.'/Article/catlist');?>">文章分类</a></li> !-->
		<li class="layui-nav-item"><a href="<?php echo U(GROUP_NAME.'/Article/index');?>">常见问题</a></li>
	</ul>
  </div>
</div>  
      <div class="layui-body">
        <!-- 内容主体区域 -->
        <blockquote class="layui-elem-quote layui-text">
           <?php echo ($title); ?>
          </blockquote>
        <div style="padding: 15px;"><style>
	.tableBasic input[type=checkbox]{
		display: none;
	}
</style>

<div class="layui-table-tool">
<div class="filter">
	<form action="<?php echo U(GROUP_NAME.'/User/index');?>" method="post">
		<input name="keyword" type="text" class="inpMain" placeholder="用户名" value="<?php echo ($keyword); ?>" size="20" />
		<input name="sday" id="sday" type="text" placeholder="开始时间" class="inpMain  day1" value="<?php echo ($sday); ?>"
			onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" size="16" />

		<input name="eday" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" id="eday" type="text" placeholder="结束时间"
			class="inpMain  day1" value="<?php echo ($eday); ?>" size="16" />
		<input name="submit" class="btnGray layui-btn" type="submit" value="筛选" />
		<input type="button" id="smsAll" class="layui-btn-normal layui-btn" value="短信" />
		<input type="button" onclick="delAll()" class="layui-btn-normal layui-btn" value="删除" />
		<span><b>用户总计：<?php echo ($count); ?>人</b></span>
	</form>

</div>
</div>
<div id="list">
	<table width="100%" border="0" cellpadding="10" cellspacing="0" class="tableBasic">
		<tr>
			<th width="auto" align="center">
				<div class="checkall checkboxs">
					<input type="checkbox" name="selectAll" lay-skin="primary">
					<div class="layui-unselect layui-form-checkbox" lay-skin="primary"><i class="layui-icon layui-icon-ok"></i></div>
				</div>
			</th>
			<th width="auto" align="center">ID</th>
			<th width="auto" align="center">姓名</th>
			<th width="auto" align="center">手机号码</th>
			<th width="auto" align="center">银行卡号</th>
			

			<!--<th width="80px" align="center">钱包额度</th>-->
			<!--<th width="80px" align="center">会员级别</th>-->
			<th width="80px" align="center">状态</th>

			

			<th width="80" align="center">注册日期</th>
		<!--	<th width="100" align="center">最后登录时间</th> !-->
			<th width="auto" align="center">操作</th>
		</tr>
		<?php if(is_array($list)): $i = 0; $__LIST__ = $list;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?><tr>
				<td align="center">
					<div class="checkzi checkboxs">
						<input type="checkbox" name="chooseInfo" lay-skin="primary" value="<?php echo ($vo["id"]); ?>">
						<div class="layui-unselect layui-form-checkbox" lay-skin="primary"><i class="layui-icon layui-icon-ok"></i></div>
					</div>
				</td >
				
				<td align="center"><?php echo ($vo["id"]); ?></td>
				<td align="center"><?php echo ($vo["name"]); ?></td>
				<td align="center"><?php echo ($vo["phone"]); ?></td>
				<td align="center"><?php echo ($vo["usercard"]); ?></td>

			<!--	<td align="center"><?php echo ($vo["edu"]); ?></td> -->
			<!--	<td align="center">
					<?php if($vo['vip'] == 2) echo "中级会员"; if($vo['vip'] ==3) echo "高级会员"; if($vo['vip'] == 1) echo "普通用户"; ?>
				</td>
				-->
				<td align="center">
					<?php if($vo['status'] == 1) echo "允许登录"; if($vo['status'] == 0) echo "禁止登录"; ?>
				</td>

				

				<td align="center"><?php echo (date('Y-m-d',$vo["addtime"])); ?></td>
			<!--	<td align="center"><?php echo (date('Y-m-d',$vo["last_time"])); ?></td> !-->
				<td align="center">
					<?php if($vo['status'] == 1){ ?>
					<button class="layui-btn layui-btn-sm layui-btn-danger"><a
						href="<?php echo U(GROUP_NAME.'/User/status',array('id' => $vo['id']));?>">禁止登录</a></button>
					<?php }else{ ?>
					<button class="layui-btn layui-btn-sm layui-btn-danger"><a
						href="<?php echo U(GROUP_NAME.'/User/status',array('id' => $vo['id']));?>">允许登录</a></button>
					<?php } ?>
					<button class="layui-btn layui-btn-sm layui-btn-danger"><a
						href="<?php echo U(GROUP_NAME.'/User/userinfo',array('user' => $vo['phone']));?>">查看资料</a> </button>
					<button class="layui-btn layui-btn-sm layui-btn-normal"> <a href="javascript:changepass('<?php echo ($vo["id"]); ?>');"><i class="layui-icon"></i>修改密码</a> </button>
					<!--<button class="layui-btn layui-btn-sm layui-btn-normal"><a href="javascript:changeedu('<?php echo ($vo["id"]); ?>');"><i class="layui-icon"></i>额度管理</a> </button>-->
					<button class="layui-btn layui-btn-sm"><a
							href="javascript:del('<?php echo ($vo["phone"]); ?>','<?php echo U(GROUP_NAME.'/User/del',array('id'=>$vo['id']));?>');"><i class="layui-icon"></i>删除订单</a></button>
				<!--	<button class="layui-btn layui-btn-sm layui-btn-normal"><a href="javascript:changevip('<?php echo ($vo["id"]); ?>');"><i class="layui-icon"></i>会员等级</a> </button> !-->
				</td>
			</tr><?php endforeach; endif; else: echo "" ;endif; ?>
	</table>
</div>
<div class="clear"></div>
<div class="pager">
	<?php echo ($page); ?>
</div>
</div>
<script>
	function del(uname, jumpurl) {
		layer.confirm(
			'确定要删除用户:' + uname + '吗?',
			function () {
				window.location.href = jumpurl;
			}
		);
	}
	function changepass(uid) {
		layer.prompt({ title: '输入新密码，并确认', formType: 1 }, function (pass, index) {
			if (pass == '' || pass == null) {
				layer.msg('密码不能为空!');
			} else if (pass.length < 6) {
				layer.msg("密码长度不能小于6位!");
			} else {
				$.post(
					"<?php echo U(GROUP_NAME.'/User/changepass');?>",
					{ id: uid, pass: pass },
					function (data, state) {
						if (state != "success") {
							layer.msg("网络通讯失败!");
						} else if (data.status == 1) {
							layer.msg("密码修改成功!");
							setTimeout(function () { location.reload(); }, 1000);
							layer.close(index);
						} else {
							layer.msg(data.msg);
						}
					}
				);
			}
		});
	}








	function changeedu(uid) {
		layer.prompt({ title: '输入新额度，并确认', formType: 0 }, function (edu, index) {
			if (edu == '' || edu == null) {
				layer.msg('额度不能为空!');
			} else {
				$.post(
					"<?php echo U(GROUP_NAME.'/User/changeedu');?>",
					{ id: uid, edu: edu },
					function (data, state) {
						if (state != "success") {
							layer.msg("网络通讯失败!");
						} else if (data.status == 1) {
							layer.msg("额度修改成功!");
							setTimeout(function () { location.reload(); }, 1000);
							layer.close(index);
						} else {
							layer.msg(data.msg);
						}
					}
				);
			}
		});
	}

	function changevip(uid) {
		layer.prompt({ title: '输入数字（1：普通 ； 2：中级  ； 3：高级）', formType: 0 }, function (edu, index) {
			if (edu == '' || edu == null) {
				layer.msg('会员等级不能为空!');
			} else {
				$.post(
					"<?php echo U(GROUP_NAME.'/Index/changevip');?>",
					{ id: uid, edu: edu },
					function (data, state) {
						if (data.status != 1) {
							layer.msg(data.info);
						} else {
							layer.close(index);
							layer.msg("会员等级修改成功");
							setTimeout(function () { location.reload(); }, 1000);
						}
					}
				);
			}
		});
	}

	//暂时不用   数组删减的方法
	// Array.prototype.remove = function(val) { 
	// 	var index = this.indexOf(val); 
	// 		if (index > -1) { 
	// 			this.splice(index, 1); 
	// 		} 
	// };
	// 全选
	var checkarr = [];
	$('.checkall').on('click',function () {
		$(this).find('div').hasClass('layui-form-checked') ? $(this).find('div').removeClass('layui-form-checked') : $(this).find('div').addClass('layui-form-checked');
		if($(this).find('input').is(':checked')){
			
			$('input[name="chooseInfo"]').each(function(){
				$(this).prop("checked",false);
				$(this).nextAll('div').removeClass('layui-form-checked');

				//hj摒弃  数组删除id
				// if(checkarr.includes($(this).val())){
				// 	checkarr.remove($(this).val())
				// }
			});
			$(this).find('input').prop("checked",false).trigger('change');
		}else{
			
			$('input[name="chooseInfo"]').each(function(){
				$(this).prop("checked",true);
				$(this).nextAll('div').addClass('layui-form-checked');

				//hj摒弃  id加入数组
				// if(!checkarr.includes($(this).val())){
				// 	checkarr.push($(this).val())
				// }
			});
			$(this).find('input').prop("checked",true).trigger('change');
		}
	})

	
	
	//单选
	$('.checkzi').on('click',function () {
		$(this).find('div').hasClass('layui-form-checked') ? $(this).find('div').removeClass('layui-form-checked') : $(this).find('div').addClass('layui-form-checked');
		if($(this).find('input').is(':checked')){
			$(this).find('input').prop("checked",false).trigger('change');
			$('input[name="selectAll"]').prop("checked",false);
			$('.checkall').find('div').removeClass('layui-form-checked');
			//hj摒弃  数组删除id
			// if(checkarr.includes($(this).find('input').val())){
			// 	checkarr.remove($(this).find('input').val())
			// }
		}else{
			$(this).find('input').prop("checked",true).trigger('change');
			//hj摒弃  id加入数组
			// if(!checkarr.includes($(this).find('input').val())){
			// 	checkarr.push($(this).find('input').val())
			// }

		}
	})

	$('input[type="checkbox"]').on('change',function (){
		checkarr = [];
		$('input[name="chooseInfo"]:checked').each(function(){
			checkarr.push($(this).val());
		});
		
	})



	
	$('#smsAll').on('click',function() {
		console.log(checkarr)
		if(checkarr.length){
			layer.open({
            title: '是否发送短信', 
			btn: ['确定'],
			area:['550px','250px'],
			content: '<div class="layui-form-item">' +
                '<label class="layui-form-mid">请选择短信内容</label>' +
                '<div class="layui-input-block">' +
                '<select name="range" id="range" lay-filter="range" onchange="smsselect(this)">' +
				'<option value="">请选择</option>' +
				'<?php if(is_array($sms)): $i = 0; $__LIST__ = $sms;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>' +
                '<option value="<?php echo ($vo["content"]); ?>"><?php echo ($vo["content"]); ?></option>' +
				'<?php endforeach; endif; else: echo "" ;endif; ?>' +
                '</select>' +
				'</div>' +
				'</div>' +
				'<div class="layui-form-item">'+
				'<label class="layui-form-mid" style="margin-left: 44px;">短信内容</label>' +
				'<input type="text" id="smscontent" required lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input" style="width: 67%;">  ' +
				'</div>',
				yes: function (index) {
					if($('#smscontent').val().length>1){
						if(checkarr.length>0){
							$.post(
								"<?php echo U(GROUP_NAME.'/User/smsall');?>",
								{ id: checkarr, edu: $('#smscontent').val() },
								function (data, state) {
									if (data.status != 1) {
										layer.msg(data.info);
									} else {
										layer.close(index);
										layer.msg("短信发送成功");
										setTimeout(function () { location.reload(); }, 1000);
									}
								}
							);
						}
					}else{
						layer.msg("短信内容不能为空!", {icon: 5, anim: 6});
					}
                }
        	});
		}else{
			layer.msg('请您选择一个用户!');
		}
	})


	function smsselect(obj){
		$('#smscontent').val($(obj).val());
	}
	function delAll(){
		if(checkarr.length){
			layer.confirm('是否删除用户？', {
			  btn: ['确定','取消'] //按钮
			},function(){
				$.post(
					"<?php echo U(GROUP_NAME.'/User/delall');?>",
					{ id: checkarr},
					function (data, state) {
						if (data.status != 1) {
							layer.msg(data.info);
						} else {
							layer.msg("删除成功");
							setTimeout(function () { location.reload(); }, 1000);
						}
					}
				);
			});
		}else{
			layer.msg('请您选择一个用户!');
		}
	}
	
	
</script></div>
      </div>
      
    
        <div class="layui-footer">
		 <div id="footer">
			  <ul>
            <h1><font color="red" size="3">唯一联系方式QQ【18108197】_TG【Kong_Money】</font></b></h1> 
			  </ul>
		 </div>
	</div>

      </div>
      <script>
      //JavaScript代码区域
      layui.use('element', function(){
        var element = layui.element;
      });
      </script>
      </body>
      
      <script>
      //JavaScript代码区域
      layui.use('element', function(){
        var element = layui.element;
        
      });
      </script>
</body>

</html>