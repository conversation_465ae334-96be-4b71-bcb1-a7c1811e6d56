<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">

<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title><?php echo ($title); ?> - <?php
 $name = "cfg_sitetitle"; if(empty($name)){ echo ""; }else{ echo htmlspecialchars_decode(C($name)); } ?>  - 站长源码库（zzmaku.com） </title>
    <link href="__PUBLIC__/main/css/layui.css" rel="stylesheet" type="text/css">
    <link href="__PUBLIC__/main/css/public.css" rel="stylesheet" type="text/css">
    <script type="text/javascript" src="__PUBLIC__/main/js/jquery.min.js"></script>
    <script type="text/javascript" src="__PUBLIC__/main/js/global.js"></script>
    <script type="text/javascript" src="__PUBLIC__/My97DatePicker/WdatePicker.js"></script>
    <script type="text/javascript" src="__PUBLIC__/main/js/jquery.tab.js"></script>
    <script type="text/javascript" src="__PUBLIC__/main/js/echarts.min.js"></script>
    <script src="__PUBLIC__/layer/layer.js"></script>
</head>
<body class="layui-layout-body">
    <div class="layui-layout layui-layout-admin">

      <script src="__PUBLIC__/layer/layui.js"></script>
<div class="layui-header">
    <a href="<?php echo U(GROUP_NAME.'/Main/index');?>" class="layui-logo"><?php echo C('cfg_sitename');?></a>
    <!-- 头部区域（可配合layui已有的水平导航） -->
    <ul class="layui-nav layui-layout-left">
       <!--
       <li class="layui-nav-item">
            <a href="javascript:;">新建</a>
            <dl class="layui-nav-child layui-anim layui-anim-upbit">
                <dd> <a href="<?php echo U(GROUP_NAME.'/Article/add');?>">文章</a></dd>
                <dd><a href="<?php echo U(GROUP_NAME.'/Article/addcat');?>">文章分类</a></dd>
                <dd> <a href="<?php echo U(GROUP_NAME.'/Admin/add');?>">管理员</a></dd>
            </dl>
        </li>
        -->
        <li class="layui-nav-item"><a href="<?php
 $name = "cfg_siteurl"; if(empty($name)){ echo ""; }else{ echo htmlspecialchars_decode(C($name)); } ?>" target="_blank">查看站点</a></li>
        <li class="layui-nav-item"><a href="<?php echo U(GROUP_NAME.'/Main/clearcache');?>">清除缓存</a></li>

    </ul>
    <ul class="layui-nav layui-layout-right">
        <li class="layui-nav-item">
            <a href="javascript:;">
                您好，<?php echo session('admin_user');?>
            </a>
          
            <dl class="layui-nav-child layui-anim layui-anim-upbit">
                <dd> <a href="<?php echo U(GROUP_NAME.'/Admin/chagemypass');?>">修改密码</a></dd>
            </dl>
        </li>
        <li class="layui-nav-item"> <a href="<?php echo U(GROUP_NAME.'/Index/logout');?>">退出</a></li>
    </ul>
</div>


      <!-- dcHead 结束 -->
        <div class="layui-side layui-bg-black">
<div class="layui-side-scroll">
	<!-- 左侧导航区域（可配合layui已有的垂直导航） -->
	<ul class="layui-nav layui-nav-tree"  lay-filter="test">
		<li class="layui-nav-item"><a href="<?php echo U(GROUP_NAME.'/Main/index');?>">管理首页</a></li>
		<li class="layui-nav-item"><a href="<?php echo U(GROUP_NAME.'/System/index');?>">系统设置</a></li>
		<li class="layui-nav-item"><a href="<?php echo U(GROUP_NAME.'/Admin/index');?>">后台管理员</a></li>
		<li class="layui-nav-item"><a href="<?php echo U('User/index');?>">用户管理</a></li>
		<li class="layui-nav-item"><a href="<?php echo U(GROUP_NAME.'/Daikuan/index');?>">借款列表</a></li>
		<li class="layui-nav-item"><a href="<?php echo U('User/qianbao');?>">钱包管理</a></li>
		<li class="layui-nav-item"><a href="<?php echo U(GROUP_NAME.'/Daikuan/contract');?>">借款合同</a></li>
		<li class="layui-nav-item"><a href="<?php echo U('Tixian/index');?>">提现管理</a></li>
		<li class="layui-nav-item"><a href="<?php echo U(GROUP_NAME.'/Bills/index');?>">还款管理</a></li>	
		<li class="layui-nav-item"><a href="<?php echo U(GROUP_NAME.'/Duanxin/addsms');?>">短信状态</a></li>
		<li class="layui-nav-item"><a href="<?php echo U(GROUP_NAME.'/Block/index');?>">自 由 块</a></li>
	<!--	<li class="layui-nav-item"><a href="<?php echo U(GROUP_NAME.'/Article/catlist');?>">文章分类</a></li> !-->
		<li class="layui-nav-item"><a href="<?php echo U(GROUP_NAME.'/Article/index');?>">常见问题</a></li>
	</ul>
  </div>
</div>  
      <div class="layui-body">
        <!-- 内容主体区域 -->
        <blockquote class="layui-elem-quote layui-text">
           <?php echo ($title); ?>
          </blockquote>
        <div style="padding: 15px;">
<div class="layui-table-tool">
<h3  class="layui-table-tool-self">

    <a href="<?php echo U(GROUP_NAME.'/Block/add');?>" class="actionBtn add  layui-btn ">

        添加自由块
    </a>
</h3>
</div>
<table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableBasic">

    <tr>

        <th width="30">ID</th>

        <th width="300" align="left">名称</th>

        <th width="140" align="left">添加时间</th>

        <th width="100" align="center">操作</th>

    </tr>

    <?php if(is_array($data)): foreach($data as $key=>$vo): ?><tr>

            <td align="center"><?php echo ($vo["id"]); ?></td>

            <td align="left"><?php echo ($vo["name"]); ?></td>

            <td><?php echo (date("Y/m/d",$vo["addtime"])); ?></td>

            <td align="center">

                <button class="layui-btn layui-btn-sm layui-btn-normal"><a href="<?php echo U(GROUP_NAME.'/Block/edit',array('id'=>$vo['id']));?>"><i class="layui-icon"></i>编辑</a>
                </button>

                <button class="layui-btn layui-btn-sm layui-btn-normal"><a
                        href="javascript:delCat('<?php echo ($vo["name"]); ?>','<?php echo U(GROUP_NAME.'/Block/del',array('id'=>$vo['id']));?>');"><i class="layui-icon"></i>删除</a></button>

            </td>

        </tr><?php endforeach; endif; ?>

</table>

<script>

    function delCat(name, jumpurl) {

        layer.confirm(

            '确定要删除自由块:[' + name + ']吗?',

            function () {

                window.location.href = jumpurl;

            }

        );

    }

</script></div>
      </div>
      
    
        <div class="layui-footer">
		 <div id="footer">
			  <ul>
            <h1>源码分享：<font color="red" size="3">站长源码库（zzmaku.com）</font></b></h1> 
			  </ul>
		 </div>
	</div>

      </div>
      <script>
      //JavaScript代码区域
      layui.use('element', function(){
        var element = layui.element;
      });
      </script>
      </body>
      
      <script>
      //JavaScript代码区域
      layui.use('element', function(){
        var element = layui.element;
        
      });
      </script>
</body>

</html>