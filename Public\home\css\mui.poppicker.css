.mui-poppicker {
	position: fixed;
	left: 0px;
	width: 100%;
	z-index: 999;
	background-color: #eee;
	border-top: solid 1px #ccc;
	box-shadow: 0px -5px 7px 0px rgba(0, 0, 0, 0.1);
	-webkit-transition: .3s;
	bottom: 0px;
	-webkit-transform: translateY(300px);
}
.mui-poppicker.mui-active {
	-webkit-transform: translateY(0px);
}
.mui-android-5-1 .mui-poppicker {
	bottom: -300px;
	-webkit-transition-property: bottom;
	-webkit-transform: none;
}
.mui-android-5-1 .mui-poppicker.mui-active {
	bottom: 0px;
	-webkit-transition-property: bottom;
	-webkit-transform: none;
}
.mui-poppicker-header {
	padding: 6px;
	font-size: 14px;
	color: #888;
}
.mui-poppicker-header .mui-btn {
	font-size: 12px;
	padding: 5px 10px;
}
.mui-poppicker-btn-cancel {
	float: left;
}
.mui-poppicker-btn-ok {
	float: right;
}
.mui-poppicker-clear {
	clear: both;
	height: 0px;
	line-height: 0px;
	font-size: 0px;
	overflow: hidden;
}
.mui-poppicker-body {
	position: relative;
	width: 100%;
	height: 200px;
	border-top: solid 1px #ddd;
	/*-webkit-perspective: 1200px;
	perspective: 1200px;
	-webkit-transform-style: preserve-3d;
	transform-style: preserve-3d;*/
}
.mui-poppicker-body .mui-picker {
	width: 100%;
	height: 100%;
	margin: 0px;
	border: none;
	float: left;
}