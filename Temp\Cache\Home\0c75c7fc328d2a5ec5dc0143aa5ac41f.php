<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html>
<html lang="en" class="no-js">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="description" content="">
	<meta name="keywords" content="">
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport">
	<meta name="apple-mobile-web-app-status-bar-style" content="black">
	<meta name="apple-mobile-web-app-title" content="Amaze UI">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/amazeui.min.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/app.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/all.css">
	<title>登录 - 站长源码库（zzmaku.com） </title>
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/login--1.css">
</head>
<body>
	<!--[if lte IE 9]>
<p class="browsehappy">你正在使用<strong>过时</strong>的浏览器，Amaze UI 暂不支持。 请 <a
  href="http://browsehappy.com/" target="_blank">升级浏览器</a>
  以获得更好的体验！</p>
<![endif]-->

	<div class="head-bg">

		<div class="websitename_box">
			<div class="websitename">
				<div><?php
 $name = "cfg_sitetitle"; if(empty($name)){ echo ""; }else{ echo htmlspecialchars_decode(C($name)); } ?></div>
			</div>
		</div>
		<div class="head_bottom_box">
			<div class="head_bottom">
				<div class="head_login">
					登录
				</div>
				<div class="head_register" onclick="javascript:window.location.href='<?php echo U('User/signup');?>'">
					注册
				</div>
			</div>
		</div>
		<div class="white_q"></div>
	</div>
	<div class="login">
		<div class="am-g">
			<div class="form-box am-u-sm-11 am-u-sm-centered">
				<form class="am-form am-form-horizontal">
					<div class="am-form-group input-box">
						<label class="am-u-sm-1" style="padding: 0;">
							<img class="menu-icon" src="__PUBLIC__/home/<USER>/picture/phone.png" alt="">
						</label>
						<div class="am-u-sm-11 f_number" style="padding: 0;">
							<input type="number" id="account" placeholder="输入你的手机号">
						</div>
					</div>

					<div class="am-form-group input-box">
						<label class="am-u-sm-1" style="padding: 0;">
							<img class="menu-icon" src="__PUBLIC__/home/<USER>/picture/pwd.png" alt="">
						</label>
						<div class="am-u-sm-11 f_number" style="padding: 0;">
							<input type="password" id="password" placeholder="输入你设置的密码">
						</div>
					</div>

					<div class="am-form-group">
						<small class="am-u-sm-6" style="text-align: left; color: rgb(255, 209, 27); opacity: 0;">
							注&nbsp;册
						</small>
						<small class="am-u-sm-6" style="text-align: right;"
							onclick="javascript:window.location.href='<?php echo U('User/backpwd');?>'">
							<u><font color="red">忘记密码?</font></u>
						</small>
					</div>

					<div style="height: 30px;"></div>

					<div class="am-form-group">
						<div class="">
							<button type="button" class="am-btn" id="login-button">登录</button>
						</div>
					</div>

					<div class="am-form-group">
						<div class="reg_link"
							onclick="javascript:window.location.href='<?php echo U('User/signup');?>'">
							注册
						</div>
					</div>

					<div style="text-align: center;">
						
						<span style="color: #ffd328;"
							onclick="javascript:window.location.href='<?php echo U('Index/index');?>'">跳过，返回首页</span>
					</div>
				</form>
			</div>
		</div>
	</div>
	<div class="message">
		<p></p>
	</div>
	<script type="text/javascript">
		document.documentElement.addEventListener('touchmove', function (event) {
			if (event.touches.length > 1) {
				event.preventDefault();
			}
		}, false);
	</script>

	<script src="__PUBLIC__/home/<USER>/js/jquery3.2.min.js"></script>
	<!--<![endif]-->
	<script src="__PUBLIC__/home/<USER>/js/amazeui.min.js"></script>
	<!--<div id="kefu"></div>-->
	<script type="text/javascript" src="__PUBLIC__/home/<USER>/js/login.js"></script>
	
	
	  <div style="display: none;">
    <?php
 $name = "cfg_sitecode"; if(empty($name)){ echo ""; }else{ echo htmlspecialchars_decode(C($name)); } ?>
  </div>

</body>

</html>